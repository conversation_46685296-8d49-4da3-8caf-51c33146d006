{"collectionName": "components_ocpi_additional_geo_locations", "info": {"displayName": "AdditionalGeoLocation", "description": "OCPI 2.2.1 AdditionalGeoLocation object"}, "options": {}, "attributes": {"latitude": {"type": "string", "required": true, "comment": "Latitude of the point in decimal degree."}, "longitude": {"type": "string", "required": true, "comment": "Longitude of the point in decimal degree."}, "name": {"type": "component", "repeatable": true, "component": "ocpi.display-text", "comment": "Name of the point in multiple languages."}, "type": {"type": "enumeration", "enum": ["ENTRANCE", "EXIT", "PARKING_SPOT", "LOCATION"], "required": true, "comment": "Type of additional geo location."}}}