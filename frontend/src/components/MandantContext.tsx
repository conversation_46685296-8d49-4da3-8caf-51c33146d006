"use client";

import React, {
	createContext,
	useContext,
	useState,
	useEffect,
	type ReactNode,
} from "react";
import {
	type MandantData,
	type MandantResponse,
	apiClient,
	mandantsApi,
} from "../services/api";
import { useAuth } from "./AuthContext";

// Mandanten-Typ für den Kontext
export type Mandant = MandantData;

// Kontext-Typ
interface MandantContextType {
	activeMandant: Mandant | null;
	setActiveMandant: (mandant: Mandant) => void;
	availableMandants: Mandant[];
	isLoading: boolean;
	error: string | null;
	fetchMandants: () => Promise<void>;
	getAllowedMandantIds: () => number[]; // Gibt alle zulässigen Mandanten-IDs zurück (inkl. Kinder)
}

// Erstelle den Kontext
const MandantContext = createContext<MandantContextType | undefined>(undefined);

// Provider-Komponente
export function MandantProvider({ children }: { children: ReactNode }) {
	const { user, token, loading } = useAuth();
	const [activeMandant, setActiveMandant] = useState<Mandant | null>(null);
	const [availableMandants, setAvailableMandants] = useState<Mandant[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Abrufen der verfügbaren Mandanten für den aktuellen Benutzer
	const fetchMandants = async () => {
		if (loading || !user || !token) {
			setIsLoading(false);
			return;
		}

		setIsLoading(true);
		setError(null);

		try {
			// Verwende die mandantsApi aus dem services/api Modul
			const response = await mandantsApi.getAll();

			if (response?.data) {
				const mandants = response.data;
				setAvailableMandants(mandants);

				// Versuche den gespeicherten Mandanten zu laden oder den ersten zu nehmen
				const savedMandantId = localStorage.getItem("activeMandantId");

				if (!activeMandant) {
					if (savedMandantId && mandants.length > 0) {
						const savedMandant = mandants.find(
							(m) => m.id.toString() === savedMandantId,
						);
						if (savedMandant) {
							setActiveMandant(savedMandant);
						} else if (mandants.length > 0) {
							// Wenn gespeicherter Mandant nicht gefunden, ersten nehmen
							if (mandants[0]) {
								setActiveMandant(mandants[0]);
								localStorage.setItem(
									"activeMandantId",
									mandants[0].id.toString(),
								);
							}
						}
					} else if (mandants.length > 0) {
						// Wenn kein Mandant gespeichert ist, ersten nehmen
						if (mandants[0]) {
							setActiveMandant(mandants[0]);
							localStorage.setItem(
								"activeMandantId",
								mandants[0].id.toString(),
							);
						}
					}
				}
			}
		} catch (err) {
			console.error("Fehler beim Abrufen der Mandanten:", err);
			setError(
				"Fehler beim Laden der Mandanten. Bitte versuchen Sie es später erneut.",
			);
		} finally {
			setIsLoading(false);
		}
	};

	// Beim ersten Laden und bei Änderungen des Benutzers Mandanten abrufen
	useEffect(() => {
		if (user && token) {
			fetchMandants();
		}
	}, [user, token]);

	// Speichere die Auswahl im localStorage, wenn sich der aktive Mandant ändert
	useEffect(() => {
		if (activeMandant) {
			localStorage.setItem("activeMandantId", activeMandant.id.toString());
		}
	}, [activeMandant]);

	// Funktion zum Ermitteln aller erlaubten Mandanten-IDs (inkl. Kinder)
	const getAllowedMandantIds = (): number[] => {
		if (!activeMandant) return [];

		// Sammle aktive Mandant und alle seine Kinder
		const ids: number[] = [activeMandant.id];

		// Funktion zum rekursiven Sammeln aller Kind-Mandanten
		const collectChildIds = (mandant: Mandant) => {
			// Füge Kinder hinzu, falls vorhanden
			if (mandant.children?.data && mandant.children.data.length > 0) {
				for (const child of mandant.children.data) {
					ids.push(child.id);

					// Suche diesen Kind-Mandanten in der Liste, um dessen Kinder zu prüfen
					const childMandant = availableMandants.find((m) => m.id === child.id);
					if (childMandant) {
						collectChildIds(childMandant);
					}
				}
			}
		};

		// Starte Sammlung mit dem aktiven Mandanten
		collectChildIds(activeMandant);

		return ids;
	};

	return (
		<MandantContext.Provider
			value={{
				activeMandant,
				setActiveMandant,
				availableMandants,
				isLoading,
				error,
				fetchMandants,
				getAllowedMandantIds,
			}}
		>
			{children}
		</MandantContext.Provider>
	);
}

// Hook für einfacheren Zugriff auf den Kontext
export function useMandant() {
	const context = useContext(MandantContext);
	if (context === undefined) {
		throw new Error(
			"useMandant muss innerhalb eines MandantProviders verwendet werden",
		);
	}
	return context;
}
