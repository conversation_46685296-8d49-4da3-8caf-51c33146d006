"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function HomePage() {
	const router = useRouter();

	useEffect(() => {
		// Prü<PERSON>, ob der Benutzer eingeloggt ist
		// Hier würde normaler Code stehen, der den Login-Status prüft
		const isLoggedIn = false; // Beispiel - muss durch Ihre eigene Logik ersetzt werden

		// Verwende einen kurzen Timeout, um sicherzustellen, dass die Navigation funktioniert
		setTimeout(() => {
			if (isLoggedIn) {
				router.push("/dashboard");
			} else {
				router.push("/login");
			}
		}, 100);
	}, [router]);

	// Zeigt nur einen Ladebildschirm, während die Weiterleitung erfolgt
	return (
		<div className="flex h-screen items-center justify-center">
			<div className="text-center">
				<h1 className="mb-4 font-bold text-2xl text-gray-800">
					Payment Terminal Hub
				</h1>
				<p className="text-gray-600">Wird geladen...</p>
			</div>
		</div>
	);
}
