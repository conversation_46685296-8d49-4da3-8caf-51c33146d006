'use strict';

/**
 * OCPI Token Validator Service
 * Bietet Funktionen zur Validierung von OCPI-Tokens
 */
module.exports = {
    /**
     * Extrahiert und validiert ein Token aus dem Authorization-Header
     *
     * @param {string} authHeader - Der Authorization-Header
     * @returns {string|null} - Das extrahierte Token oder null, wenn kein gültiges Token gefunden wurde
     */
    extractToken(authHeader) {
        if (!authHeader) return null;

        const tokenPrefix = "Token ";
        if (!authHeader.startsWith(tokenPrefix)) return null;

        return authHeader.substring(tokenPrefix.length);
    },

    /**
     * Validiert ein Token gegen InitialSecret oder ReceivingSecret
     *
     * @param {string} token - Das zu validierende Token
     * @param {string} tokenType - Der Typ des Tokens ('initial', 'receiving', 'any')
     * @returns {Promise<Object|null>} - Die gefundene OCPI-Connection oder null, wenn keine gefunden wurde
     */
    async validateToken(token, tokenType = 'any') {
        if (!token) return null;

        // Logger-Service referenzieren
        const logger = strapi.service('api::ocpi-log.ocpi-logger');

        // Suchkriterien basierend auf dem Token-Typ
        let whereCondition = {};

        switch (tokenType) {
            case 'initial':
                whereCondition = { initialSecret: token };
                break;
            case 'receiving':
                whereCondition = { receivingSecret: token };
                break;
            case 'send':
                whereCondition = { sendSecret: token };
                break;
            case 'any':
            default:
                whereCondition = {
                    $or: [
                        { initialSecret: token },
                        { receivingSecret: token },
                        { sendSecret: token }
                    ]
                };
                break;
        }

        try {
            // OCPI Connection mit dem Token suchen
            const connection = await strapi.documents('api::ocpi-connection.ocpi-connection').findFirst({
                filters: whereCondition,
                populate: {
                    mandants: true
                }
            });

            if (!connection) {
                await logger.warn(`Keine OCPI-Connection mit dem Token (Typ: ${tokenType}) gefunden`, {
                    endpoint: 'token-validation'
                });
                return null;
            }

            await logger.info(`Token (Typ: ${tokenType}) erfolgreich validiert für Connection ${connection.name}`, {
                endpoint: 'token-validation',
                mandantId: connection.mandants[0]?.id
            });

            return connection;
        } catch (error) {
            await logger.error(`Fehler bei der Token-Validierung: ${error.message}`, {
                endpoint: 'token-validation'
            });
            return null;
        }
    },

    /**
     * Validiert ein Token aus dem Authorization-Header eines HTTP-Requests
     *
     * @param {Object} ctx - Der Strapi-Kontext mit dem HTTP-Request
     * @param {string} tokenType - Der Typ des Tokens ('initial', 'receiving', 'any')
     * @returns {Promise<Object>} - Ein Objekt mit dem Validierungsergebnis und ggf. der Connection
     */
    async validateRequestToken(ctx, tokenType = 'any') {
        // Token aus dem Authorization-Header extrahieren
        const authHeader = ctx.request.headers.authorization || '';
        const token = this.extractToken(authHeader);

        if (!token) {
            return {
                valid: false,
                status_code: 2000,
                status_message: "Unauthorized - Kein gültiges Token gefunden",
                timestamp: new Date().toISOString()
            };
        }

        // Token validieren
        const connection = await this.validateToken(token, tokenType);

        if (!connection) {
            return {
                valid: false,
                status_code: 2000,
                status_message: "Unauthorized - Token ungültig",
                timestamp: new Date().toISOString()
            };
        }

        return {
            valid: true,
            connection,
            status_code: 1000,
            status_message: "Success",
            timestamp: new Date().toISOString()
        };
    }
};
