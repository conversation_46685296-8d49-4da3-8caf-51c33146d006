{"collectionName": "components_ocpi_status_schedules", "info": {"displayName": "StatusSchedule", "description": "OCPI 2.2.1 StatusSchedule object"}, "options": {}, "attributes": {"periodBegin": {"type": "datetime", "required": true, "comment": "Begin of the scheduled period in UTC, in ISO 8601 format"}, "periodEnd": {"type": "datetime", "comment": "End of the scheduled period in UTC, in ISO 8601 format"}, "status": {"type": "enumeration", "enum": ["AVAILABLE", "BLOCKED", "CHARGING", "INOPERATIVE", "OUTOFORDER", "PLANNED", "REMOVED", "RESERVED", "UNKNOWN"], "required": true, "comment": "Status during the scheduled period"}}}