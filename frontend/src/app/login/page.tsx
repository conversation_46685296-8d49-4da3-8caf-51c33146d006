"use client";

import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useAuth } from "~/components/AuthContext";

// Client component to safely use searchParams
function LoginForm() {
	const { login, user, loading, error: authError } = useAuth();
	const searchParams = useSearchParams();
	const callbackUrl = searchParams?.get("callbackUrl") || "/dashboard";

	return <LoginContent callbackUrl={callbackUrl} />;
}

// Main content component that doesn't directly use searchParams
function LoginContent({ callbackUrl }: { callbackUrl: string }) {
	const { login, user, loading, error: authError } = useAuth();
	const router = useRouter();

	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [username, setUsername] = useState("");
	const [password, setPassword] = useState("");

	useEffect(() => {
		if (user) {
			router.push(callbackUrl);
		}
	}, [user, router, callbackUrl]);

	async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
		e.preventDefault();
		setIsLoading(true);

		try {
			const formData = new FormData(e.currentTarget);
			const identifier = formData.get("username") as string;
			const password = formData.get("password") as string;

			// Strapi-Login durchführen
			await login(identifier, password);

			// Simuliere einen erfolgreichen Login für das Beispiel
			toast.success("Erfolgreich angemeldet!");

			// Falls die direkte Weiterleitung nicht funktioniert, versuchen wir es mit window.location
			// Dies ist ein Fallback und sollte nur verwendet werden, wenn router.push nicht funktioniert
			setTimeout(() => {
				if (window.location.pathname !== "/dashboard") {
					window.location.href = "/dashboard";
				}
			}, 1500);
		} catch (error) {
			toast.error(
				"Anmeldung fehlgeschlagen. Bitte überprüfen Sie Ihre Anmeldedaten.",
			);
		} finally {
			setIsLoading(false);
		}
	}

	return (
		<div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-[rgb(23,85,104)] via-[rgb(33,105,124)] to-[rgb(43,135,154)] p-4">
			<div className="w-full max-w-md">
				<div className="overflow-hidden rounded-2xl bg-white shadow-xl">
					{/* Header mit Logo auf weißem Hintergrund */}
					<div className="border-gray-200 border-b p-8 text-center">
						<div className="relative mx-auto mb-4">
							<div className="flex justify-center">
								<Image
									src="/logo/Eulektro-Logo.png"
									alt="Eulektro Logo"
									width={160}
									height={45}
									className="object-contain"
									priority
								/>
							</div>
						</div>
						<h1 className="mt-4 font-bold text-2xl text-[rgb(33,105,124)]">
							Payment Terminal Hub
						</h1>
						<h2 className="mt-2 text-[rgb(33,105,124)] text-xl">
							Willkommen zurück
						</h2>
						<p className="mt-1 text-gray-600">
							Melden Sie sich bei Ihrem Konto an
						</p>
					</div>

					{/* Formular mit leicht getöntem Hintergrund */}
					<div className="bg-[rgba(236,236,231,0.5)] p-8">
						<form onSubmit={handleSubmit} className="space-y-6">
							<div>
								<label
									className="mb-2 block font-medium text-gray-700 text-sm"
									htmlFor="username"
								>
									Benutzername
								</label>
								<input
									id="username"
									name="username"
									type="text"
									required
									value={username}
									onChange={(e) => setUsername(e.target.value)}
									className="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-[rgb(33,105,124)]"
									placeholder="Benutzername eingeben"
								/>
							</div>

							<div>
								<div className="mb-2 flex items-center justify-between">
									<label
										className="block font-medium text-gray-700 text-sm"
										htmlFor="password"
									>
										Passwort
									</label>
								</div>
								<input
									id="password"
									name="password"
									type="password"
									required
									value={password}
									onChange={(e) => setPassword(e.target.value)}
									className="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-[rgb(33,105,124)]"
									placeholder="••••••••"
								/>
							</div>

							<div>
								<button
									type="submit"
									disabled={isLoading}
									className={`btn-primary w-full py-3 ${isLoading ? "cursor-not-allowed opacity-70" : ""}`}
								>
									{isLoading ? "Anmeldung..." : "Anmelden"}
								</button>
							</div>
						</form>
					</div>

					{/* Footer */}
					<div className="border-gray-200 border-t bg-white py-4">
						<p className="text-center text-gray-600 text-sm">
							© {new Date().getFullYear()} Payment Terminal Hub | Eulektro
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}

// Main export component with Suspense boundary
export default function LoginPage() {
	return (
		<Suspense
			fallback={
				<div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-[rgb(23,85,104)] via-[rgb(33,105,124)] to-[rgb(43,135,154)] p-4">
					<div className="text-center text-white">
						<h1 className="mb-2 font-bold text-2xl">Payment Terminal Hub</h1>
						<p>Wird geladen...</p>
					</div>
				</div>
			}
		>
			<LoginForm />
		</Suspense>
	);
}
