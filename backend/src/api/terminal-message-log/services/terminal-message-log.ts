/**
 * terminal-message-log service
 */
import { factories } from '@strapi/strapi';

// Neues Interface für die Logging-Optionen, das nur die relevanten Felder definiert
interface TerminalMessageLogOptions {
    terminalId: string;
    direction: 'ServerToTerminal' | 'TerminalToServer' | 'WebToServer' | 'Unknown' | 'ServerToWeb';
    payload?: any;
    messageType?: 'info' | 'warning' | 'error' | 'debug';
    paymentSessionId?: string | null;
    // Es wird nur die documentId für die Mandanten-Relation benötigt
    mandantDocumentId?: string;
}

// Standard-Service erstellen
const defaultService = factories.createCoreService('api::terminal-message-log.terminal-message-log');

export default {
    ...defaultService,

    /**
     * Erstellt einen neuen Terminal-Message-Log-Eintrag
     *
     * @param options - Logging-Optionen
     * @returns - Erstellter Log-Eintrag
     */
    async log({
                  terminalId,
                  direction,
                  payload = null,
                  messageType = 'info',
                  paymentSessionId = null,
                  mandantDocumentId = null,
              }: TerminalMessageLogOptions) {
        try {

            // Stelle sicher, dass direction einen gültigen Wert hat
            const validDirections = ['ServerToTerminal', 'TerminalToServer', 'WebToServer', 'ServerToWeb', 'Unknown'];
            direction = validDirections.includes(direction) ? direction : 'Unknown';

            const logEntry: any = {
                terminalId,
                direction,
                messageType,
                payload: payload ? JSON.stringify(payload) : null,
                paymentSessionId,
            };

            // Mandanten-Relation hinzufügen, falls eine documentId übergeben wurde.
            // Hier wird über den connect-Operator nur die documentId gesetzt.
            if (mandantDocumentId) {
                logEntry.mandant = {
                    connect: { documentId: mandantDocumentId },
                };
            }

            // Log in die Datenbank schreiben
            const result = await strapi.entityService.create('api::terminal-message-log.terminal-message-log', {
                data: logEntry,
            });

            return result;
        } catch (error) {
            console.error('Fehler beim Erstellen des Terminal-Message-Logs:', error);
            // Bei Logfehler nicht abstürzen
            return null;
        }
    },

    /**
     * Loggt eine Nachricht vom Server zum Terminal
     */
    async logToTerminal(terminalId: string, payload: any, options: Partial<TerminalMessageLogOptions> = {}) {
        return this.log({
            terminalId,
            direction: 'ServerToTerminal',
            payload,
            paymentSessionId: options.paymentSessionId,
            mandantDocumentId: options.mandantDocumentId,
            messageType: 'info',
        });
    },

    /**
     * Loggt eine Nachricht vom Terminal zum Server
     */
    async logFromTerminal(terminalId: string, payload: any, options: Partial<TerminalMessageLogOptions> = {}) {
        return this.log({
            terminalId,
            direction: 'TerminalToServer',
            payload,
            paymentSessionId: options.paymentSessionId,
            // Hier wird ebenfalls die Mandanten-Relation korrekt übergeben
            mandantDocumentId: options.mandantDocumentId,
            messageType: 'info',
        });
    },

    /**
     * Loggt einen Fehler in der Terminal-Kommunikation
     */
    async logError(terminalId: string, payload: any, options: Partial<TerminalMessageLogOptions> = {}) {
        return this.log({
            terminalId,
            direction: options.direction || 'ServerToTerminal',
            payload,
            paymentSessionId: options.paymentSessionId,
            mandantDocumentId: options.mandantDocumentId,
            messageType: 'error',
        });
    },

    /**
     * Loggt eine Debug-Nachricht in der Terminal-Kommunikation
     */
    async logDebug(terminalId: string, payload: any, options: Partial<TerminalMessageLogOptions> = {}) {
        return this.log({
            terminalId,
            direction: options.direction || 'ServerToTerminal',
            payload,
            paymentSessionId: options.paymentSessionId,
            mandantDocumentId: options.mandantDocumentId,
            messageType: 'debug',
        });
    },
};
