'use strict';

/**
 * OCPI Location Routes für OCPI 2.2.1
 * Definiert die Endpunkte für GET, PUT und PATCH von Locations
 */
module.exports = {
    routes: [
        {
            method: 'GET',
            path: '/locations',
            handler: 'frontend-location.getLocations',
            config: {
                auth: false
            }
        },
        {
            method: 'PUT',
            path: '/locations/:documentId/assign-mandant',
            handler: 'frontend-location.assignMandant',
            config: {
                auth: false
            }
        },
    ],
};
