import screens from "../../config/payter-screens.json";

export async function authorizedCallback(ctx, terminalId: string, state: string) {
    let nextScreen: string | null = null;
    let sessionId: string = ctx.request.body?.sessionId || `session_${Date.now()}`;
    // Stelle sicher, dass sessionId immer ein String ist
    if (typeof sessionId !== 'string') {
        sessionId = `session_${Date.now()}`;
    }



    // Extrahiere die evseId aus der URL
    const evseId = ctx.request.body.session.merchantReference || '';
    console.log(`Processing authorize callback for terminal ${terminalId}, state: ${state}, evseId: ${evseId}`);

    try {
        // Extrahiere relevante Daten aus dem Request-Body
        const callbackData = ctx.request.body || {};

        // Prüfe, ob es sich um eine erfolgreiche Autorisierung handelt
        if (callbackData.session.state === 'AUTHORIZED' && callbackData.session.result === 'APPROVED') {
            console.log(`Payment authorized for terminal ${terminalId}, sessionId: ${callbackData.sessionId}`);

            // Suche das Terminal in der Datenbank
            const terminal = await strapi.documents('api::terminal.terminal').findFirst(
                {
                    filters: {
                        serialNumber: callbackData.session.serialNumber
                    },
                    populate: ['mandant']
                });

            if (!terminal) {
                console.error(`Terminal ${terminalId} not found in database`);
                return ctx.send({
                    status: 404,
                    message: `Terminal ${terminalId} not found`
                });
            }
            ctx.state.terminal = terminal;
            const { apiClient,callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

            // Hole den Mandanten aus dem Terminal, falls vorhanden
            const mandantId = terminal.mandant?.documentId;


            // Erstelle einen PaymentSession-Datensatz
            const paymentSession = await strapi.documents('api::payment-session.payment-session').create({
                data: {
                    // Verwende die sessionId als paymentIntent und transactionId
                    paymentIntent: callbackData.session.sessionId,
                    terminal: terminal.documentId,
                    mandant: mandantId,
                    ocpi_evse: callbackData.session.merchantReference || null,
                    blockedAmount: callbackData.session.authorizedAmount,
                    history: JSON.stringify([callbackData.session]),
                    paymentSessionState: 'authorized',
                    cardId: callbackData.session.cardId,
                    brand: callbackData.session.brand,
                    merchantReference: callbackData.session.merchantReference,
                    authorizationCode: callbackData.session.authorizationCode,
                    authorizationHostReference: callbackData.session.authorizationHostReference,
                    showPriceDateTime: new Date(decodeURIComponent(decodeURIComponent(decodeURIComponent(ctx.request.query.datetimeForShowPrice)))), // weniger codieren könnte hier auch helfen.
                    // Zeitstempel
                    authorizedAt: new Date(callbackData.session.transactionTime)
                }
            });

            console.log(`Created payment session with ID: ${paymentSession.documentId}`);

            // Zeige den Starting-Session-Screen an (Spinner)
            try {
                // Hole den Starting-Session-Screen aus der Konfiguration
                const startingSessionScreen = screens.screens[ctx.state.terminal.currentLanguage].find(screen => screen.id === 'screen-starting-session');
                if (startingSessionScreen) {
                    // Erstelle eine Kopie des Screens, um die Platzhalter zu ersetzen
                    const screenWithAmount = JSON.parse(JSON.stringify(startingSessionScreen));

                    await apiClient.post(`/terminals/${terminalId}/ui`, screenWithAmount);
                    console.log('Starting session screen displayed');
                }
            } catch (error) {
                console.error('Error displaying starting session screen:', error);
            }

            // Starte eine OCPI-Session, wenn eine evseId vorhanden ist
            if (callbackData.session.merchantReference) {
                try {
                    // Rufe den OCPI-Command-Service auf, um eine Ladesession zu starten
                    const ocpiResult = await strapi.service('api::ocpi-command.ocpi-command').startSession({
                        evse_id: callbackData.session.merchantReference,
                        mandant_id: mandantId,
                        payment_intent: paymentSession.documentId,
                        serialNumber: terminal.serialNumber,
                    });

                    if (ocpiResult.success) {
                        console.log('OCPI session started successfully:', ocpiResult.response.data);

                    } else {
                        console.error('Failed to start OCPI session:', ocpiResult.response);

                        const startFailedScreen =  screens.screens[ctx.state.terminal.currentLanguage].find(screen => screen.id === 'screen-start-command-response-error');
                        const params = new URLSearchParams({ callbackUrl: callback.ui });
                        await apiClient.post(`/terminals/${terminalId}/ui?${params.toString()}`, startFailedScreen);
                        //await cancelPaymentSession(paymentSession.documentId);
                    }
                } catch (error) {
                    console.error('Error starting OCPI session:', error);
                }
            }


            // Bereite die Antwort vor
            return ctx.send({
                status: 200,
                message: 'Payment session created successfully'
            });
        } else {
            // ToDo wass passiert, wenn die Autorisierung nicht erfolgreich ist?
            // kurze Info 3sec und dann zurück zu idle
        }


        // Wenn es keine erfolgreiche Autorisierung ist, gib eine einfache Bestätigung zurück
        return ctx.send({
            status: 200,
            message: 'Callback received',
            sessionId: sessionId
        });
    } catch (error) {
        console.error(`Error processing authorize callback for terminal ${terminalId}:`, error);

        return ctx.send({
            status: 500,
            message: `Error processing authorize callback: ${error.message || 'Unknown error'}`,
            sessionId: sessionId
        });
    }
}
