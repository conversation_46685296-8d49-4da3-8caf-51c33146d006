/**
 * Payter API Service
 *
 * Dieser Service stellt Methoden zur Kommunikation mit der Payter API bereit.
 */
import axios, {AxiosInstance, AxiosRequestConfig} from 'axios';

/**
 * Payter API Service
 */
export default {
    /**
     * Erstellt einen konfigurierten API-Client für die Payter API
     *
     * @param {string} baseUrl - Die Basis-URL der Payter API
     * @param {string} apiKey - Der API-Key für die Authentifizierung
     * @returns {AxiosInstance} - Der konfigurierte Axios-Client
     */
    getApiClient(baseUrl: string, apiKey: string): AxiosInstance {
        // Client erstellen mit Basis-URL und Standard-Headers
        const client = axios.create({
            baseURL: baseUrl,
            headers: {
                "Content-Type": "application/json",
                Authorization: `${apiKey}`,
            },
        });

        // Logging-Interceptors hinzufügen
        this.setupLoggingInterceptors(client);

        return client;
    },

    /**
     * Fügt Logging-Interceptors zum API-Client hinzu
     *
     * @param {AxiosInstance} client - Der Axios-Client
     * @param {string} mandantId - Optional: Die Mandanten-ID für das Logging
     */
    setupLoggingInterceptors(client: AxiosInstance, mandantId?: number): void {
        // Request-Interceptor für Logging
        client.interceptors.request.use(
            (config) => {
                // Terminal-ID aus dem Request extrahieren
                const terminalId = this.extractTerminalIdFromRequest(config);

                // Speichere die Request-Daten in der Config für spätere Verwendung im Response-Interceptor
                (config as any)._requestPayload = config.data;
                (config as any)._requestTimestamp = new Date().toISOString();
                (config as any)._terminalId = terminalId;

                console.log(`Sending ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);
                if (config.data) {
                    console.log('Request body:', config.data);
                }

                return config;
            },
            (error) => {
                console.error('Request error:', error);
                return Promise.reject(error);
            }
        );

        // Response-Interceptor für Logging
        client.interceptors.response.use(
            async (response) => {
                // Hole die gespeicherten Request-Daten aus der Config
                const requestPayload = (response.config as any)._requestPayload;
                const terminalId = (response.config as any)._terminalId || this.extractTerminalIdFromRequest(response.config);
                const requestTimestamp = (response.config as any)._requestTimestamp;

                // Extrahiere die PaymentSession-ID aus Request oder Response
                const paymentSessionId = this.extractPaymentSessionId(requestPayload, response.data);

                if (terminalId) {
                    try {
                        // Logge Request und Response als einen zusammenhängenden Datensatz
                        const logData = {
                            terminalId,
                            direction: 'ServerToTerminal', // Richtung der Anfrage (Server zu Terminal)
                            payload: requestPayload, // Original-Request
                            responsePayload: response.data, // Response-Daten
                            messageType: 'info',
                            paymentSessionId,
                            mandantId: mandantId || null
                        };

                        // Verwende den Strapi-Service zum Erstellen des Logs
                        await strapi.service('api::terminal-message-log.terminal-message-log').logToTerminal(
                            terminalId,
                            logData.payload,
                            {
                                paymentSessionId,
                                mandantId: mandantId || null,
                                responsePayload: logData.responsePayload
                            }
                        );
                    } catch (logError) {
                        console.error("Fehler beim Loggen der Terminal-Kommunikation:", logError);
                    }
                }

                console.log(`Response from ${response.config.url} - Status:`, response.status);
                return response;
            },
            async (error) => {
                if (error.response) {
                    // Ein Fehler mit einer Antwort vom Server
                    const requestPayload = (error.config as any)?._requestPayload;
                    const terminalId = (error.config as any)?._terminalId || this.extractTerminalIdFromRequest(error.config);
                    const requestTimestamp = (error.config as any)?._requestTimestamp;
                    const paymentSessionId = this.extractPaymentSessionId(requestPayload, error.response?.data);

                    if (terminalId) {
                        try {
                            // Logge Request und Error-Response als einen zusammenhängenden Datensatz
                            const logData = {
                                terminalId,
                                direction: 'ServerToTerminal',
                                payload: requestPayload,
                                responsePayload: {
                                    error: true,
                                    status: error.response.status,
                                    data: error.response.data
                                },
                                messageType: 'error',
                                paymentSessionId,
                                mandantId: mandantId || null
                            };

                            // Verwende den Strapi-Service zum Erstellen des Logs
                            await strapi.service('api::terminal-message-log.terminal-message-log').logToTerminal(
                                terminalId,
                                logData.payload,
                                {
                                    paymentSessionId,
                                    mandantId: mandantId || null,
                                    responsePayload: logData.responsePayload
                                }
                            );
                        } catch (logError) {
                            console.error("Fehler beim Loggen des Terminal-Fehlers:", logError);
                        }
                    }

                    console.error(`Error response from ${error.config.url} - Status:`, error.response.status);
                    console.error('Error details:', error.response.data);
                } else if (error.request) {
                    console.error(`No response received for request to ${error.config.url}`);
                } else {
                    console.error('Error setting up request:', error.message);
                }
                return Promise.reject(error);
            }
        );
    },

    /**
     * Extrahiert die Terminal-ID aus dem Request
     *
     * @param {AxiosRequestConfig} config - Die Axios-Konfiguration
     * @returns {string|null} - Die extrahierte Terminal-ID oder null
     */
    extractTerminalIdFromRequest(config: AxiosRequestConfig): string | null {
        try {
            // Aus der URL extrahieren, z.B. /terminals/1234
            if (config.url) {
                const urlMatch = config.url.match(/\/terminals\/([^\/]+)/);
                if (urlMatch && urlMatch[1]) {
                    return urlMatch[1];
                }
            }

            // Aus dem Request-Body extrahieren
            if (config.data) {
                const data = typeof config.data === 'string' ? JSON.parse(config.data) : config.data;

                if (data.terminalId) return data.terminalId;
                if (data.serialNumber) return data.serialNumber;
                if (data.terminal_id) return data.terminal_id;
            }

            // Aus den Parametern extrahieren
            if (config.params) {
                if (config.params.terminalId) return config.params.terminalId;
                if (config.params.serialNumber) return config.params.serialNumber;
                if (config.params.terminal_id) return config.params.terminal_id;
            }

            return null;
        } catch (error) {
            console.error("Fehler beim Extrahieren der Terminal-ID:", error);
            return null;
        }
    },

    /**
     * Extrahiert die PaymentSession-ID aus Request oder Response
     *
     * @param {any} requestData - Die Request-Daten
     * @param {any} responseData - Die Response-Daten
     * @returns {string|null} - Die extrahierte PaymentSession-ID oder null
     */
    extractPaymentSessionId(requestData: any, responseData: any): string | null {
        try {
            // Aus dem Request extrahieren
            if (requestData) {
                const request = typeof requestData === 'string' ? JSON.parse(requestData) : requestData;

                if (request.paymentSessionId) return request.paymentSessionId;
                if (request.payment_session_id) return request.payment_session_id;
                if (request.sessionId) return request.sessionId;
                if (request.session_id) return request.session_id;
            }

            // Aus der Response extrahieren
            if (responseData) {
                const response = typeof responseData === 'string' ? JSON.parse(responseData) : responseData;

                if (response.paymentSessionId) return response.paymentSessionId;
                if (response.payment_session_id) return response.payment_session_id;
                if (response.sessionId) return response.sessionId;
                if (response.session_id) return response.session_id;
            }

            return null;
        } catch (error) {
            console.error("Fehler beim Extrahieren der PaymentSession-ID:", error);
            return null;
        }
    }
};
