import type React from "react";
import { FaUnlink } from "react-icons/fa";
import { FiLink, FiTrash2 } from "react-icons/fi";
import { toast } from "react-toastify";
import { Button } from "~/components/ui/button";
import { apiClient } from "~/services/api";

export interface OcpiConnection {
	documentId: string;
	name: string;
	ConnectionUrl: string;
	connectionStatus: "new" | "pending" | "registered" | "active" | "inactive";
	lastConnection?: string;
	OCPIVersion?: string;
	Type?: "Prod" | "Test" | "Dev";
	role?: "sender" | "receiver";
	mandantId?: number;
	operatorId?: string;
	partyId?: string;
	countryCode?: string;
	companyName?: string;
}

interface OcpiConnectionTableProps {
	connections: OcpiConnection[];
	onRefresh: () => void;
}

const OcpiConnectionTable: React.FC<OcpiConnectionTableProps> = ({
	connections,
	onRefresh,
}) => {
	const handleConnect = async (documentId: string) => {
		try {
			// Verwende den POST-Endpunkt für den Handshake
			const response = await apiClient.post(
				`/api/ocpi-connections/${documentId}/connect`,
			);
			// Aktualisiere die Verbindungsliste nach dem Verbinden
			onRefresh();
			// Erfolgsmeldung anzeigen
			toast.success("OCPI-Handshake wurde erfolgreich gestartet");
		} catch (error) {
			console.error("Fehler beim Verbinden:", error);
			toast.error("Fehler beim Verbinden der OCPI-Verbindung");
		}
	};

	const handleDisconnect = async (documentId: string) => {
		try {
			// Bestätigung vom Benutzer einholen - mit Toast statt confirm
			if (
				window.confirm("Möchten Sie diese OCPI-Verbindung wirklich trennen?")
			) {
				// Nur fortfahren, wenn der Benutzer bestätigt
			} else {
				return;
			}

			// Verwende den POST-Endpunkt zum Trennen der Verbindung
			const response = await apiClient.post(
				`/api/ocpi-connections/${documentId}/disconnect`,
			);
			// Aktualisiere die Verbindungsliste nach dem Trennen
			onRefresh();
			// Erfolgsmeldung anzeigen
			toast.success("OCPI-Verbindung wurde erfolgreich getrennt");
		} catch (error) {
			console.error("Fehler beim Trennen:", error);
			toast.error("Fehler beim Trennen der OCPI-Verbindung");
		}
	};

	const handleDelete = async (documentId: string) => {
		try {
			// Prüfe zuerst, ob die Verbindung aktiv ist
			const connection = connections.find((c) => c.documentId === documentId);
			if (connection?.connectionStatus === "active") {
				toast.warning(
					"Die Verbindung muss zuerst getrennt werden, bevor sie gelöscht werden kann.",
				);
				return;
			}

			// Bestätigung vom Benutzer einholen
			if (
				window.confirm("Möchten Sie diese OCPI-Verbindung wirklich löschen?")
			) {
				// Nur fortfahren, wenn der Benutzer bestätigt
			} else {
				return;
			}

			// Verbindung löschen
			await apiClient.delete(`/api/ocpi-connections/${documentId}`);
			// Aktualisiere die Verbindungsliste nach dem Löschen
			onRefresh();
			// Erfolgsmeldung anzeigen
			toast.success("OCPI-Verbindung erfolgreich gelöscht");
		} catch (error) {
			console.error("Fehler beim Löschen:", error);
			toast.error("Fehler beim Löschen der OCPI-Verbindung");
		}
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "active":
				return (
					<span className="rounded-full bg-green-100 px-2 py-1 text-green-800 text-xs">
						Aktiv
					</span>
				);
			case "pending":
				return (
					<span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-800">
						Wird verbunden
					</span>
				);
			case "registered":
				return (
					<span className="rounded-full bg-blue-100 px-2 py-1 text-blue-800 text-xs">
						Registriert
					</span>
				);
			case "new":
				return (
					<span className="rounded-full bg-purple-100 px-2 py-1 text-purple-800 text-xs">
						Neu
					</span>
				);
			default:
				return (
					<span className="rounded-full bg-gray-100 px-2 py-1 text-gray-800 text-xs">
						Inaktiv
					</span>
				);
		}
	};

	return (
		<div className="overflow-x-auto">
			<table className="min-w-full bg-white">
				<thead className="bg-gray-50">
					<tr>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Name
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							URL
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Status
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Version
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Typ
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Rolle
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Operator ID
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Party ID
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Land
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Firma
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Letzte Verbindung
						</th>
						<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
							Aktionen
						</th>
					</tr>
				</thead>
				<tbody className="divide-y divide-gray-200">
					{connections.length === 0 ? (
						<tr>
							<td
								colSpan={12}
								className="px-6 py-4 text-center text-gray-500 text-sm"
							>
								Keine OCPI-Verbindungen gefunden. Klicken Sie auf "Neue
								Verbindung", um eine OCPI-Verbindung anzulegen.
							</td>
						</tr>
					) : (
						connections.map((connection) => (
							<tr key={connection.documentId}>
								<td className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 text-sm">
									{connection.name}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.ConnectionUrl}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{getStatusBadge(connection.connectionStatus)}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.OCPIVersion || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.Type || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.role || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.operatorId || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.partyId || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.countryCode || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.companyName || "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
									{connection.lastConnection
										? new Date(connection.lastConnection).toLocaleString()
										: "-"}
								</td>
								<td className="whitespace-nowrap px-6 py-4 font-medium text-sm">
									<div className="flex space-x-2">
										{connection.connectionStatus !== "active" ? (
											<Button
												onClick={() => handleConnect(connection.documentId)}
												variant="default"
												className="flex items-center"
												disabled={connection.connectionStatus === "pending"}
											>
												<FiLink className="mr-1" />
												Verbinden
											</Button>
										) : (
											<Button
												onClick={() => handleDisconnect(connection.documentId)}
												variant="default"
												className="flex items-center"
											>
												<FaUnlink className="mr-1" />
												Trennen
											</Button>
										)}

										<Button
											onClick={() => handleDelete(connection.documentId)}
											variant="destructive"
											className="flex items-center"
											disabled={connection.connectionStatus === "active"}
										>
											<FiTrash2 className="mr-1" />
											Löschen
										</Button>
									</div>
								</td>
							</tr>
						))
					)}
				</tbody>
			</table>
		</div>
	);
};

export default OcpiConnectionTable;
