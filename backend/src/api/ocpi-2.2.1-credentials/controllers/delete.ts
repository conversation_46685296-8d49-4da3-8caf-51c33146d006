// src/api/ocpi-2.2.1-credentials/controllers/delete.ts
'use strict';

/**
 * OCPI Credentials DELETE Controller
 * Implementiert den Endpunkt zum Löschen einer OCPI-Verbindung
 */
module.exports = {
    /**
     * DELETE /ocpi/2.2.1/credentials
     * Wird aufgerufen, wenn ein Partner die Verbindung löschen möchte
     */
    async deleteCredentials(ctx) {
        try {

            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            if (!validationResult.connection) {
                await logger.error(`Keine Verbindung mit dem ReceivingSecret ${ctx.request.body.token} gefunden`, {
                    endpoint: 'ocpi/credentials/delete'
                });

                return ctx.send({
                    status_code: 2003,
                    status_message: "Unknown Connection",
                    timestamp: new Date().toISOString()
                }, 404);
            }
            const connection = validationResult.connection;

            // Die Verbindung als inaktiv markieren und Tokens zurücksetzen
            await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                documentId: connection.documentId,
                data: {
                    connectionStatus: 'disconnected',
                    sendSecret: null,
                    lastConnection: new Date().toISOString()
                }
            });

            await logger.info(`Verbindung mit ID ${connection.documentId} wurde durch Remote-DELETE deaktiviert`, {
                endpoint: 'ocpi/credentials/delete'
            });

            // Erfolgsantwort senden
            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            // Logger-Service direkt referenzieren
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            await logger.error(`Fehler beim Verarbeiten der DELETE-Anfrage: ${error.message || 'Unbekannter Fehler'}`, {
                endpoint: 'ocpi/credentials/delete',
                error: error.stack || error.message
            });

            return ctx.send({
                status_code: 3000,
                status_message: "Server error",
                timestamp: new Date().toISOString()
            }, 500);
        }
    }
};
