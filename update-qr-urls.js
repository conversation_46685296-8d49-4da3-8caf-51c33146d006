const fs = require('fs');
const path = require('path');

// Pfad zur JSON-Datei
const filePath = path.join(__dirname, 'backend/src/api/payter/config/payter-screens.json');

// Datei einlesen
fs.readFile(filePath, 'utf8', (err, data) => {
  if (err) {
    console.error('Fehler beim Lesen der Datei:', err);
    return;
  }

  // JSON parsen
  const screens = JSON.parse(data);

  // Alle Sprachen durchlaufen
  Object.keys(screens.screens).forEach(language => {
    // Alle Screens der Sprache durchlaufen
    screens.screens[language].forEach(screen => {
      // Wenn es sich um den screen-charging-started handelt und eine qr-Eigenschaft hat
      if (screen.id === 'screen-charging-started' && screen.properties && screen.properties.qr) {
        // URL aktualisieren
        screen.properties.qr = "{{frontendURL}}/invoice/{{session_id}}";
        console.log(`QR-URL für Sprache ${language} aktualisiert`);
      }
    });
  });

  // Aktualisierte JSON-Datei speichern
  fs.writeFile(filePath, JSON.stringify(screens, null, 2), 'utf8', (err) => {
    if (err) {
      console.error('Fehler beim Schreiben der Datei:', err);
      return;
    }
    console.log('Alle QR-URLs wurden erfolgreich aktualisiert!');
  });
});
