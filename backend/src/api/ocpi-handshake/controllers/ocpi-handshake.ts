'use strict';

/**
 * OCPI Handshake Controller
 * Bietet Endpunkte für das Auslösen des OCPI-Handshakes
 */
module.exports = {
    /**
     * GET /ocpi/handshake/:name
     * Startet einen OCPI-Handshake für die angegebene Verbindung über den Namen
     */
    async handshake(ctx) {
        try {
            // Den Namen aus den Route-Parametern auslesen
            const { name } = ctx.params;

            if (!name) {
                return ctx.send({
                    status_code: 3001,
                    status_message: "Kein Verbindungsname angegeben"
                });
            }

            // Handshake-Service aufrufen mit dem Namen
            const result = await strapi.service('api::ocpi-handshake.ocpi-handshake')
                .performHandshake(name, false);

            // Ergebnis zurückgeben
            return ctx.send(result);
        } catch (err) {
            strapi.log.error('Fehler beim OCPI-Handshake:', err);
            return ctx.send({
                status_code: 3000,
                status_message: `Serverfehler: ${err.message || 'Unbekannter Fehler'}`
            });
        }
    },

    /**
     * GET /ocpi/handshake/document/:documentId
     * Startet einen OCPI-Handshake für die angegebene Verbindung über die documentId
     */
    async handshakeByDocumentId(ctx) {
        try {
            // Die DocumentID aus den Route-Parametern auslesen
            const { documentId } = ctx.params;

            if (!documentId) {
                return ctx.send({
                    status_code: 3001,
                    status_message: "Keine Verbindungs-DocumentID angegeben"
                });
            }

            // Handshake-Service aufrufen mit der DocumentID
            const result = await strapi.service('api::ocpi-handshake.ocpi-handshake')
                .performHandshake(documentId, true);

            // Ergebnis zurückgeben
            return ctx.send(result);
        } catch (err) {
            strapi.log.error('Fehler beim OCPI-Handshake:', err);
            return ctx.send({
                status_code: 3000,
                status_message: `Serverfehler: ${err.message || 'Unbekannter Fehler'}`
            });
        }
    },

    /**
     * GET /ocpi/handshake/status/:name
     * Gibt den Status einer OCPI-Verbindung zurück
     */
    async status(ctx) {
        try {
            // Den Namen aus den Route-Parametern auslesen
            const { name } = ctx.params;

            if (!name) {
                return ctx.send({
                    status_code: 3001,
                    status_message: "Kein Verbindungsname angegeben"
                });
            }

            // OCPI Connection laden
            const connection = await strapi.db.query('api::ocpi-connection.ocpi-connection').findOne({
                where: { name }
            });

            if (!connection) {
                return ctx.send({
                    status_code: 3002,
                    status_message: `OCPI Connection mit Name "${name}" nicht gefunden`
                });
            }

            // Status zurückgeben
            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                data: {
                    id: connection.id,
                    name: connection.name,
                    status: connection.connectionStatus,
                    lastConnection: connection.lastConnection
                }
            });
        } catch (err) {
            strapi.log.error('Fehler beim Abrufen des OCPI-Status:', err);
            return ctx.send({
                status_code: 3000,
                status_message: `Serverfehler: ${err.message || 'Unbekannter Fehler'}`
            });
        }
    },

    /**
     * GET /ocpi/handshake/status/document/:documentId
     * Gibt den Status einer OCPI-Verbindung zurück
     */
    async statusByDocumentId(ctx) {
        try {
            // Die DocumentID aus den Route-Parametern auslesen
            const { documentId } = ctx.params;

            if (!documentId) {
                return ctx.send({
                    status_code: 3001,
                    status_message: "Keine Verbindungs-DocumentID angegeben"
                });
            }

            // OCPI Connection laden
            const connection = await strapi.db.query('api::ocpi-connection.ocpi-connection').findOne({
                where: { id: documentId }
            });

            if (!connection) {
                return ctx.send({
                    status_code: 3002,
                    status_message: `OCPI Connection mit DocumentID "${documentId}" nicht gefunden`
                });
            }

            // Status zurückgeben
            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                data: {
                    id: connection.id,
                    name: connection.name,
                    status: connection.connectionStatus,
                    lastConnection: connection.lastConnection
                }
            });
        } catch (err) {
            strapi.log.error('Fehler beim Abrufen des OCPI-Status:', err);
            return ctx.send({
                status_code: 3000,
                status_message: `Serverfehler: ${err.message || 'Unbekannter Fehler'}`
            });
        }
    }
};
