/**
 * ocpi-connection service
 */

import {factories, Data, UID} from '@strapi/strapi';

type OCPIConnection = Data.ContentType<'api::ocpi-connection.ocpi-connection'>

// ENUM für die möglichen OCPI-Module
export enum OCPIModule {
    CREDENTIALS = 'credentials',
    TOKENS = 'tokens',
    COMMANDS = 'commands',
    LOCATIONS = 'locations',
    SESSIONS = 'sessions',
    CDRS = 'cdrs',
    TARIFFS = 'tariffs'
}

// Mögliche Rollen
export enum OCPIRole {
    SENDER = 'SENDER',
    RECEIVER = 'RECEIVER'
}

// Interface für ein OCPI-Modul in der remoteModules-Liste
interface OCPIModuleEntry {
    identifier: OCPIModule;
    role: OCPIRole;
    url: string;
}


export default factories.createCoreService('api::ocpi-connection.ocpi-connection', ({strapi}) => ({

    /**
     * Gibt die URL für ein bestimmtes OCPI-Modul zurück
     *
     * @param connection - Die OCPI-Connection oder das OCPI-Connection-Objekt
     * @param moduleIdentifier - Der Identifier des Moduls (z.B. OCPIModule.COMMANDS)
     * @returns Die URL des Moduls oder null, wenn das Modul nicht gefunden wurde
     */
    async getModuleUrl(connection: OCPIConnection, moduleIdentifier: OCPIModule): Promise<string | null> {
        try {

            // Prüfen, ob remoteModules vorhanden ist
            if (!connection.remoteModules || !Array.isArray(connection.remoteModules)) {
                console.error(`No remoteModules found for OCPI Connection ${connection.documentId || connection.id}`);
                return null;
            }

            // Suche das Modul in der remoteModules-Liste
            const moduleEntry = connection.remoteModules.find(
                (module) => {
                    const typeModul = module as unknown as OCPIModuleEntry;
                    return typeModul?.identifier === moduleIdentifier
                }
            ) as unknown as OCPIModuleEntry | undefined;

            if (!moduleEntry) {
                console.error(`Module ${moduleIdentifier} not found in remoteModules for OCPI Connection ${connection.documentId || connection.id}`);
                return null;
            }

            return moduleEntry.url;
        } catch (error) {
            console.error(`Error getting module URL: ${(error as Error).message}`);
            return null;
        }
    },

    /**
     * Sendet eine POST-Anfrage an ein bestimmtes OCPI-Modul
     *
     * @param connectionIdOrObject - Die documentId einer OCPI-Connection oder das OCPI-Connection-Objekt
     * @param moduleIdentifier - Der Identifier des Moduls
     * @param payload - Die zu sendenden Daten
     * @param path - Der Pfad, der an die URL angehängt werden soll (optional)
     * @returns Die Antwort des Servers oder null im Fehlerfall
     */
    async postModule(connectionIdOrObject: string | OCPIConnection, moduleIdentifier: OCPIModule, payload: any, path?: string): Promise<Response | null> {
        // Prüfen, ob ein Objekt oder eine ID übergeben wurde
        let connection: OCPIConnection;

        if (typeof connectionIdOrObject === 'string') {
            // Es wurde eine ID übergeben, hole die Connection aus der Datenbank
            connection = await strapi.documents('api::ocpi-connection.ocpi-connection').findOne({
                documentId: connectionIdOrObject
            });

            if (!connection) {
                console.error(`OCPI Connection with ID ${connectionIdOrObject} not found`);
                return null;
            }
        } else {
            // Es wurde ein Objekt übergeben
            connection = connectionIdOrObject;
        }

        let moduleUrl = await this.getModuleUrl(connection, moduleIdentifier);

        if (!moduleUrl) {
            console.error(`No URL found for module ${moduleIdentifier}`);
            return null;
        }

        if (path) {
            // Füge den Pfad zur URL hinzu
            moduleUrl += path;
        }

        try {
            const response = await fetch(moduleUrl, {
                method: "POST",
                headers: {
                    authorization: `Token ${connection.sendSecret}`,
                    "content-type": "application/json"
                },
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                console.error(`Error posting to module ${moduleIdentifier}: ${response.status} ${response.statusText}`);
            }

            return response;
        } catch (error) {
            console.error(`Error posting to module ${moduleIdentifier}: ${(error as Error).message}`);
            return null;
        }

    },
}));
