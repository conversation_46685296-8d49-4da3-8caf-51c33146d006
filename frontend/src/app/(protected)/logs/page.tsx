"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>ow<PERSON>,
	<PERSON>R<PERSON>resh<PERSON><PERSON>,
	FiSearch,
} from "react-icons/fi";
import { useMandant } from "~/components/MandantContext";
import { useDebounce } from "~/hooks/useDebounce";
import { apiClient, payterApi } from "~/services/api";

// Konstanten für die Richtungsfilter
const MESSAGE_DIRECTIONS = {
	SERVER_TO_TERMINAL: "ServerToTerminal",
	TERMINAL_TO_SERVER: "TerminalToServer",
	WEB_TO_SERVER: "WebToServer",
	UNKNOWN: "Unknown",
};

// Liste der gültigen Richtungswerte
const VALID_DIRECTIONS = Object.values(MESSAGE_DIRECTIONS);

// Typ für die Terminal Message Log Einträge
interface TerminalMessageLog {
	id: number;
	terminalId: string;
	direction:
		| "ServerToTerminal"
		| "TerminalToServer"
		| "WebToServer"
		| "Unknown";
	payload: any;
	messageType: "info" | "warning" | "error" | "debug";
	paymentSessionId?: string;
	createdAt: string;
	updatedAt: string;
	mandant?: {
		id: number;
		name: string;
	};
}

// Filterzustände
interface FilterState {
	search: string;
	direction: string;
	messageType: string;
	terminalId: string;
	mandantId: string;
	dateFrom: string;
	dateTo: string;
}

export default function LogsPage() {
	const { activeMandant, getAllowedMandantIds, availableMandants } =
		useMandant();
	const [logs, setLogs] = useState<TerminalMessageLog[]>([]);
	const [loading, setLoading] = useState(true);
	const [liveMode, setLiveMode] = useState(false);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [filters, setFilters] = useState<FilterState>({
		search: "",
		direction: "",
		messageType: "",
		terminalId: "",
		mandantId: "",
		dateFrom: "",
		dateTo: "",
	});
	const debouncedSearch = useDebounce(filters.search, 500);
	const observerRef = useRef<IntersectionObserver | null>(null);
	const loadingRef = useRef<HTMLDivElement>(null);
	const liveModeInterval = useRef<NodeJS.Timeout | null>(null);

	// Ref zum Tracken des aktuellen Ladezustandes, um mehrfaches Laden zu verhindern
	const isLoadingRef = useRef(false);

	// Terminal-IDs für Filter-Dropdown
	const [terminalIds, setTerminalIds] = useState<string[]>([]);

	// Funktion zum Laden der Logs
	const loadLogs = useCallback(
		async (pageToLoad = 1, replace = false) => {
			// Verhindere mehrfaches Laden oder Laden bei leeren Ergebnissen
			if (isLoadingRef.current || (pageToLoad > 1 && !hasMore)) {
				return;
			}

			// Setze Ladestatus
			isLoadingRef.current = true;
			setLoading(true);

			try {
				// Erstellen der Filter-Parameter
				const params = new URLSearchParams();
				params.append("pagination[page]", pageToLoad.toString());
				params.append("pagination[pageSize]", "30");
				params.append("sort[0]", "createdAt:desc");

				// Suchfilter anwenden
				if (debouncedSearch) {
					params.append(
						"filters[$or][0][payload][$containsi]",
						debouncedSearch,
					);
					params.append(
						"filters[$or][1][terminalId][$containsi]",
						debouncedSearch,
					);
					params.append(
						"filters[$or][2][paymentSessionId][$containsi]",
						debouncedSearch,
					);
				}

				// Weitere Filter anwenden
				// Stellen Sie sicher, dass der Richtungsfilter korrekt angewendet wird
				// Verwenden Sie den exakten ENUM-Wert für die Richtung
				if (filters.direction) {
					// Validieren Sie den Wert gegen die bekannten ENUM-Werte
					if (VALID_DIRECTIONS.includes(filters.direction)) {
						params.append("filters[direction][$eq]", filters.direction);
					}
				}
				if (filters.messageType) {
					params.append("filters[messageType][$eq]", filters.messageType);
				}
				if (filters.terminalId) {
					params.append("filters[terminalId][$eq]", filters.terminalId);
				}
				if (filters.dateFrom) {
					params.append("filters[createdAt][$gte]", filters.dateFrom);
				}
				if (filters.dateTo) {
					params.append("filters[createdAt][$lte]", filters.dateTo);
				}

				// Mandantenfilter
				if (filters.mandantId) {
					params.append("filters[mandant][id][$eq]", filters.mandantId);
				} else {
					const allowedMandantIds = activeMandant ? getAllowedMandantIds() : [];
					if (allowedMandantIds.length > 0) {
						allowedMandantIds.forEach((id, index) => {
							params.append(
								`filters[$or][${index}][mandant][id][$eq]`,
								id.toString(),
							);
						});
					}
				}

				// Konsolen-Logging für Debugging
				console.log(
					"API Request URL:",
					`/api/terminal-message-logs?${params.toString()}`,
				);

				const response = await apiClient.get("/api/terminal-message-logs", {
					params,
				});

				// Konsolen-Logging für Debugging
				console.log("API Response:", response.data);

				// Sicherstellen, dass wir die Datenstruktur richtig behandeln, insbesondere bei leeren Ergebnissen
				const newLogs = Array.isArray(response.data.data)
					? response.data.data.map((item: any) => ({
							id: item.id,
							...item.attributes,
							mandant: item.attributes.mandant?.data
								? {
										id: item.attributes.mandant.data.id,
										name: item.attributes.mandant.data.attributes.Name,
									}
								: undefined,
						}))
					: [];

				// Logs ersetzen oder anhängen
				if (replace) {
					setLogs(newLogs);
				} else {
					setLogs((prev) => [...prev, ...newLogs]);
				}

				// Pagination aktualisieren
				const {
					page: currentPage,
					pageCount,
					total,
				} = response.data.meta.pagination;
				const noMoreData =
					newLogs.length === 0 || currentPage >= pageCount || total === 0;
				setHasMore(!noMoreData);
				setPage(currentPage);
			} catch (error) {
				console.error("Fehler beim Laden der Logs:", error);
			} finally {
				isLoadingRef.current = false;
				setLoading(false);
			}
		},
		[activeMandant, debouncedSearch, filters, hasMore],
	);

	// Funktion zum Laden der Terminal-IDs für Filter
	const loadTerminalIds = useCallback(async () => {
		try {
			const response = await apiClient.get(
				"/api/terminal-message-logs/terminal-ids",
			);
			setTerminalIds(response.data);
		} catch (error) {
			console.error("Fehler beim Laden der Terminal-IDs:", error);
		}
	}, []);

	// Effekt: Initialisierung der Seite
	useEffect(() => {
		loadLogs(1, true);
		loadTerminalIds();
	}, [loadLogs, loadTerminalIds]);

	// Effekt: Suchfunktion und erweiterte Filter
	useEffect(() => {
		loadLogs(1, true);
	}, [
		debouncedSearch,
		filters.direction,
		filters.messageType,
		filters.terminalId,
		filters.mandantId,
		filters.dateFrom,
		filters.dateTo,
		loadLogs,
	]);

	// Effekt: Live-Modus
	useEffect(() => {
		if (liveMode) {
			liveModeInterval.current = setInterval(() => {
				loadLogs(1, true);
			}, 5000);
		} else if (liveModeInterval.current) {
			clearInterval(liveModeInterval.current);
		}

		return () => {
			if (liveModeInterval.current) {
				clearInterval(liveModeInterval.current);
			}
		};
	}, [liveMode, loadLogs]);

	// Effekt: Infinite Scrolling
	useEffect(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				// Sicherstellen, dass entries nicht leer ist, bevor auf das erste Element zugegriffen wird
				if (
					entries.length > 0 &&
					entries[0]?.isIntersecting &&
					hasMore &&
					!loading &&
					!liveMode &&
					logs.length > 0
				) {
					loadLogs(page + 1, false);
				}
			},
			{ threshold: 0.5 },
		);

		if (loadingRef.current && hasMore && logs.length > 0) {
			observer.observe(loadingRef.current);
		}

		observerRef.current = observer;

		return () => {
			if (observerRef.current) {
				observerRef.current.disconnect();
			}
		};
	}, [hasMore, loading, liveMode, loadLogs, page, logs.length]);

	// Filterfunktionen
	const handleFilterChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
	) => {
		const { name, value } = e.target;
		setFilters((prev) => ({ ...prev, [name]: value }));
	};

	// Handler für die Aktualisierungsschaltfläche
	const handleRefresh = () => {
		loadLogs(1, true);
	};

	// Handler für den Live-Modus-Schalter
	const toggleLiveMode = () => {
		setLiveMode((prev) => !prev);
	};

	// Funktion zum Initialisieren eines Terminals
	const handleInitTerminal = async (terminalId: string) => {
		try {
			// Zeige Ladeindikator oder Benachrichtigung
			setLoading(true);

			// Rufe den Payter-Init-Endpunkt auf
			const response = await payterApi.initTerminal(terminalId);

			// Zeige Erfolgsmeldung
			alert(`Terminal ${terminalId} erfolgreich initialisiert!`);

			// Lade die Logs neu, um die neuesten Einträge anzuzeigen
			loadLogs(1, true);
		} catch (error: any) {
			// Zeige Fehlermeldung
			console.error(
				`Fehler beim Initialisieren des Terminals ${terminalId}:`,
				error,
			);
			alert(
				`Fehler beim Initialisieren des Terminals ${terminalId}: ${error?.message || "Unbekannter Fehler"}`,
			);
		} finally {
			// Setze Ladeindikator zurück
			setLoading(false);
		}
	};

	// Hilfsfunktion zum Formatieren von Datum und Uhrzeit
	const formatDateTime = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleString("de-DE", {
			day: "2-digit",
			month: "2-digit",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit",
			second: "2-digit",
		});
	};

	// Hilfsfunktion zum Formatieren der Richtung
	const formatDirection = (direction: string) => {
		switch (direction) {
			case "ServerToTerminal":
				return "Server → Terminal";
			case "TerminalToServer":
				return "Terminal → Server";
			default:
				return direction;
		}
	};

	// Hilfsfunktion zum Formatieren des Nachrichtentyps mit Farbe
	const getMessageTypeClass = (type: string) => {
		switch (type) {
			case "info":
				return "bg-blue-100 text-blue-800";
			case "warning":
				return "bg-yellow-100 text-yellow-800";
			case "error":
				return "bg-red-100 text-red-800";
			case "debug":
				return "bg-gray-100 text-gray-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	// Hilfsfunktion zum Formatieren von JSON
	const formatPayload = (payload: any) => {
		try {
			const jsonObject =
				typeof payload === "string" ? JSON.parse(payload) : payload;
			return JSON.stringify(jsonObject, null, 2);
		} catch (error) {
			return typeof payload === "string" ? payload : JSON.stringify(payload);
		}
	};

	// Hilfsfunktion zum Extrahieren der URL aus dem Payload
	const extractUrlFromPayload = (payload: any): string | null => {
		try {
			if (!payload) return null;

			const jsonObject =
				typeof payload === "string" ? JSON.parse(payload) : payload;

			// Suche nach URL-Eigenschaften im Payload
			const urlProperties = ["url", "URL", "uri", "URI", "endpoint", "path"];

			for (const prop of urlProperties) {
				if (jsonObject[prop] && typeof jsonObject[prop] === "string") {
					return jsonObject[prop];
				}
			}

			// Wenn keine direkte URL-Eigenschaft gefunden wurde, suche in verschachtelten Objekten
			for (const key in jsonObject) {
				if (jsonObject[key] && typeof jsonObject[key] === "object") {
					for (const prop of urlProperties) {
						if (
							jsonObject[key][prop] &&
							typeof jsonObject[key][prop] === "string"
						) {
							return jsonObject[key][prop];
						}
					}
				}
			}

			return null;
		} catch (error) {
			return null;
		}
	};

	return (
		<div>
			<h1 className="mb-6 font-bold text-2xl">Terminal Message Logs</h1>

			{/* Filter- und Steuerungsleiste */}
			<div className="mb-6 rounded bg-white p-4 shadow">
				<div className="mb-4 flex flex-wrap items-center gap-4">
					{/* Live-Modus-Schalter */}
					<button
						onClick={toggleLiveMode}
						className={`flex items-center gap-2 rounded px-3 py-2 ${
							liveMode ? "bg-green-600 text-white" : "bg-gray-200 text-gray-700"
						}`}
					>
						{liveMode ? <FiPause /> : <FiPlay />}
						{liveMode ? "Live-Modus aktiv" : "Live-Modus"}
					</button>

					{/* Aktualisierungsschaltfläche */}
					<button
						onClick={handleRefresh}
						className="flex items-center gap-2 rounded bg-blue-600 px-3 py-2 text-white hover:bg-blue-700"
						disabled={loading}
					>
						<FiRefreshCw className={loading ? "animate-spin" : ""} />
						Aktualisieren
					</button>

					{/* Suchfeld */}
					<div className="flex items-center rounded bg-gray-100 px-3 py-2">
						<FiSearch className="mr-2 text-gray-500" />
						<input
							type="text"
							name="search"
							value={filters.search}
							onChange={handleFilterChange}
							placeholder="Suchen..."
							className="w-64 border-none bg-transparent outline-none"
						/>
					</div>
				</div>

				{/* Erweiterte Filter */}
				<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6">
					<div>
						<label className="mb-1 block font-medium text-gray-700 text-sm">
							Richtung
						</label>
						<select
							name="direction"
							value={filters.direction}
							onChange={handleFilterChange}
							className="w-full rounded border border-gray-300 px-3 py-2"
						>
							<option value="">Alle</option>
							<option value={MESSAGE_DIRECTIONS.SERVER_TO_TERMINAL}>
								Server → Terminal
							</option>
							<option value={MESSAGE_DIRECTIONS.TERMINAL_TO_SERVER}>
								Terminal → Server
							</option>
							<option value={MESSAGE_DIRECTIONS.WEB_TO_SERVER}>
								Web → Server
							</option>
							<option value={MESSAGE_DIRECTIONS.UNKNOWN}>Unbekannt</option>
						</select>
					</div>

					<div>
						<label className="mb-1 block font-medium text-gray-700 text-sm">
							Nachrichtentyp
						</label>
						<select
							name="messageType"
							value={filters.messageType}
							onChange={handleFilterChange}
							className="w-full rounded border border-gray-300 px-3 py-2"
						>
							<option value="">Alle</option>
							<option value="info">Info</option>
							<option value="warning">Warnung</option>
							<option value="error">Fehler</option>
							<option value="debug">Debug</option>
						</select>
					</div>

					<div>
						<label className="mb-1 block font-medium text-gray-700 text-sm">
							Terminal-ID
						</label>
						<select
							name="terminalId"
							value={filters.terminalId}
							onChange={handleFilterChange}
							className="w-full rounded border border-gray-300 px-3 py-2"
						>
							<option value="">Alle</option>
							{terminalIds.map((id) => (
								<option key={id} value={id}>
									{id}
								</option>
							))}
						</select>
					</div>

					<div>
						<label className="mb-1 block font-medium text-gray-700 text-sm">
							Mandant
						</label>
						<select
							name="mandantId"
							value={filters.mandantId}
							onChange={handleFilterChange}
							className="w-full rounded border border-gray-300 px-3 py-2"
						>
							<option value="">Alle</option>
							{availableMandants.map((mandant) => (
								<option key={mandant.id} value={mandant.id.toString()}>
									{mandant.name}
								</option>
							))}
						</select>
					</div>

					<div>
						<label className="mb-1 block font-medium text-gray-700 text-sm">
							Von Datum
						</label>
						<input
							type="date"
							name="dateFrom"
							value={filters.dateFrom}
							onChange={handleFilterChange}
							className="w-full rounded border border-gray-300 px-3 py-2"
						/>
					</div>

					<div>
						<label className="mb-1 block font-medium text-gray-700 text-sm">
							Bis Datum
						</label>
						<input
							type="date"
							name="dateTo"
							value={filters.dateTo}
							onChange={handleFilterChange}
							className="w-full rounded border border-gray-300 px-3 py-2"
						/>
					</div>
				</div>
			</div>

			{/* Log-Einträge */}
			<div className="space-y-4">
				{!loading && logs.length === 0 ? (
					<div className="rounded bg-white p-8 text-center shadow">
						<p className="text-gray-500">Keine Log-Einträge gefunden.</p>
						<p className="mt-2 text-gray-500 text-sm">
							Versuchen Sie die Filter anzupassen oder{" "}
							<button
								onClick={handleRefresh}
								className="text-blue-500 underline"
							>
								klicken Sie hier
							</button>
							, um die Daten neu zu laden.
						</p>
					</div>
				) : (
					logs.map((log) => (
						<div
							key={log.id}
							className="rounded bg-white p-4 shadow transition-all hover:shadow-md"
						>
							<div className="mb-2 flex flex-wrap justify-between gap-2">
								<div className="flex flex-wrap gap-2">
									{/* Zeitstempel */}
									<span className="text-gray-500 text-sm">
										{formatDateTime(log.createdAt)}
									</span>

									{/* Terminal-ID mit Init-Button */}
									<div className="flex items-center gap-1">
										<span className="rounded bg-gray-100 px-2 py-1 text-gray-800 text-sm">
											{log.terminalId}
										</span>
									</div>

									{/* Richtung */}
									<span className="rounded bg-purple-100 px-2 py-1 text-purple-800 text-sm">
										{formatDirection(log.direction)}
									</span>

									{/* Nachrichtentyp und URL (falls vorhanden) */}
									<div className="flex items-center gap-1">
										<span
											className={`rounded px-2 py-1 text-sm ${getMessageTypeClass(log.messageType)}`}
										>
											{log.messageType.toUpperCase()}
										</span>

										{/* URL aus dem Payload (falls vorhanden) */}
										{extractUrlFromPayload(log.payload) && (
											<span className="max-w-xs truncate text-gray-500 text-xs">
												{extractUrlFromPayload(log.payload)}
											</span>
										)}
									</div>

									{/* Payment Session ID (falls vorhanden) */}
									{log.paymentSessionId && (
										<span className="rounded bg-indigo-100 px-2 py-1 text-indigo-800 text-sm">
											Session: {log.paymentSessionId}
										</span>
									)}
								</div>

								{/* Mandantenname (falls vorhanden) */}
								{log.mandant && (
									<span className="font-medium text-gray-600 text-sm">
										Mandant: {log.mandant.name}
									</span>
								)}
							</div>

							{/* Toggle-Button für Payload */}
							<div className="mt-2">
								<button
									onClick={() => {
										const element = document.getElementById(
											`payload-${log.id}`,
										);
										if (element) {
											element.classList.toggle("hidden");
										}
									}}
									className="text-blue-600 text-sm hover:text-blue-800 focus:outline-none"
								>
									Payload anzeigen/ausblenden
								</button>

								{/* Payload als formatiertes JSON (standardmäßig ausgeblendet) */}
								<pre
									id={`payload-${log.id}`}
									className="scrollbar-thin mt-2 hidden max-h-60 overflow-x-auto rounded bg-gray-50 p-3 text-sm"
								>
									{formatPayload(log.payload)}
								</pre>
							</div>
						</div>
					))
				)}

				{/* Ladestatus und Infinite Scrolling */}
				{hasMore && !liveMode && logs.length > 0 && (
					<div ref={loadingRef} className="p-4 text-center">
						{loading ? (
							<div className="flex animate-pulse justify-center">
								<div className="mr-1 h-4 w-4 rounded-full bg-blue-600" />
								<div className="mr-1 h-4 w-4 animate-pulse rounded-full bg-blue-600 delay-100" />
								<div className="h-4 w-4 animate-pulse rounded-full bg-blue-600 delay-200" />
							</div>
						) : (
							<p className="text-gray-500 text-sm">Scrolle für mehr Logs</p>
						)}
					</div>
				)}
			</div>
		</div>
	);
}
