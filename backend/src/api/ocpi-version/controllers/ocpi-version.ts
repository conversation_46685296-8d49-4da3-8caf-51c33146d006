// backend/src/api/ocpi-version/controllers/ocpi-version.ts
'use strict';

module.exports = {
    async getVersions(ctx) {
        // Token-Validator-Service referenzieren
        const tokenValidator = strapi.service('api::ocpi-common.token-validator');

        // Token validieren
        const validationResult = await tokenValidator.validateRequestToken(ctx, 'any');

        if (!validationResult.valid) {
            return ctx.send(validationResult);
        }

        // Connection aus dem Validierungsergebnis extrahieren
        const connection = validationResult.connection;

        // Den Host aus der Strapi-Konfiguration holen, mit Fallback-Wert
        const publicurl = strapi.config.get('server.publicURL', 'localhost:1337');

        const response = {
            status_code: 1000,
            status_message: "Success",
            data: [
                {
                    version: "2.2.1",
                    url: `${publicurl}/api/ocpi/versions/2.2.1`
                }
            ],
            timestamp: new Date().toISOString()
        };

        ctx.send(response);
    }
};
