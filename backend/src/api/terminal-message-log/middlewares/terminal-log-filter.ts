/**
 * Terminal-Log-Filter Middleware
 *
 * Diese Middleware stellt sicher, dass Benutzer nur Logs für ihre eigenen Mandanten sehen können.
 * Sie fügt automatisch einen Filter für den aktiven Mandanten des Benutzers hinzu.
 */

export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Nur für Anfragen an den terminal-message-log Endpunkt
    if (!ctx.url.includes('/api/terminal-message-logs')) {
      return next();
    }

    // Nur für GET-Anfragen (Lesezugriff)
    if (ctx.method !== 'GET') {
      return next();
    }

    try {
      // Sicherstellen, dass ctx.query ein Objekt ist
      if (!ctx.query) {
        ctx.query = {};
      }

      // Filters initialisieren, falls nicht vorhanden
      if (!ctx.query.filters) {
        ctx.query.filters = {};
      }

      // Population der Mandanten aktivieren
      if (!ctx.query.populate) {
        ctx.query.populate = { mandant: true };
      } else if (typeof ctx.query.populate === 'object') {
        ctx.query.populate.mandant = true;
      }

      // Wenn ein spezifischer Mandanten-Filter gesetzt ist, verwenden wir diesen
      const hasMandantFilter = ctx.query.filters &&
                             (ctx.query.filters.mandant ||
                              ctx.query.filters.mandant?.$eq ||
                              ctx.query.filters['mandant.documentId'] ||
                              ctx.query.filters['mandant.documentId']?.$eq);

      // Wenn kein spezifischer Filter gesetzt ist, den Benutzer aus dem JWT-Token holen
      if (!hasMandantFilter) {
        try {
          // Den Benutzer aus dem JWT-Token holen
          const token = ctx.request.header.authorization?.replace('Bearer ', '');

          if (token) {
            try {
              // JWT Token auswerten
              const decoded = await strapi.plugins['users-permissions'].services.jwt.verify(token);
              const userId = decoded.id;

// Benutzer mit Mandant laden
              const user = await strapi.documents('plugin::users-permissions.user').findOne({
                documentId: userId,
              });

              // Wenn Benutzer einen Mandanten hat, Filter hinzufügen
              if (user?.mandant?.documentId) {
                const userMandantId = user.mandant.documentId;
                ctx.query.filters['mandant.documentId'] = userMandantId;
                console.log(`Terminal-Log-Filter: Filtering logs for mandant ${userMandantId}`);
              }
            } catch (jwtError) {
              console.error('Error verifying JWT token:', jwtError);
            }
          }
        } catch (tokenError) {
          console.error('Error parsing JWT token:', tokenError);
          // Bei Token-Fehler weitermachen ohne Filter
        }
      }
    } catch (error) {
      console.error('Error in terminal-log-filter middleware:', error);
    }

    // Zur nächsten Middleware/zum Controller weitergehen
    await next();
  };
};
