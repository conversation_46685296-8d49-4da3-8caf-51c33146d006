
// Definiere den Typ mit allen möglichen Eigenschaften für die Erfolgsantwort
export interface ResponseData {
    status_code: number;
    status_message: string;
    session_id: string;
    next_screen: string;
    payment_session_id?: string;
    card_info?: {
        maskedPan: string;
        cardId: string;
        brand?: string;
    };
}

// Definiere den Typ für die Fehlerantwort
export interface PayterErrorResponse {
    status: number;
    message: string;
    error: string;
    timestamp: string;
}


/**
 * UI Controller für Payter Terminals
 * Verwaltet die Anzeige von Screens auf dem Terminal
 */

/**
 * Interface für die Screens-Konfiguration mit Sprachunterstützung
 */
export interface ScreensConfig {
    screens: {
        [languageCode: string]: PayterScreen[];
    };
}

/**
 * Interface für einen Payter Screen
 */
export interface PayterScreen {
    id: string;
    type: 'message' | 'selection' | 'info' | 'status' | 'spinner';
    properties: PayterScreenProperties;
}

/**
 * Union-Typ für die verschiedenen Screen-Eigenschaften
 */
export type PayterScreenProperties =
    | MessageScreenProperties
    | SelectionScreenProperties
    | InfoScreenProperties
    | StatusScreenProperties
    | SpinnerScreenProperties;

/**
 * Interface für die Eigenschaften eines Message-Screens
 */
export interface MessageScreenProperties {
    title: string;
    message: string;
    icon: 'info' | 'approved' | 'success' | 'error';
    'buttons.ok'?: string;
    'buttons.ok.label'?: string;
    'buttons.cancel'?: string;
    'buttons.cancel.label'?: string;
    'buttons.retry'?: string;
    'buttons.retry.label'?: string;
}

/**
 * Interface für die Eigenschaften eines Selection-Screens
 */
export interface SelectionScreenProperties {
    title: string;
    message: string;
    type: string;
    [key: `items.cp${number}`]: string;
    [key: `items.cp${number}.label`]: string;
}

/**
 * Interface für die Eigenschaften eines Info-Screens
 */
export interface InfoScreenProperties {
    title: string;
    subtitle: string;
    qr: string;
    'buttons.ok'?: string;
    'buttons.ok.label'?: string;
}

/**
 * Interface für die Eigenschaften eines Status-Screens
 */
export interface StatusScreenProperties {
    title: string;
    message: string;
}

/**
 * Interface für die Eigenschaften eines Spinner-Screens
 */
export interface SpinnerScreenProperties {
    title: string;
    message: string;
    amount?: string;
    currency?: string;
}
