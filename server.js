// server.js
const http = require('http');

const PORT = 3045;

const server = http.createServer((req, res) => {
    console.log('Neue Anfrage empfangen:');
    console.log(`Methode: ${req.method}`);
    console.log(`URL: ${req.url}`);
    console.log('Header:', req.headers);

    let body = '';
    req.on('data', (chunk) => {
        body += chunk;
    });

    req.on('end', () => {
        if (body) {
            console.log('Body:', body);
        }
        // Sende eine einfache Antwort an den Client
        res.writeHead(200, { 'Content-Type': 'text/plain' });
        res.end('OK\n');
    });
});

server.listen(PORT, () => {
    console.log(`Server hört auf Port ${PORT}`);
});
