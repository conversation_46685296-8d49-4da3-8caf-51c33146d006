/**
 * terminal-message-log router
 */

export default {
  routes: [
    // Standard API-Routen
    {
      method: 'GET',
      path: '/terminal-message-logs',
      handler: 'terminal-message-log.find',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.find']
        }
      }
    },
    {
      method: 'GET',
      path: '/terminal-message-logs/count',
      handler: 'terminal-message-log.count',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.count']
        }
      }
    },
    {
      method: 'GET',
      path: '/terminal-message-logs/:id',
      handler: 'terminal-message-log.findOne',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.findOne']
        }
      }
    },
    {
      method: 'POST',
      path: '/terminal-message-logs',
      handler: 'terminal-message-log.create',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.create']
        }
      }
    },
    {
      method: 'PUT',
      path: '/terminal-message-logs/:id',
      handler: 'terminal-message-log.update',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.update']
        }
      }
    },
    {
      method: 'DELETE',
      path: '/terminal-message-logs/:id',
      handler: 'terminal-message-log.delete',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.delete']
        }
      }
    },

    // Benutzerdefinierte Routen
    {
      method: 'GET',
      path: '/terminal-message-logs/terminal-ids',
      handler: 'terminal-message-log.getTerminalIds',
      config: {
        auth: {
          scope: ['api::terminal-message-log.terminal-message-log.find']
        }
      }
    },
    {
      method: 'POST',
      path: '/terminal-message-log/log-to-terminal',
      handler: 'terminal-message-log.logToTerminal',
      config: {
        auth: false // API-Zugriff ohne Auth erlauben für Logging-Integration
      }
    },
    {
      method: 'POST',
      path: '/terminal-message-log/log-from-terminal',
      handler: 'terminal-message-log.logFromTerminal',
      config: {
        auth: false // API-Zugriff ohne Auth erlauben für Logging-Integration
      }
    },
    {
      method: 'POST',
      path: '/terminal-message-log/log-error',
      handler: 'terminal-message-log.logError',
      config: {
        auth: false // API-Zugriff ohne Auth erlauben für Logging-Integration
      }
    }
  ]
};