"use client";

import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useEffect, useState } from "react";
import {
	type EvseData,
	type LocationData,
	type TerminalData,
	evsesApi,
	locationsApi,
} from "~/services/api";

export interface TerminalEditFormData {
	id: number;
	documentId: string;
	serialNumber: string;
	terminalName: string;
	locationId?: string;
	locationDocumentId?: string;
	evseIds?: string[];
	evseDocumentIds?: string[];
}

interface TerminalEditModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSave: (data: TerminalEditFormData) => Promise<void>;
	terminal: TerminalData | null;
}

export default function TerminalEditModal({
	isOpen,
	onClose,
	onSave,
	terminal,
}: TerminalEditModalProps) {
	const [formData, setFormData] = useState<TerminalEditFormData>({
		id: 0,
		documentId: "",
		serialNumber: "",
		terminalName: "",
		locationId: undefined,
		locationDocumentId: undefined,
		evseIds: [],
		evseDocumentIds: [],
	});

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [locations, setLocations] = useState<LocationData[]>([]);
	const [evses, setEvses] = useState<EvseData[]>([]);

	// Lade Standorte und EVSEs beim Öffnen des Modals
	useEffect(() => {
		if (isOpen) {
			const loadData = async () => {
				setIsLoading(true);
				try {
					const [locationsResponse, evsesResponse] = await Promise.all([
						locationsApi.getAllByMandant(
							terminal?.mandant?.documentId,
							terminal?.payter_connection?.type,
						),
						evsesApi.getAll(),
					]);

					setLocations(locationsResponse.data);
					setEvses(evsesResponse.data);

					if (terminal) {
						// Formular mit Terminal-Daten initialisieren
						const formDataInit: TerminalEditFormData = {
							id: terminal.id,
							documentId: terminal.documentId,
							serialNumber: terminal.serialNumber,
							terminalName: terminal.terminalName || "",
							evseIds: terminal.evses?.map((evse) => evse.id.toString()) || [],
							evseDocumentIds:
								terminal.evses?.map((evse) => evse.documentId) || [],
							// Location ID wird separat gesucht
						};

						// Suche die Location, die dieses Terminal enthält
						for (const location of locationsResponse.data) {
							const terminalInLocation = location.terminals?.find(
								(t) => t.id === terminal.id,
							);
							if (terminalInLocation) {
								formDataInit.locationId = location.id.toString();
								formDataInit.locationDocumentId = location.documentId;
								break;
							}
						}

						setFormData(formDataInit);
					}
				} catch (err) {
					console.error("Fehler beim Laden der Daten:", err);
					setError(
						"Fehler beim Laden der Daten. Bitte versuchen Sie es später erneut.",
					);
				} finally {
					setIsLoading(false);
				}
			};

			loadData();
		}
	}, [isOpen, terminal]);

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
	) => {
		const { name, value } = e.target;

		// Wenn der Standort geändert wird, aktualisiere sowohl locationId als auch locationDocumentId
		if (name === "locationId") {
			const selectedLocation = locations.find(
				(loc) => loc.documentId === value,
			);
			setFormData((prev) => ({
				...prev,
				locationId: value,
				locationDocumentId: value, // documentId ist der gleiche Wert
			}));
		} else {
			setFormData((prev) => ({ ...prev, [name]: value }));
		}
	};

	const handleEvseChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
		const selectedOptions = Array.from(
			e.target.selectedOptions,
			(option) => option.value,
		);

		// Finde die entsprechenden documentIds für die ausgewählten EVSEs
		const selectedEvses = evses.filter((evse) =>
			selectedOptions.includes(evse.id.toString()),
		);
		const selectedDocumentIds = selectedEvses.map((evse) => evse.documentId);

		setFormData((prev) => ({
			...prev,
			evseIds: selectedOptions,
			evseDocumentIds: selectedDocumentIds,
		}));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError("");

		if (!formData.serialNumber || !formData.terminalName) {
			setError("Seriennummer und Terminal-Name sind Pflichtfelder.");
			return;
		}

		try {
			setIsLoading(true);
			await onSave(formData);
			onClose();
		} catch (err) {
			console.error("Fehler beim Speichern:", err);
			setError(
				"Fehler beim Speichern der Daten. Bitte versuchen Sie es später erneut.",
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Transition appear show={isOpen} as={Fragment}>
			<Dialog as="div" className="relative z-10" onClose={onClose}>
				<Transition.Child
					as={Fragment}
					enter="ease-out duration-300"
					enterFrom="opacity-0"
					enterTo="opacity-100"
					leave="ease-in duration-200"
					leaveFrom="opacity-100"
					leaveTo="opacity-0"
				>
					<div className="fixed inset-0 bg-black bg-opacity-25" />
				</Transition.Child>

				<div className="fixed inset-0 overflow-y-auto">
					<div className="flex min-h-full items-center justify-center p-4 text-center">
						<Transition.Child
							as={Fragment}
							enter="ease-out duration-300"
							enterFrom="opacity-0 scale-95"
							enterTo="opacity-100 scale-100"
							leave="ease-in duration-200"
							leaveFrom="opacity-100 scale-100"
							leaveTo="opacity-0 scale-95"
						>
							<Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
								<Dialog.Title
									as="h3"
									className="font-medium text-gray-900 text-lg leading-6"
								>
									Terminal bearbeiten
								</Dialog.Title>

								{error && (
									<div className="mt-2 rounded bg-red-100 p-2 text-red-700">
										{error}
									</div>
								)}

								<form onSubmit={handleSubmit}>
									<div className="mt-4">
										<label className="block font-medium text-gray-700 text-sm">
											Seriennummer
										</label>
										<input
											type="text"
											name="serialNumber"
											value={formData.serialNumber}
											onChange={handleInputChange}
											className="mt-1 block w-full rounded border border-gray-300 p-2"
											disabled // Seriennummer kann nicht geändert werden
										/>
									</div>

									<div className="mt-4">
										<label className="block font-medium text-gray-700 text-sm">
											Terminal Name (nicht änderbar)
										</label>
										<input
											type="text"
											name="terminalName"
											value={formData.terminalName}
											onChange={handleInputChange}
											className="mt-1 block w-full rounded border border-gray-300 bg-gray-100 p-2"
											disabled
											required
										/>
									</div>

									<div className="mt-4">
										<label className="block font-medium text-gray-700 text-sm">
											Standort
										</label>
										<select
											name="locationId"
											value={formData.locationId || ""}
											onChange={handleInputChange}
											className="mt-1 block w-full rounded border border-gray-300 p-2"
										>
											<option value="">Kein Standort ausgewählt</option>
											{locations.map((location) => (
												<option
													key={location.documentId}
													value={location.documentId}
												>
													{location.name}
												</option>
											))}
										</select>
									</div>

									<div className="mt-4">
										<label className="block font-medium text-gray-700 text-sm">
											EVSEs
										</label>
										<select
											multiple
											name="evseIds"
											value={formData.evseIds?.map(String) || []}
											onChange={handleEvseChange}
											className="mt-1 block h-32 w-full rounded border border-gray-300 p-2"
										>
											{evses.map((evse) => (
												<option key={evse.id} value={evse.id}>
													{evse.EvseId} - {evse.Charger}
												</option>
											))}
										</select>
										<p className="mt-1 text-gray-500 text-xs">
											Mehrere auswählen mit Strg/Cmd + Klick
										</p>
									</div>

									<div className="mt-6 flex justify-end space-x-3">
										<button
											type="button"
											onClick={onClose}
											className="rounded-md bg-gray-100 px-4 py-2 font-medium text-gray-700 text-sm hover:bg-gray-200"
										>
											Abbrechen
										</button>
										<button
											type="submit"
											disabled={isLoading}
											className="btn-primary disabled:opacity-50"
										>
											{isLoading ? "Wird gespeichert..." : "Speichern"}
										</button>
									</div>
								</form>
							</Dialog.Panel>
						</Transition.Child>
					</div>
				</div>
			</Dialog>
		</Transition>
	);
}
