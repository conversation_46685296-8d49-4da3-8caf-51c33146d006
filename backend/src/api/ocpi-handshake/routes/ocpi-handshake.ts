'use strict';

module.exports = {
    routes: [
        // Handshakes über Namen aufrufen
        {
            method: 'GET',
            path: '/ocpi/handshake/:name',
            handler: 'ocpi-handshake.handshake',
            config: {
                auth: false
            }
        },
        // Handshakes über DocumentID aufrufen
        {
            method: 'GET',
            path: '/ocpi/handshake/document/:documentId',
            handler: 'ocpi-handshake.handshakeByDocumentId',
            config: {
                auth: false
            }
        },
        // Status über Namen abrufen
        {
            method: 'GET',
            path: '/ocpi/handshake/status/:name',
            handler: 'ocpi-handshake.status',
            config: {
                auth: false
            }
        },
        // Status über DocumentID abrufen
        {
            method: 'GET',
            path: '/ocpi/handshake/status/document/:documentId',
            handler: 'ocpi-handshake.statusByDocumentId',
            config: {
                auth: false
            }
        }
    ]
};
