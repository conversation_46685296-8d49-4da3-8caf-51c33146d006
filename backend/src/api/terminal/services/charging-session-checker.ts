/**
 * Service zur Prüfung aktiver Ladevorgänge an EVSEs
 */

/**
 * Prüft, ob an einer EVSE bereits ein aktiver Ladevorgang läuft
 * @param evseDocumentId Die Document-ID der EVSE
 * @returns Promise<{hasActiveSession: boolean, paymentSession?: any}>
 */
export async function checkActiveChargingSession(evseDocumentId: string): Promise<{
  hasActiveSession: boolean;
  ocpiSession?: any;
}> {



  try {


    const evse = await strapi.documents('api::ocpi-evse.ocpi-evse').findOne({
      documentId: evseDocumentId,
    });
    const hasActiveSession = evse.ocpiStatus === 'CHARGING';
    if(hasActiveSession)
    {
      const ocpiSession = await strapi.documents('api::ocpi-session.ocpi-session').findFirst({
        filters: {
          evseUid: evse.uid,
          ocpiStatus: 'ACTIVE' // Nur aktive Sessions
        },
        sort: { startTime: 'desc' }, // Neueste zuerst
        populate: {
          mandant: true,
          payment_session: true
        }
      });

      return {hasActiveSession: hasActiveSession, ocpiSession: ocpiSession};



    }
    return {hasActiveSession: false};

  } catch (error) {
    console.error(`Fehler beim Prüfen der aktiven Ladesession für EVSE ${evseDocumentId}:`, error);
    return {
      hasActiveSession: false
    };
  }
}

/**
 * Stoppt eine aktive Ladesession
 * @param paymentSessionId Die ID der Payment-Session
 * @returns Promise<{success: boolean, message?: string}>
 */
export async function stopChargingSession(ocpiSession): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    // Hole die Payment-Session mit allen relevanten Daten


    // Verwende den OCPI STOP_SESSION Command
    const stopSessionResult = await strapi.service('api::ocpi-command.ocpi-command').stopSession({
      session_id: ocpiSession?.sessionId,
      evse_id: ocpiSession.evseId,
      mandant_id: ocpiSession.mandantId,
    });



    if (stopSessionResult.success) {
      // Aktualisiere die Payment-Session
      /*await strapi.documents('api::payment-session.payment-session').update({
        documentId: paymentSessionId,
        data: {
          paymentSessionState: 'canceled',
          closedAt: new Date()
        }
      });*/

      return {
        success: true,
        message: 'Ladevorgang erfolgreich gestoppt'
      };
    } else {
      return {
        success: false,
        message: stopSessionResult.response?.status_message || 'Fehler beim Stoppen des Ladevorgangs'
      };
    }
  } catch (error) {
    console.error(`Fehler beim Stoppen der Ladesession ${ocpiSession.session_id}:`, error);
    return {
      success: false,
      message: 'Unerwarteter Fehler beim Stoppen des Ladevorgangs'
    };
  }
}

export default {
  checkActiveChargingSession,
  stopChargingSession
};
