/**
 * Payter API Service
 *
 * Dieser Service stellt Methoden zur Kommunikation mit der Payter API bereit.
 * Die API-Schlüssel und Basis-URLs werden aus den entsprechenden Quellen geladen.
 */
import axios, {
	type AxiosInstance,
	type AxiosRequestConfig,
	type AxiosResponse,
} from "axios";
import { payterConnectionsApi } from "~/services/api";
import { terminalLogger } from "~/services/terminal-logger";

export interface ApiUser {
	terminals: string[];
	terminalStateWebHookUrl: string;
	terminalStateWebHookStates: string[];
}

export interface PayterTerminal {
	serialNumber: string;
	terminalName: string;
	terminalIds: number;
	apiUsers: ApiUser[];
	error: string;
	online: boolean;
	state: string; // Beispiel: "IDLE", evtl. auch weitere Zustände
}

// Klassenbasiertes Singleton-Muster für den PayterApiService
export class PayterApiService {
	private static instance: PayterApiService;
	private apiClients: { [key: string]: AxiosInstance } = {};

	// Private Konstruktor verhindert direkte Instanziierung von außen
	private constructor() {}

	/**
	 * Gibt die Singleton-Instanz des PayterApiService zurück
	 * oder erstellt eine neue, falls noch keine existiert
	 */
	public static getInstance(): PayterApiService {
		if (!PayterApiService.instance) {
			PayterApiService.instance = new PayterApiService();
		}
		return PayterApiService.instance;
	}

	/**
	 * Client für eine bestimmte Umgebung holen oder erstellen
	 * @param environment Die Umgebung (test oder prod)
	 * @param activeMandantId Die ID des aktiven Mandanten (optional)
	 */
	public async getApiClient(
		environment: "test" | "prod",
		activeMandantId?: number,
	): Promise<AxiosInstance | null> {
		// Erstelle einen Cache-Key, der die Umgebung und den Mandanten berücksichtigt
		const cacheKey = activeMandantId
			? `${environment}_${activeMandantId}`
			: environment;

		// Wenn Client bereits im Cache ist, zurückgeben
		const cachedClient = this.apiClients[cacheKey];
		if (cachedClient) {
			return cachedClient;
		}

		try {
			// Parameter für die API-Anfrage vorbereiten
			const params: Record<string, any> = {
				"filters[type][$eq]":
					environment.charAt(0).toUpperCase() + environment.slice(1),
			};

			// Wenn ein Mandant angegeben ist, nach Verbindungen für diesen Mandanten filtern
			if (activeMandantId) {
				params["filters[mandants][id][$eq]"] = activeMandantId;
			}

			// Payter-Verbindungen mit Filtern abrufen
			const payterConnections = await payterConnectionsApi.getAll(params);

			// Wenn keine Verbindung gefunden wurde, versuche es ohne Mandanten-Filter
			let connection = payterConnections.data[0];
			if (!connection && activeMandantId) {
				// Versuche es ohne Mandanten-Filter, nur mit Umgebungsfilter
				const fallbackParams = {
					"filters[type][$eq]":
						environment.charAt(0).toUpperCase() + environment.slice(1),
				};
				const fallbackConnections =
					await payterConnectionsApi.getAll(fallbackParams);
				connection = fallbackConnections.data[0];
			}

			if (!connection) {
				console.error(
					`Keine Payter-Verbindung für Umgebung ${environment}${activeMandantId ? ` und Mandant ${activeMandantId}` : ""} gefunden.`,
				);
				return null;
			}

			// Prüfe, ob die Verbindung eine API-URL und einen API-Key hat
			if (!connection.apiUrl) {
				console.error("Keine API-URL für die Payter-Verbindung gefunden.");
				return null;
			}

			if (!connection.apiKey) {
				console.error("Kein API-Key für die Payter-Verbindung gefunden.");
				return null;
			}

			// Client erstellen und im Cache speichern
			const client = axios.create({
				baseURL: connection.apiUrl,
				headers: {
					"Content-Type": "application/json",
					Authorization: `${connection.apiKey}`,
				},
			});

			// Interceptors für das Logging der Kommunikation
			this.setupLoggingInterceptors(client, connection.id);

			this.apiClients[cacheKey] = client;
			return client;
		} catch (error) {
			console.error("Fehler beim Erstellen des API-Clients:", error);
			return null;
		}
	}

	/**
	 * Interceptors für das Logging der Kommunikation einrichten
	 */
	private setupLoggingInterceptors(
		client: AxiosInstance,
		connectionId: number,
	) {
		// Request Interceptor
		client.interceptors.request.use(
			async (config) => {
				try {
					// Extrahiere Terminal-ID aus der URL oder config
					const terminalId = this.extractTerminalId(config);
					if (terminalId) {
						// Speichere die Request-ID in der Config für spätere Verwendung
						const requestId = await terminalLogger.logToTerminal({
							terminalId: terminalId,
							payload: config.data || {},
							mandantId: connectionId,
						});

						// Speichere die Request-ID in der Config für spätere Verwendung
						config.headers = config.headers || {};
						config.headers["X-Request-ID"] = requestId;
					}
				} catch (error) {
					// Logging-Fehler nicht weitergeben
					console.error("Fehler beim Logging der Terminal-Anfrage:", error);
				}
				return config;
			},
			(error) => {
				return Promise.reject(error);
			},
		);

		// Response Interceptor
		client.interceptors.response.use(
			async (response) => {
				try {
					// Extrahiere Terminal-ID aus der URL oder response.config
					const terminalId = this.extractTerminalId(response.config);
					if (terminalId) {
						// Hole die Request-ID aus dem Header, falls vorhanden
						const requestId = response.config.headers?.[
							"X-Request-ID"
						] as string;

						// Verwende den Terminal-Logger zum Loggen der eingehenden Antwort
						await terminalLogger.logFromTerminal(
							{
								terminalId: terminalId,
								payload: null,
								responsePayload: response.data || {},
								mandantId: connectionId,
							},
							requestId,
						);
					}
				} catch (error) {
					// Logging-Fehler nicht weitergeben
					console.error("Fehler beim Logging der Terminal-Antwort:", error);
				}
				return response;
			},
			async (error) => {
				try {
					if (error.config) {
						// Extrahiere Terminal-ID aus der URL oder error.config
						const terminalId = this.extractTerminalId(error.config);
						if (terminalId) {
							// Hole die Request-ID aus dem Header, falls vorhanden
							const requestId = error.config.headers?.[
								"X-Request-ID"
							] as string;

							// Extrahiere die ursprüngliche Anfrage aus dem Request-Body
							const originalRequest = error.config.data
								? typeof error.config.data === "string"
									? JSON.parse(error.config.data)
									: error.config.data
								: null;

							// Verwende den Terminal-Logger zum Loggen des Fehlers
							await terminalLogger.logError({
								terminalId: terminalId,
								payload: originalRequest,
								responsePayload: {
									message: error.message,
									response: error.response?.data,
									status: error.response?.status,
								},
								mandantId: connectionId,
							});
						}
					}
				} catch (logError) {
					// Logging-Fehler nicht weitergeben
					console.error("Fehler beim Logging des Terminal-Fehlers:", logError);
				}
				return Promise.reject(error);
			},
		);
	}

	/**
	 * Extrahiert die Terminal-ID aus der URL oder der Anfrage-Konfiguration
	 */
	private extractTerminalId(config: AxiosRequestConfig): string | null {
		try {
			// Versuche Terminal-ID aus URL zu extrahieren
			const url = config.url || "";
			const urlMatch = url.match(/\/terminals\/([^\/]+)/);
			if (urlMatch?.[1]) {
				return urlMatch[1];
			}

			// Versuche Terminal-ID aus den Anfragedaten zu extrahieren
			if (config.data && typeof config.data === "object") {
				const data = config.data as any;
				if (data.terminalId || data.serialNumber || data.terminal_id) {
					return data.terminalId || data.serialNumber || data.terminal_id;
				}
			}

			// Keine Terminal-ID gefunden
			return null;
		} catch (error) {
			console.error("Fehler beim Extrahieren der Terminal-ID:", error);
			return null;
		}
	}

	/**
	 * Alle verfügbaren Terminals abrufen
	 * @param environment Die Umgebung (test oder prod)
	 * @param activeMandantId Die ID des aktiven Mandanten (optional)
	 */
	public async getTerminals(
		environment: "test" | "prod" = "prod",
		activeMandantId?: number,
	): Promise<PayterTerminal[]> {
		try {
			// Client mit Mandanten-ID holen
			const client = await this.getApiClient(environment, activeMandantId);
			if (!client) {
				// Wenn kein Client verfügbar ist, leere Liste zurückgeben
				return [];
			}

			// Terminals von der Payter API abrufen
			const response = await client.get("/terminals");
			return response.data;
		} catch (error) {
			console.error("Fehler beim Abrufen der Terminals:", error);
			return [];
		}
	}
}

// Exportiere eine Zugriffsfunktion für einfacheren Zugriff auf die Singleton-Instanz
export const payterApiService = PayterApiService.getInstance();
