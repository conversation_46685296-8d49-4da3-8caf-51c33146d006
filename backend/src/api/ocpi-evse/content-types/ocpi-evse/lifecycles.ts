/**
 * OCPI EVSE Lifecycles
 * Enthält Lifecycle-Hooks für das OCPI-EVSE-Modell
 */

import { updateTerminalScreenByLocationStatus } from '../../../terminal/services/terminal-status';

export default {
  /**
   * Wird nach dem Aktualisieren eines EVSE aufgerufen
   */
  async afterUpdate(event) {
    const { result } = event;
    
    // Prüfe, ob sich der Status geändert hat
    if (result && result.ocpiStatus) {
      try {
        // Lade die vollständigen EVSE-Daten mit der Location
        const evse = await strapi.documents('api::ocpi-evse.ocpi-evse').findOne({
          documentId: result.documentId,
          populate: {
            location: true
          }
        });

        if (evse && evse.location && evse.location.ocpiId) {
          // Aktualisiere den Terminal-Screen basierend auf dem Status der EVSEs an der Location
          await updateTerminalScreenByLocationStatus(evse.location.ocpiId);
          
          // Logge die Aktualisierung
          await strapi.service('api::terminal-message-log.terminal-message-log').log({
            terminalId: 'system',
            direction: 'System',
            payload: {
              message: `Terminal-Screen für Location ${evse.location.ocpiId} aktualisiert aufgrund von Statusänderung des EVSE ${evse.uid} auf ${evse.ocpiStatus}`
            },
            messageType: 'info'
          });
        }
      } catch (error) {
        console.error('Fehler beim Aktualisieren des Terminal-Screens nach EVSE-Update:', error);
      }
    }
  }
};
