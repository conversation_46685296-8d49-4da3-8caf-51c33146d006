{"collectionName": "components_ocpi_images", "info": {"displayName": "Image", "description": "OCPI 2.2.1 Image object"}, "options": {}, "attributes": {"url": {"type": "string", "required": true, "comment": "URL from where the image data can be retrieved"}, "thumbnail": {"type": "string", "comment": "URL from where a thumbnail of the image can be retrieved"}, "category": {"type": "enumeration", "enum": ["CHARGER", "ENTRANCE", "LOCATION", "NETWORK", "OPERATOR", "OTHER", "OWNER"], "comment": "Image category"}, "type": {"type": "string", "comment": "MIME type of the image"}, "width": {"type": "integer", "comment": "Width of the image in pixels"}, "height": {"type": "integer", "comment": "Height of the image in pixels"}}}