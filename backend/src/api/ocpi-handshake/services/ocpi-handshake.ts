'use strict';

import { randomBytes } from 'crypto';
import axios from 'axios';


// OCPI Typdefinitionen
interface OcpiResponse<T> {
    status_code: number;
    status_message: string;
    data?: T;
    timestamp: string;
}

interface OcpiVersion {
    version: string;
    url: string;
}

interface OcpiVersionDetail {
    version: string;
    endpoints: OcpiEndpoint[];
}

interface OcpiEndpoint {
    identifier: string;
    role: string;
    url: string;
}

interface OcpiCredentialRole {
    role: string;
    business_details: {
        name: string;
        website?: string;
        logo?: {
            url?: string;
            thumbnail?: string;
            category?: string;
            type?: string;
            width?: string | number;
            height?: string | number;
        };
    };
    party_id: string;
    country_code: string;
}

interface OcpiCredential {
    token: string;
    url: string;
    roles: OcpiCredentialRole[];
}

/**
 * OCPI Handshake Service
 * Bietet Funktionen für den kompletten OCPI-Handshake-Prozess
 */
module.exports = {
    /**
     * Führt einen vollständigen OCPI-Handshake mit der Gegenstelle durch
     * @param {string} connectionIdentifier - Name oder documentId der OCPI Connection
     * @param {boolean} useDocumentId - Wenn true, wird connectionIdentifier als documentId behandelt, sonst als Name
     * @param {number|null} mandantId - Optional: ID des Mandanten aus dem Kontext
     * @returns {Promise<object>} - Erfolgs- oder Fehlerobjekt
     */
    async performHandshake(connectionIdentifier, useDocumentId = true, mandantId = null) {
        try {
            // Logger-Service referenzieren für detaillierte Protokollierung
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            await logger.info(`Starte OCPI Handshake für Connection: ${connectionIdentifier}`, {
                endpoint: 'handshake'
            });


            const connection = await strapi.documents('api::ocpi-connection.ocpi-connection').findOne({
                documentId: connectionIdentifier,
                populate: ['mandants']
            });

            if (!connection) {
                const searchType = useDocumentId ? "ID" : "Name";
                await logger.error(`OCPI Connection mit ${searchType} "${connectionIdentifier}" nicht gefunden`, {
                    endpoint: 'handshake'
                });

                return {
                    success: false,
                    status_code: 3002,
                    status_message: `OCPI Connection mit ${searchType} "${connectionIdentifier}" nicht gefunden`
                };
            }


            // 2. Verbindungsstatus prüfen
            if (connection.connectionStatus === 'active') {
                await logger.info(`OCPI Connection ${connection.name} ist bereits aktiv`, {
                    endpoint: 'handshake',
                });

                return {
                    success: true,
                    status_code: 1000,
                    status_message: "Verbindung ist bereits aktiv",
                    connection: {
                        id: connection.documentId,
                        name: connection.name,
                        status: connection.connectionStatus
                    }
                };
            }

            // 3. Verbindungsstatus auf 'pending' setzen
            await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                documentId: connection.documentId,
                data: {
                    connectionStatus: 'pending',
                    lastConnection: new Date().toISOString()
                }
            });

            await logger.info(`OCPI Connection ${connection.name} Status auf 'pending' gesetzt`, {
                endpoint: 'handshake',
            });

            // 4. Versions-Endpunkt der Gegenstelle abfragen
            const versionsUrl = connection.connectionUrl;
            const initialToken = connection.initialSecret;

            if (!versionsUrl || !initialToken) {
                await logger.error(`Fehlende URL oder InitialSecret für Connection ${connection.name}`, {
                    endpoint: 'handshake',
                });

                return {
                    success: false,
                    status_code: 3003,
                    status_message: "Fehlende URL oder InitialSecret"
                };
            }

            // Schritt 1: GET /ocpi/versions mit initialSecret als Token
            await logger.info(`Rufe Versions-Endpunkt auf: ${versionsUrl}`, {
                endpoint: 'handshake/versions',
            });

            let versionsResponse;
            try {
                versionsResponse = await axios.get(versionsUrl, {
                    headers: {
                        'Authorization': `Token ${initialToken}`
                    }
                });
            } catch (error) {
                await logger.error(`Fehler beim Abrufen der Versions: ${error.message}`, {
                    endpoint: 'handshake/versions',
                });

                return {
                    success: false,
                    status_code: 3004,
                    status_message: `Fehler beim Abrufen der Versions: ${error.message}`
                };
            }

            // Prüfen, ob die Antwort gültig ist
            const versionsData = versionsResponse.data as OcpiResponse<OcpiVersion[]>;
            if (!versionsData || !versionsData.data || !Array.isArray(versionsData.data)) {
                await logger.error(`Ungültige Versions-Antwort: ${JSON.stringify(versionsData)}`, {
                    endpoint: 'handshake/versions',
                });

                return {
                    success: false,
                    status_code: 3005,
                    status_message: "Ungültige Versions-Antwort"
                };
            }

            // 5. Passende Version finden (2.2.1)
            const targetVersion = connection.ocpiVersion || 'v221';
            const versionMapping = {
                'v221': '2.2.1',
                'v22': '2.2',
                'v211': '2.1.1'
            };

            const ocpiVersionString = versionMapping[targetVersion] || '2.2.1';

            const matchingVersion = versionsData.data.find(v => v.version === ocpiVersionString);
            if (!matchingVersion) {
                await logger.error(`Keine passende Version ${ocpiVersionString} gefunden`, {
                    endpoint: 'handshake/versions',
                });

                return {
                    success: false,
                    status_code: 3006,
                    status_message: `Keine passende Version ${ocpiVersionString} gefunden`
                };
            }

            // 6. Details der Version abrufen
            await logger.info(`Rufe Details für Version ${ocpiVersionString} ab: ${matchingVersion.url}`, {
                endpoint: 'handshake/version_details',
            });

            let versionDetailsResponse;
            try {
                versionDetailsResponse = await axios.get(matchingVersion.url, {
                    headers: {
                        'Authorization': `Token ${initialToken}`
                    }
                });
            } catch (error) {
                await logger.error(`Fehler beim Abrufen der Version-Details: ${error.message}`, {
                    endpoint: 'handshake/version_details',
                });

                return {
                    success: false,
                    status_code: 3007,
                    status_message: `Fehler beim Abrufen der Version-Details: ${error.message}`
                };
            }

            // Prüfen, ob die Antwort gültig ist
            const versionDetailsData = versionDetailsResponse.data as OcpiResponse<OcpiVersionDetail>;
            if (!versionDetailsData || !versionDetailsData.data || !versionDetailsData.data.endpoints) {
                await logger.error(`Ungültige Version-Details-Antwort: ${JSON.stringify(versionDetailsData)}`, {
                    endpoint: 'handshake/version_details',
                });

                return {
                    success: false,
                    status_code: 3008,
                    status_message: "Ungültige Version-Details-Antwort"
                };
            }

            // 7. Credentials-Endpunkt finden
            const credentialsEndpoint = versionDetailsData.data.endpoints.find(e =>
                e.identifier.toLowerCase() === 'credentials'
            );

            if (!credentialsEndpoint) {
                await logger.error(`Kein Credentials-Endpunkt gefunden`, {
                    endpoint: 'handshake/version_details',
                });

                return {
                    success: false,
                    status_code: 3009,
                    status_message: "Kein Credentials-Endpunkt gefunden"
                };
            }

            // 8. Alle Endpunkte speichern
            await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                documentId: connection.documentId, // Note: uses documentId instead of id
                data: {
                    remoteModules: JSON.stringify(versionDetailsData.data.endpoints),
                    ocpiVersion: targetVersion
                }
            });

            await logger.info(`Remote-Module gespeichert: ${JSON.stringify(versionDetailsData.data.endpoints)}`, {
                endpoint: 'handshake/version_details',
            });

            // 9. Falls kein ReceivingSecret vorhanden ist, generiere eines und speichere es SOFORT in der Datenbank
            let receivingSecret = connection.receivingSecret;
            if (!receivingSecret) {
                receivingSecret = randomBytes(16).toString('hex');

                // WICHTIG: Das neue ReceivingSecret wird sofort in die Datenbank geschrieben,
                // bevor es im Credentials-Request verwendet wird
                await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                    documentId: connection.documentId,
                    data: {
                        receivingSecret: receivingSecret
                    }
                });

                await logger.info(`Neues ReceivingSecret generiert und in Datenbank gespeichert: ${receivingSecret}`, {
                    endpoint: 'handshake/credentials',
                });
            }

            // 10. Credentials-Request vorbereiten
            const serverUrl = strapi.config.get('server.publicURL', 'localhost:1337');
            const versionsEndpoint = `${serverUrl}/api/ocpi/versions`;

            const credentialsRequest: OcpiCredential = {
                token: receivingSecret,
                url: versionsEndpoint,
                roles: [{
                    role: 'EMSP',
                    party_id: connection.partyId || 'EUL',
                    country_code: connection.countryCode || 'DE',
                    business_details: {
                        name: connection.companyName || 'Eulektro GmbH'
                    }
                }]
            };

            // 11. Credentials-Request senden
            await logger.info(`Sende Credentials-Request an: ${credentialsEndpoint.url}`, {
                endpoint: 'handshake/credentials',
            });

            let credentialsResponse;
            try {
                credentialsResponse = await axios.post(credentialsEndpoint.url, credentialsRequest, {
                    headers: {
                        'Authorization': `Token ${initialToken}`,
                        'Content-Type': 'application/json'
                    }
                });
            } catch (error) {
                await logger.error(`Fehler beim Senden der Credentials: ${error.message}`, {
                    endpoint: 'handshake/credentials',
                });

                return {
                    success: false,
                    status_code: 3010,
                    status_message: `Fehler beim Senden der Credentials: ${error.message}`
                };
            }

            // Prüfen, ob die Antwort gültig ist
            const credentialsData = credentialsResponse.data as OcpiResponse<OcpiCredential>;
            if (!credentialsData || !credentialsData.data || !credentialsData.data.token) {
                await logger.error(`Ungültige Credentials-Antwort: ${JSON.stringify(credentialsData)}`, {
                    endpoint: 'handshake/credentials',
                });

                return {
                    success: false,
                    status_code: 3011,
                    status_message: "Ungültige Credentials-Antwort"
                };
            }

            // 12. Verbindung aktualisieren
            await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                documentId:  connection.documentId,
                data: {
                    sendSecret: credentialsData.data.token,
                    connectionStatus: 'active',
                    lastConnection: new Date().toISOString(),
                    remoteParty: JSON.stringify(credentialsData.data.roles || [])
                }
            });

            await logger.info(`OCPI-Handshake erfolgreich abgeschlossen für Connection ${connection.name}`, {
                endpoint: 'handshake',
            });

            // 13. Erfolg zurückgeben
            return {
                success: true,
                status_code: 1000,
                status_message: "OCPI-Handshake erfolgreich",
                connection: {
                    id: connection.id,
                    name: connection.name,
                    status: 'active'
                }
            };
        } catch (error) {
            // Allgemeiner Fehler
            const logger = strapi.service('api::ocpi-log.ocpi-logger');
            await logger.error(`Unerwarteter Fehler beim OCPI-Handshake: ${error.message}`, {
                endpoint: 'handshake'
            });

            return {
                success: false,
                status_code: 3000,
                status_message: `Unerwarteter Fehler: ${error.message}`
            };
        }
    },

    /**
     * Trennt eine OCPI-Verbindung
     * @param {string} connectionIdentifier - Name oder documentId der OCPI Connection
     * @param {boolean} useDocumentId - Wenn true, wird connectionIdentifier als documentId behandelt, sonst als Name
     * @param {number|null} mandantId - Optional: ID des Mandanten aus dem Kontext
     * @returns {Promise<object>} - Erfolgs- oder Fehlerobjekt
     */
    async disconnectConnection(connectionIdentifier, useDocumentId = true, mandantId = null) {
        try {
            // Logger-Service referenzieren für detaillierte Protokollierung
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            await logger.info(`Trenne OCPI Connection: ${connectionIdentifier}`, {
                endpoint: 'disconnect'
            });

            // 1. OCPI Connection laden
            const whereCondition = useDocumentId
                ? { id: connectionIdentifier }
                : { name: connectionIdentifier };

            const connection = await strapi.db.query('api::ocpi-connection.ocpi-connection').findOne({
                where: whereCondition,
                populate: ['mandant']
            });

            if (!connection) {
                const searchType = useDocumentId ? "ID" : "Name";
                await logger.error(`OCPI Connection mit ${searchType} "${connectionIdentifier}" nicht gefunden`, {
                    endpoint: 'disconnect'
                });

                return {
                    success: false,
                    status_code: 3002,
                    status_message: `OCPI Connection mit ${searchType} "${connectionIdentifier}" nicht gefunden`
                };
            }

            // 2. Verbindungsstatus prüfen
            if (connection.connectionStatus !== 'active') {
                await logger.info(`OCPI Connection ${connection.name} ist nicht aktiv (Status: ${connection.connectionStatus})`, {
                    endpoint: 'disconnect',
                });

                return {
                    success: true,
                    status_code: 1000,
                    status_message: `Verbindung ist bereits inaktiv (Status: ${connection.connectionStatus})`,
                    connection: {
                        id: connection.id,
                        name: connection.name,
                        status: connection.connectionStatus
                    }
                };
            }

            // 3. DELETE-Request an den OCPI-Endpunkt senden, um die Verbindung auch auf der Gegenseite zu löschen
            try {
                // Credentials-Endpunkt aus den remoteModules extrahieren
                let credentialsUrl = null;
                if (connection.remoteModules && typeof connection.remoteModules === 'string') {
                    try {
                        const remoteModules = JSON.parse(connection.remoteModules) as Array<{identifier: string; url: string}>;
                        const credentialsModule = remoteModules.find(m => m.identifier.toLowerCase() === 'credentials');
                        if (credentialsModule) {
                            credentialsUrl = credentialsModule.url;
                        }
                    } catch (parseError) {
                        await logger.error(`Fehler beim Parsen der remoteModules: ${parseError.message}`, {
                            endpoint: 'disconnect',
                            mandantId
                        });
                    }
                }

                if (!credentialsUrl) {
                    await logger.warn(`Keine Credentials-URL für Connection ${connection.name} gefunden`, {
                        endpoint: 'disconnect',
                        mandantId
                    });
                } else {
                    // Daten für den DELETE-Request vorbereiten
                    const serverUrl = process.env.SERVER_URL || 'http://localhost:1337';
                    const versionsEndpoint = `${serverUrl}/api/ocpi/versions`;

                    const deleteData = {
                        token: connection.receivingSecret,
                        url: versionsEndpoint,
                        roles: [{
                            role: 'EMSP',
                            party_id: connection.partyId || 'EUL',
                            country_code: connection.countryCode || 'DE',
                            business_details: {
                                name: connection.companyName || 'Eulektro GmbH'
                            }
                        }]
                    };

                    // DELETE-Request senden
                    await logger.info(`Sende DELETE-Request an: ${credentialsUrl}`, {
                        endpoint: 'disconnect',
                        mandantId
                    });

                    // Axios ist bereits am Anfang der Datei importiert
                    await axios.delete(credentialsUrl, {
                        headers: {
                            'Authorization': `Token ${connection.sendSecret}`,
                            'Content-Type': 'application/json'
                        },
                        data: deleteData // Wichtig: Bei axios muss der Body für DELETE-Requests im 'data'-Feld übergeben werden
                    });

                    await logger.info(`DELETE-Request erfolgreich gesendet`, {
                        endpoint: 'disconnect',
                        mandantId
                    });
                }
            } catch (deleteError) {
                // Fehler beim DELETE-Request loggen, aber den Prozess fortsetzen
                await logger.error(`Fehler beim Senden des DELETE-Requests: ${deleteError.message}`, {
                    endpoint: 'disconnect',
                    mandantId
                });
                // Wir setzen den Prozess fort, auch wenn der DELETE-Request fehlschlägt
            }

            // 4. Verbindung in der Datenbank aktualisieren
            await strapi.db.query('api::ocpi-connection.ocpi-connection').update({
                where: { id: connection.id },
                data: {
                    connectionStatus: 'inactive',
                    lastConnection: new Date().toISOString()
                }
            });

            await logger.info(`OCPI Connection ${connection.name} Status auf 'inactive' gesetzt`, {
                endpoint: 'disconnect',
                mandantId
            });

            // 4. Erfolg zurückgeben
            return {
                success: true,
                status_code: 1000,
                status_message: "OCPI-Verbindung erfolgreich getrennt",
                connection: {
                    id: connection.id,
                    name: connection.name,
                    status: 'inactive'
                }
            };
        } catch (error) {
            // Allgemeiner Fehler
            const logger = strapi.service('api::ocpi-log.ocpi-logger');
            await logger.error(`Unerwarteter Fehler beim Trennen der OCPI-Verbindung: ${error.message}`, {
                endpoint: 'disconnect'
            });

            return {
                success: false,
                status_code: 3000,
                status_message: `Unerwarteter Fehler: ${error.message}`
            };
        }
    }
};
