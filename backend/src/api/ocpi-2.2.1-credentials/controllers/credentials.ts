// src/api/ocpi/2.2.1/credentials/controllers/credential.ts
'use strict';

import { randomBytes } from 'crypto';

interface VersionDetails {
    version: string;
    url: string;
}

interface ModuleEndpoint {
    identifier: string;
    role: string;
    url: string;
}

interface CredentialResponse {
    token: string;
    url: string;
    roles?: {
        role: string;
        business_details: {
            name: string;
        };
        party_id: string;
        country_code: string;
    }[];
    versions?: VersionDetails[];
}

// OCPI Typdefinitionen
interface OcpiResponse<T> {
    status_code: number;
    status_message: string;
    data?: T;
    timestamp: string;
}

interface OcpiVersion {
    version: string;
    url: string;
}

interface OcpiVersionDetail {
    version: string;
    endpoints: ModuleEndpoint[];
}

/**
 * OCPI Credentials Controller
 * Implementiert die OCPI 2.2.1 Credential-Endpunkte
 */
module.exports = {
    /**
     * GET /ocpi/2.2.1/credentials
     * Gibt die aktuellen Credentials und Verbindungsinformationen zurück
     */
    async getCredentials(ctx) {
        try {
            // Authentifizierung prüfen
            const authHeader = ctx.request.headers.authorization || '';
            const tokenPrefix = "Token ";

            if (!authHeader.startsWith(tokenPrefix)) {
                return ctx.send({
                    status_code: 2000,
                    status_message: "Unauthorized",
                    timestamp: new Date().toISOString()
                });
            }

            const providedToken = authHeader.substring(tokenPrefix.length);

            // OCPI-Verbindungen laden, die dieses Token als ReceivingSecret haben
            const ocpiConnection = await strapi.db.query('api::ocpi-connection.ocpi-connection').findOne({
                where: { ReceivingSecret: providedToken }
            });

            if (!ocpiConnection) {
                return ctx.send({
                    status_code: 2000,
                    status_message: "Invalid token",
                    timestamp: new Date().toISOString()
                });
            }

            // Neues Token generieren (Token C)
            const newToken = randomBytes(16).toString('hex');

            // Verbindung aktualisieren mit neuem Token
            await strapi.db.query('api::ocpi-connection.ocpi-connection').update({
                where: { id: ocpiConnection.id },
                data: {
                    ReceivingSecret: newToken
                }
            });

            // Host aus der Strapi-Konfiguration holen
            const publicUrl = strapi.config.get('server.publicURL', 'localhost:1337');

            // Antwort erstellen gemäß OCPI 2.2.1
            const response = {
                status_code: 1000,
                status_message: "Success",
                data: {
                    token: newToken,
                    url: `${publicUrl}/api/ocpi/versions`,
                    roles: [
                        {
                            role: "CPO",
                            business_details: {
                                name: "EulektroTerminalVerwaltung CPO"
                            },
                            party_id: "EVT",
                            country_code: "DE"
                        }
                    ]
                },
                timestamp: new Date().toISOString()
            };

            return ctx.send(response);
        } catch (error) {
            strapi.log.error('Error in getCredentials:', error);
            return ctx.send({
                status_code: 3000,
                status_message: "Server error",
                timestamp: new Date().toISOString()
            });
        }
    },

    /**
     * POST /ocpi/2.2.1/credentials
     * Empfängt und speichert neue Credentials von der Gegenstelle
     */
    async postCredentials(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'any');

            // Request-Body validieren
            const { token, url, roles } = ctx.request.body as { token: string; url: string; roles?: any[] };

            if (!token || !url) {
                return ctx.send({
                    status_code: 4000,
                    status_message: "Missing required fields",
                    timestamp: new Date().toISOString()
                });
            }

            // Verbindungsdaten von der Gegenstelle abrufen
            let modules: ModuleEndpoint[] = [];
            try {
                // Version-Informationen abrufen
                const versionResponse = await fetch(url, {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!versionResponse.ok) {
                    throw new Error(`Error fetching versions: ${versionResponse.statusText}`);
                }

                const versionData = await versionResponse.json() as OcpiResponse<OcpiVersion[]>;

                if (versionData.data && versionData.data.length > 0) {
                    const latestVersion = versionData.data[0];

                    // Module abrufen
                    const modulesResponse = await fetch(latestVersion.url, {
                        headers: {
                            'Authorization': `Token ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!modulesResponse.ok) {
                        throw new Error(`Error fetching modules: ${modulesResponse.statusText}`);
                    }

                    const modulesData = await modulesResponse.json() as OcpiResponse<OcpiVersionDetail>;

                    // Hier ist eine weitere Korrektur: Wir erwarten ein einzelnes Versiondetail-Objekt, kein Array
                    if (modulesData.data) {
                        modules = modulesData.data.endpoints || [];
                    }
                }
            } catch (error) {
                strapi.log.error('Error fetching remote OCPI data:', error);
            }

            // Verbindung aktualisieren
            await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                documentId: validationResult.ocpiConnection.documentId,
                data: {
                    receivingSecret: token, // Speichere Token B als ReceivingSecret
                    connectionUrl: url,
                    remoteParty: JSON.stringify(roles),
                    remoteModules: JSON.stringify(modules),
                    connectionStatus: 'active' // Optional: Status auf aktiv setzen
                }
            });

            // Neues Token für unsere Antwort generieren
            const newToken = randomBytes(16).toString('hex');

            // Unser Token als SendSecret speichern
            await strapi.documents('api::ocpi-connection.ocpi-connection').update({
                documentId: validationResult.ocpiConnection.documentId,
                data: {
                    SendSecret: newToken, // Speichere Token C als SendSecret
                    lastConnection: new Date().toISOString() // Optional: Zeitstempel aktualisieren
                }
            });

            // Host aus der Strapi-Konfiguration holen
            const publicUrl = strapi.config.get('server.publicURL', 'localhost:1337');

            // Antwort erstellen
            const response = {
                status_code: 1000,
                status_message: "Success",
                data: {
                    token: newToken,
                    url: `${publicUrl}/api/ocpi/versions`,
                    roles: [
                        {
                            role: "CPO",
                            business_details: {
                                name: "EulektroTerminalVerwaltung CPO"
                            },
                            party_id: "EVT",
                            country_code: "DE"
                        }
                    ]
                },
                timestamp: new Date().toISOString()
            };

            return ctx.send(response);
        } catch (error) {
            strapi.log.error('Error in postCredentials:', error);
            return ctx.send({
                status_code: 3000,
                status_message: "Server error",
                timestamp: new Date().toISOString()
            });
        }
    }
};
