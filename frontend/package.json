{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "dev": "next dev --turbo ", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^2.2.2", "@libsql/client": "^0.15.4", "@radix-ui/react-tabs": "^1.1.9", "@t3-oss/env-nextjs": "^0.13.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "next": "^15.3.1", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-toastify": "^11.0.5", "react-use": "^17.6.0", "server-only": "^0.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.2.0", "zod": "^3.24.3", "autoprefixer": "^10.4.21"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20.17.31", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "axios": "^1.9.0", "lucide-react": "^0.503.0", "postcss": "^8.5.3", "react-icons": "^5.5.0", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}, "ct3aMetadata": {"initVersion": "7.39.0"}, "packageManager": "npm@10.8.2"}