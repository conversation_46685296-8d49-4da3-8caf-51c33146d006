// src/env.js
import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
	server: {
		AUTH_SECRET:
			process.env.NODE_ENV === "production"
				? z.string()
				: z.string().optional(),
		DATABASE_URL: z.string().url(),
		NODE_ENV: z
			.enum(["development", "test", "production"])
			.default("development"),
		STRAPI_API_URL: z.string().url(),
		STRAPI_API_TOKEN: z.string(),
	},
	client: {
		NEXT_PUBLIC_STRAPI_API_URL: z.string().url(),
		NEXT_PUBLIC_IMAGES_PROTOCOL: z.string(),
		NEXT_PUBLIC_IMAGES_HOSTNAME: z.string(),
		NEXT_PUBLIC_IMAGES_PORT: z.string(),
		NEXT_PUBLIC_IMAGES_PATHNAME: z.string(),
	},
	runtimeEnv: {
		AUTH_SECRET: process.env.AUTH_SECRET,
		DATABASE_URL: process.env.DATABASE_URL,
		NODE_ENV: process.env.NODE_ENV,
		STRAPI_API_URL: process.env.STRAPI_API_URL,
		STRAPI_API_TOKEN: process.env.STRAPI_API_TOKEN,
		NEXT_PUBLIC_STRAPI_API_URL: process.env.NEXT_PUBLIC_STRAPI_API_URL,
		NEXT_PUBLIC_IMAGES_PROTOCOL: process.env.NEXT_PUBLIC_IMAGES_PROTOCOL,
		NEXT_PUBLIC_IMAGES_HOSTNAME: process.env.NEXT_PUBLIC_IMAGES_HOSTNAME,
		NEXT_PUBLIC_IMAGES_PORT: process.env.NEXT_PUBLIC_IMAGES_PORT,
		NEXT_PUBLIC_IMAGES_PATHNAME: process.env.NEXT_PUBLIC_IMAGES_PATHNAME,
	},
	skipValidation: !!process.env.SKIP_ENV_VALIDATION,
	emptyStringAsUndefined: true,
});
