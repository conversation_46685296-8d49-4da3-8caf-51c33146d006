/**
 * payment-session service
 */

import { factories } from '@strapi/strapi';
import { calculateAmountToCaptchaByPriceAndCdr, chargeCreditCard } from './payment-capture';

export default factories.createCoreService('api::payment-session.payment-session', ({ strapi }) => ({
  /**
   * Berechnet den zu belastenden Betrag basierend auf dem Preis und den CDR-Daten
   *
   * @param price - Der Preis für den Ladevorgang
   * @param cdr - Die CDR-Daten
   * @returns Der zu belastende Betrag in Cent
   */
  async calculateAmountToCaptchaByPriceAndCdr(price: any, cdr: any): Promise<number> {
    return calculateAmountToCaptchaByPriceAndCdr(price, cdr);
  },

  /**
   * Belastet die Kreditkarte mit dem berechneten Betrag
   *
   * @param paymentSessionId - Die ID der Payment-Session
   * @param amountToCapture - Der zu belastende Betrag in Cent
   * @returns Die aktualisierte Payment-Session
   */
  async chargeCreditCard(paymentSessionId: string, amountToCapture: number): Promise<any> {

    return chargeCreditCard(paymentSessionId, amountToCapture);
  },

  async formatHistory (
      paymentSession: any,
      historyEntry: any
  ): Promise<string[]> {
    let currentHistory = [];

    if(paymentSession?.history)
    {
      if(Array.isArray(paymentSession.history))
      {

        currentHistory = [...paymentSession.history,historyEntry];
        return currentHistory;
      }
      return [paymentSession.history,historyEntry];
    }
    return [historyEntry];

  }
}));
