{"collectionName": "components_ocpi_regular_hours", "info": {"displayName": "RegularHours", "description": "OCPI 2.2.1 RegularHours object"}, "options": {}, "attributes": {"weekday": {"type": "integer", "required": true, "min": 1, "max": 7, "comment": "Number of day in the week, from Monday (1) till Sunday (7)"}, "periodBegin": {"type": "string", "required": true, "comment": "Begin of the regular period, in local time, in the format: hh:mm"}, "periodEnd": {"type": "string", "required": true, "comment": "End of the regular period, in local time, in the format: hh:mm"}}}