{"collectionName": "components_ocpi_hours", "info": {"displayName": "Hours", "description": "OCPI 2.2.1 Hours object"}, "options": {}, "attributes": {"twentyfourseven": {"type": "boolean", "comment": "True if the location is open 24 hours a day, 7 days a week"}, "regularHours": {"type": "component", "repeatable": true, "component": "ocpi.regular-hours", "comment": "Regular hours, weekday-based"}, "exceptionalOpenings": {"type": "component", "repeatable": true, "component": "ocpi.exceptional-period", "comment": "Exceptional opening periods"}, "exceptionalClosings": {"type": "component", "repeatable": true, "component": "ocpi.exceptional-period", "comment": "Exceptional closing periods"}}}