"use client";

import type React from "react";
import { useEffect, useState } from "react";
import { useMandant } from "~/components/MandantContext";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Spinner } from "~/components/ui/spinner";
import {
	type LocationData,
	type MandantData,
	locationsApi,
	mandantsApi,
} from "~/services/api";

interface StandortFormProps {
	standortId?: string;
	onSuccess: () => void;
	onCancel: () => void;
}

export default function StandortForm({
	standortId,
	onSuccess,
	onCancel,
}: StandortFormProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [isSaving, setIsSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [mandanten, setMandanten] = useState<MandantData[]>([]);
	const { activeMandant, getAllowedMandantIds } = useMandant();

	const [formData, setFormData] = useState({
		name: "",
		ocpiId: "",
		countryCode: "DE",
		partyId: "EUL",
		address: "",
		city: "",
		postalCode: "",
		country: "DEU",
		timeZone: "Europe/Berlin",
		publish: true,
		mandant: activeMandant?.id || undefined,
	});

	// Mandanten laden
	useEffect(() => {
		const fetchMandanten = async () => {
			try {
				const response = await mandantsApi.getAll();
				const allowedMandantIds = getAllowedMandantIds();

				// Nur erlaubte Mandanten anzeigen
				const filteredMandanten = response.data.filter((mandant) =>
					allowedMandantIds.includes(mandant.id),
				);

				setMandanten(filteredMandanten);
			} catch (err) {
				console.error("Fehler beim Laden der Mandanten:", err);
				setError("Mandanten konnten nicht geladen werden.");
			}
		};

		fetchMandanten();
	}, []);

	// Standort laden, wenn eine ID übergeben wurde (Bearbeiten-Modus)
	useEffect(() => {
		if (standortId) {
			const fetchStandort = async () => {
				setIsLoading(true);
				try {
					const standort = await locationsApi.getById(standortId);
					setFormData({
						name: standort.name || "",
						ocpiId: standort.ocpiId || "",
						countryCode: standort.countryCode || "DE",
						partyId: standort.partyId || "EUL",
						address: standort.address || "",
						city: standort.city || "",
						postalCode: standort.postalCode || "",
						country: standort.country || "DEU",
						timeZone: standort.timeZone || "Europe/Berlin",
						publish: standort.publish !== undefined ? standort.publish : true,
						mandant: standort.mandant?.data?.id,
					});
				} catch (err) {
					console.error(
						`Fehler beim Laden des Standorts mit ID ${standortId}:`,
						err,
					);
					setError("Standort konnte nicht geladen werden.");
				} finally {
					setIsLoading(false);
				}
			};

			fetchStandort();
		}
	}, [standortId]);

	const handleChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
	) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: name === "mandant" ? value || undefined : value,
		}));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSaving(true);
		setError(null);

		try {
			const data = {
				name: formData.name,
				ocpiId: formData.ocpiId,
				countryCode: formData.countryCode,
				partyId: formData.partyId,
				address: formData.address,
				city: formData.city,
				postalCode: formData.postalCode,
				country: formData.country,
				timeZone: formData.timeZone,
				publish: formData.publish,
				lastUpdated: new Date().toISOString(),
				mandant: formData.mandant ? { connect: [formData.mandant] } : undefined,
			};

			if (standortId) {
				// Standort aktualisieren
				await locationsApi.update(standortId, data);
			} else {
				// Neuen Standort erstellen
				await locationsApi.create(data);
			}

			onSuccess();
		} catch (err) {
			console.error("Fehler beim Speichern des Standorts:", err);
			setError(
				"Standort konnte nicht gespeichert werden. Bitte versuchen Sie es später erneut.",
			);
		} finally {
			setIsSaving(false);
		}
	};

	if (isLoading) {
		return (
			<div className="flex justify-center py-8">
				<Spinner size="lg" />
			</div>
		);
	}

	return (
		<div className="rounded-lg bg-white p-6 shadow-md">
			<h2 className="mb-4 font-semibold text-xl">
				{standortId ? "Standort bearbeiten" : "Neuen Standort erstellen"}
			</h2>

			{error && (
				<div className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
					{error}
				</div>
			)}

			<form onSubmit={handleSubmit} className="space-y-4">
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					<div>
						<Label htmlFor="name">Name</Label>
						<Input
							id="name"
							name="name"
							value={formData.name}
							onChange={handleChange}
							required
							placeholder="Name des Standorts"
						/>
					</div>

					<div>
						<Label htmlFor="ocpiId">OCPI ID</Label>
						<Input
							id="ocpiId"
							name="ocpiId"
							value={formData.ocpiId}
							onChange={handleChange}
							required
							placeholder="z.B. DEEUlSE0001"
						/>
					</div>

					<div>
						<Label htmlFor="countryCode">Ländercode</Label>
						<Input
							id="countryCode"
							name="countryCode"
							value={formData.countryCode}
							onChange={handleChange}
							required
							placeholder="z.B. DE"
							maxLength={2}
						/>
					</div>

					<div>
						<Label htmlFor="partyId">Party ID</Label>
						<Input
							id="partyId"
							name="partyId"
							value={formData.partyId}
							onChange={handleChange}
							required
							placeholder="z.B. EUL"
							maxLength={3}
						/>
					</div>

					<div>
						<Label htmlFor="address">Adresse</Label>
						<Input
							id="address"
							name="address"
							value={formData.address}
							onChange={handleChange}
							required
							placeholder="Straße und Hausnummer"
						/>
					</div>

					<div>
						<Label htmlFor="city">Stadt</Label>
						<Input
							id="city"
							name="city"
							value={formData.city}
							onChange={handleChange}
							required
							placeholder="Stadt"
						/>
					</div>

					<div>
						<Label htmlFor="postalCode">Postleitzahl</Label>
						<Input
							id="postalCode"
							name="postalCode"
							value={formData.postalCode}
							onChange={handleChange}
							required
							placeholder="PLZ"
						/>
					</div>

					<div>
						<Label htmlFor="country">Land</Label>
						<Input
							id="country"
							name="country"
							value={formData.country}
							onChange={handleChange}
							required
							placeholder="z.B. DEU"
							maxLength={3}
						/>
					</div>

					<div>
						<Label htmlFor="timeZone">Zeitzone</Label>
						<Input
							id="timeZone"
							name="timeZone"
							value={formData.timeZone}
							onChange={handleChange}
							placeholder="z.B. Europe/Berlin"
						/>
					</div>

					<div>
						<Label htmlFor="publish">Veröffentlichen</Label>
						<div className="mt-2 flex items-center">
							<input
								type="checkbox"
								id="publish"
								name="publish"
								checked={formData.publish}
								onChange={(e) =>
									setFormData((prev) => ({
										...prev,
										publish: e.target.checked,
									}))
								}
								className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
							/>
							<label htmlFor="publish" className="ml-2 text-gray-700 text-sm">
								Standort veröffentlichen
							</label>
						</div>
					</div>

					<div>
						<Label htmlFor="mandant">Mandant</Label>
						<select
							id="mandant"
							name="mandant"
							value={formData.mandant || ""}
							onChange={handleChange}
							className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm"
						>
							<option value="">Bitte wählen...</option>
							{mandanten.map((mandant) => (
								<option key={mandant.id} value={mandant.id}>
									{mandant.name}
								</option>
							))}
						</select>
					</div>
				</div>

				<div className="flex justify-end space-x-2 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={onCancel}
						disabled={isSaving}
					>
						Abbrechen
					</Button>
					<Button type="submit" disabled={isSaving}>
						{isSaving ? (
							<>
								<Spinner size="sm" className="mr-2" />
								Speichern...
							</>
						) : (
							"Speichern"
						)}
					</Button>
				</div>
			</form>
		</div>
	);
}
