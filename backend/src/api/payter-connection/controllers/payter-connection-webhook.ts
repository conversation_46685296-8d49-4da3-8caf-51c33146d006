/**
 * Payter Connection Webhook Controller
 * Bietet Endpunkte für die Initialisierung von Webhooks für Payter Connections
 */
import axios from 'axios';

export default {
  /**
   * Initialisiert einen Webhook für eine Payter Connection
   *
   * @param {Object} ctx - Der Kontext des Requests
   * @returns {Object} Statusmeldung
   */
  async initializeWebhook(ctx) {
    try {
      const { id } = ctx.params;

      // Prüfe, ob ein gültiger API-Token oder eine Benutzer-Session vorliegt
      if (!ctx.state.user && !ctx.state.auth?.credentials) {
        return ctx.unauthorized('Unauthorized access. Valid API token or authentication required.');
      }

      // Hole die Payter Connection
      const payterConnection = await strapi.documents('api::payter-connection.payter-connection').findOne({
        documentId: id,
      });

      if (!payterConnection) {
        return ctx.notFound(`Payter Connection with ID ${id} not found`);
      }

      // Log the action
      strapi.log.info(`Webhook initialization requested for Payter Connection ${id}`);

      // Status-Liste, die überwacht werden soll
      const states = ["IDLE", "READING", "CARD", "STOPPING", "AUTHORIZING"];

      // Hole die Server-Konfiguration für die Callback-URL
      const publicURL = strapi.config.get('server.publicURL');

      if (!publicURL) {
        return ctx.badRequest('Keine öffentliche URL in der Server-Konfiguration gefunden');
      }

      // Erstelle die Callback-URL
      const callbackUrl = `${publicURL}/api/payter/stateCallback`;

      // Erstelle einen API-Client für die Payter API
      const payterApiService = strapi.service('api::payter.payter-api');
      const apiClient = payterApiService.getApiClient(payterConnection.apiUrl, payterConnection.apiKey);

      if (!apiClient) {
        return ctx.badRequest(`Fehler beim Erstellen des API-Clients für Payter Connection ${id}`);
      }

      // Bereite die Anfrage vor
      const requestBody = {
        callbackUrl: callbackUrl,
        states: states
      };

      // Sende die Anfrage an die Payter API
      const response = await apiClient.put('webhook', requestBody);

      // Aktualisiere das lastWebhookInit Datum bei erfolgreicher Initialisierung
      await strapi.documents('api::payter-connection.payter-connection').update({
        documentId: id,
        data: {
          lastWebhookInit: new Date().toISOString()
        }
      });

      // Erfolg zurückgeben
      return ctx.send({
        success: true,
        message: `Webhook für Payter Connection "${payterConnection.name}" wurde erfolgreich initialisiert`,
        connection: {
          id: payterConnection.documentId,
          name: payterConnection.name,
          type: payterConnection.type
        },
        webhook: {
          url: callbackUrl,
          states: states
        },
        payter_response: response.data
      });
    } catch (error) {
      strapi.log.error(`Error initializing webhook for Payter Connection: ${error.message}`);

      // Detaillierte Fehlerinformationen zurückgeben
      if (error.response) {
        // Spezifische Fehlerbehandlung basierend auf der Payter-API-Antwort
        const status = error.response.status;
        const errorData = error.response.data;

        return ctx.badRequest({
          status_code: status,
          status_message: errorData.message || `Fehler bei der Webhook-Initialisierung`,
          error: errorData.error || 'Unbekannter Fehler',
          timestamp: errorData.timestamp || new Date().toISOString(),
          error_details: errorData
        });
      } else if (error.request) {
        return ctx.badRequest({
          status_code: 504,
          status_message: `Keine Antwort von der Payter API erhalten`
        });
      } else {
        return ctx.badRequest({
          status_code: 500,
          status_message: `Interner Fehler bei der Webhook-Initialisierung`,
          error_details: error.message
        });
      }
    }
  }
};
