"use client";

import { format } from "date-fns";
import { de } from "date-fns/locale";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { FaArrowLeft, FaMoneyBillWave, FaTimesCircle } from "react-icons/fa";
import { toast } from "react-toastify";
import { Spinner } from "~/components/Spinner";
import { paymentSessionDetailApi } from "~/services/payment-session-api";
import { formatCurrency } from "~/utils/formatters";

// Modal für die Capture-Funktion
const CaptureModal = ({
	isOpen,
	onClose,
	onCapture,
	suggestedAmount,
	blockedAmount,
}: {
	isOpen: boolean;
	onClose: () => void;
	onCapture: (amount: number) => void;
	suggestedAmount: number;
	blockedAmount: number;
}) => {
	const [amount, setAmount] = useState(suggestedAmount);
	const [isProcessing, setIsProcessing] = useState(false);

	// Setze den Betrag zurück, wenn sich der suggestedAmount ändert
	useEffect(() => {
		setAmount(suggestedAmount);
	}, [suggestedAmount]);

	const handleCapture = async () => {
		if (amount <= 0) {
			toast.error("Der Betrag muss größer als 0 sein.");
			return;
		}

		if (amount > blockedAmount) {
			toast.error(
				`Der Betrag darf nicht größer als der reservierte Betrag (${formatCurrency(blockedAmount / 100)}) sein.`,
			);
			return;
		}

		setIsProcessing(true);
		try {
			await onCapture(amount);
			onClose();
		} catch (error) {
			console.error("Fehler beim Capturen:", error);
		} finally {
			setIsProcessing(false);
		}
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
			<div className="w-full max-w-md rounded-lg bg-white p-6">
				<h2 className="mb-4 font-bold text-xl">Zahlung abbuchen</h2>
				<p className="mb-4">
					Geben Sie den Betrag ein, der abgebucht werden soll. Der maximale
					Betrag ist {formatCurrency(blockedAmount / 100)}.
				</p>
				<div className="mb-4">
					<label
						htmlFor="amount"
						className="mb-1 block font-medium text-gray-700 text-sm"
					>
						Betrag (in Euro)
					</label>
					<div className="relative">
						<input
							type="number"
							id="amount"
							className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
							value={amount / 100}
							onChange={(e) =>
								setAmount(Math.round(Number.parseFloat(e.target.value) * 100))
							}
							step="0.01"
							min="0.01"
							max={blockedAmount / 100}
							disabled={isProcessing}
						/>
						<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
							<span className="text-gray-500 sm:text-sm">€</span>
						</div>
					</div>
				</div>
				<div className="flex justify-end space-x-3">
					<button
						type="button"
						className="rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
						onClick={onClose}
						disabled={isProcessing}
					>
						Abbrechen
					</button>
					<button
						type="button"
						className="flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
						onClick={handleCapture}
						disabled={isProcessing}
					>
						{isProcessing ? (
							<>
								<Spinner />
								Verarbeitung...
							</>
						) : (
							<>
								<FaMoneyBillWave className="mr-2" />
								Betrag abbuchen
							</>
						)}
					</button>
				</div>
			</div>
		</div>
	);
};

export default function PaymentSessionDetailPage() {
	const params = useParams();
	const router = useRouter();
	const id = params.id as string;

	const [session, setSession] = useState<any>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showCaptureModal, setShowCaptureModal] = useState(false);
	const [suggestedAmount, setSuggestedAmount] = useState(0);

	// Daten laden
	const fetchPaymentSession = async () => {
		try {
			setLoading(true);
			const response = await paymentSessionDetailApi.getById(id);
			setSession(response.data);

			// Vorgeschlagenen Betrag berechnen
			if (response.data.ocpi_cdr?.totalCost) {
				// Wenn ein CDR vorhanden ist, verwende dessen totalCost
				setSuggestedAmount(Math.round(response.data.ocpi_cdr.totalCost * 100));
			} else if (response.data.blockedAmount) {
				// Ansonsten verwende den reservierten Betrag
				setSuggestedAmount(response.data.blockedAmount);
			}
		} catch (err: any) {
			console.error("Fehler beim Laden der Payment Session:", err);
			setError(
				`Payment Session konnte nicht geladen werden. ${err.message || ""}`,
			);
		} finally {
			setLoading(false);
		}
	};

	// Capture-Funktion
	const handleCapture = async (amount: number) => {
		try {
			const result = await paymentSessionDetailApi.capturePayment(id, amount);
			toast.success(
				`Zahlung erfolgreich abgebucht: ${formatCurrency(amount / 100)}`,
			);
			fetchPaymentSession(); // Daten neu laden
		} catch (err: any) {
			console.error("Fehler beim Abbuchen der Zahlung:", err);
			toast.error(`Fehler beim Abbuchen der Zahlung: ${err.message || ""}`);
		}
	};

	// Daten beim Laden der Seite abrufen
	useEffect(() => {
		fetchPaymentSession();
	}, [id]);

	// Formatierungsfunktionen
	const formatDate = (dateString: string | undefined) => {
		if (!dateString) return "-";
		try {
			return format(new Date(dateString), "dd.MM.yyyy HH:mm:ss", {
				locale: de,
			});
		} catch (e) {
			return dateString;
		}
	};

	const formatState = (state: string | undefined) => {
		if (!state) return "-";

		const stateMap: Record<string, { label: string; className: string }> = {
			started: { label: "Gestartet", className: "bg-blue-100 text-blue-800" },
			authorized: {
				label: "Autorisiert",
				className: "bg-yellow-100 text-yellow-800",
			},
			captured: {
				label: "Abgebucht",
				className: "bg-green-100 text-green-800",
			},
			canceled: { label: "Abgebrochen", className: "bg-red-100 text-red-800" },
			error: { label: "Fehler", className: "bg-red-100 text-red-800" },
		};

		const stateInfo = stateMap[state] || {
			label: state,
			className: "bg-gray-100 text-gray-800",
		};

		return (
			<span
				className={`rounded-full px-2 py-1 font-medium text-xs ${stateInfo.className}`}
			>
				{stateInfo.label}
			</span>
		);
	};

	if (loading) {
		return (
			<div className="container mx-auto px-4 py-8">
				<div className="flex h-64 items-center justify-center">
					<Spinner />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto px-4 py-8">
				<div className="mb-4 border-red-500 border-l-4 bg-red-50 p-4">
					<div className="flex">
						<div className="flex-shrink-0">
							<FaTimesCircle className="h-5 w-5 text-red-500" />
						</div>
						<div className="ml-3">
							<p className="text-red-700 text-sm">{error}</p>
						</div>
					</div>
				</div>
				<button
					className="flex items-center text-blue-600 hover:text-blue-800"
					onClick={() => router.back()}
				>
					<FaArrowLeft className="mr-2" />
					Zurück
				</button>
			</div>
		);
	}

	if (!session) {
		return (
			<div className="container mx-auto px-4 py-8">
				<div className="mb-4 border-yellow-500 border-l-4 bg-yellow-50 p-4">
					<div className="flex">
						<div className="flex-shrink-0">
							<FaTimesCircle className="h-5 w-5 text-yellow-500" />
						</div>
						<div className="ml-3">
							<p className="text-sm text-yellow-700">Keine Daten gefunden</p>
						</div>
					</div>
				</div>
				<button
					className="flex items-center text-blue-600 hover:text-blue-800"
					onClick={() => router.back()}
				>
					<FaArrowLeft className="mr-2" />
					Zurück
				</button>
			</div>
		);
	}

	const canCapture = session.state === "authorized";

	return (
		<div className="container mx-auto px-4 py-8">
			{/* Header mit Zurück-Button und Titel */}
			<div className="mb-6 flex items-center justify-between">
				<div className="flex items-center">
					<button
						className="mr-4 text-blue-600 hover:text-blue-800"
						onClick={() => router.back()}
					>
						<FaArrowLeft className="text-xl" />
					</button>
					<h1 className="font-bold text-2xl">Umsatz-Details</h1>
				</div>

				{/* Capture-Button, nur anzeigen wenn der Status "authorized" ist */}
				{canCapture && (
					<button
						className="flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white"
						onClick={() => setShowCaptureModal(true)}
					>
						<FaMoneyBillWave className="mr-2" />
						Zahlung abbuchen
					</button>
				)}
			</div>

			{/* Status-Banner */}
			<div className="mb-6 flex items-center rounded-lg bg-white p-4 shadow">
				<div className="mr-4">
					<span className="font-medium text-gray-500 text-sm">Status:</span>
				</div>
				<div>{formatState(session.state)}</div>
			</div>

			{/* Hauptinformationen */}
			<div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
				{/* Zahlungsinformationen */}
				<div className="overflow-hidden rounded-lg bg-white shadow">
					<div className="bg-gray-50 px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg">
							Zahlungsinformationen
						</h3>
					</div>
					<div className="border-gray-200 border-t px-4 py-5 sm:p-0">
						<dl className="sm:divide-y sm:divide-gray-200">
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Payment Intent
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.paymentIntent || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Reservierter Betrag
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{formatCurrency(session.blockedAmount / 100)}
								</dd>
							</div>
							{session.state === "captured" && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Abgebuchter Betrag
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{formatCurrency(session.capturedAmount / 100)}
									</dd>
								</div>
							)}
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Karten-PAN
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.maskedPan || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Kartentyp</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.brand || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Merchant Reference
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.merchantReference || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Autorisiert am
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{formatDate(session.authorizedAt)}
								</dd>
							</div>
							{session.closedAt && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Abgeschlossen am
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{formatDate(session.closedAt)}
									</dd>
								</div>
							)}
						</dl>
					</div>
				</div>

				{/* Terminal und Standort */}
				<div className="overflow-hidden rounded-lg bg-white shadow">
					<div className="bg-gray-50 px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg">
							Terminal und Standort
						</h3>
					</div>
					<div className="border-gray-200 border-t px-4 py-5 sm:p-0">
						<dl className="sm:divide-y sm:divide-gray-200">
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Terminal</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.terminal?.terminalName ||
										session.terminal?.serialNumber ||
										"-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Mandant</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.mandant?.name || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">EVSE ID</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.ocpi_evse?.evseId || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Ladepunkt</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.ocpi_evse?.labelForTerminal || "-"}
								</dd>
							</div>
							{session.ocpi_evse?.location && (
								<>
									<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
										<dt className="font-medium text-gray-500 text-sm">
											Standort
										</dt>
										<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
											{session.ocpi_evse.location.name || "-"}
										</dd>
									</div>
									<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
										<dt className="font-medium text-gray-500 text-sm">
											Adresse
										</dt>
										<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
											{session.ocpi_evse.location.address},{" "}
											{session.ocpi_evse.location.postalCode}{" "}
											{session.ocpi_evse.location.city}
										</dd>
									</div>
								</>
							)}
						</dl>
					</div>
				</div>
			</div>

			{/* Ladevorgang-Informationen */}
			{session.ocpi_session && (
				<div className="mb-6 overflow-hidden rounded-lg bg-white shadow">
					<div className="bg-gray-50 px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg">Ladevorgang</h3>
					</div>
					<div className="border-gray-200 border-t px-4 py-5 sm:p-0">
						<dl className="sm:divide-y sm:divide-gray-200">
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Session ID
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.ocpi_session.sessionId || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Status</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.ocpi_session.ocpiStatus || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Startzeit</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{formatDate(session.ocpi_session.startTime)}
								</dd>
							</div>
							{session.ocpi_session.endTime && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">Endzeit</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{formatDate(session.ocpi_session.endTime)}
									</dd>
								</div>
							)}
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Geladene Energie
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.ocpi_session.kwh
										? `${session.ocpi_session.kwh.toFixed(2)} kWh`
										: "0 kWh"}
								</dd>
							</div>
						</dl>
					</div>
				</div>
			)}

			{/* CDR-Informationen */}
			{session.ocpi_cdr && (
				<div className="mb-6 overflow-hidden rounded-lg bg-white shadow">
					<div className="bg-gray-50 px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg">
							Charge Detail Record (CDR)
						</h3>
					</div>
					<div className="border-gray-200 border-t px-4 py-5 sm:p-0">
						<dl className="sm:divide-y sm:divide-gray-200">
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">CDR ID</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.ocpi_cdr.cdrId || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Erstellt am
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{formatDate(session.ocpi_cdr.createdAt)}
								</dd>
							</div>
							{session.ocpi_cdr.totalCost && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Gesamtkosten
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{formatCurrency(session.ocpi_cdr.totalCost)}{" "}
										{session.ocpi_cdr.currency || "EUR"}
									</dd>
								</div>
							)}
							{session.ocpi_cdr.totalEnergy && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Gesamtenergie
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{session.ocpi_cdr.totalEnergy.toFixed(2)} kWh
									</dd>
								</div>
							)}
							{session.ocpi_cdr.totalTime && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Gesamtzeit
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{Math.floor(session.ocpi_cdr.totalTime / 3600)} Std.{" "}
										{Math.floor((session.ocpi_cdr.totalTime % 3600) / 60)} Min.
									</dd>
								</div>
							)}
							{session.ocpi_cdr.startDateTime && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Startzeit (CDR)
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{formatDate(session.ocpi_cdr.startDateTime)}
									</dd>
								</div>
							)}
							{session.ocpi_cdr.endDateTime && (
								<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
									<dt className="font-medium text-gray-500 text-sm">
										Endzeit (CDR)
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{formatDate(session.ocpi_cdr.endDateTime)}
									</dd>
								</div>
							)}
						</dl>
					</div>
				</div>
			)}

			{/* Rechnungsinformationen */}
			{session.invoice && (
				<div className="mb-6 overflow-hidden rounded-lg bg-white shadow">
					<div className="bg-gray-50 px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg">Rechnung</h3>
					</div>
					<div className="border-gray-200 border-t px-4 py-5 sm:p-0">
						<dl className="sm:divide-y sm:divide-gray-200">
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">
									Rechnungsnummer
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.invoice.invoice_number || "-"}
								</dd>
							</div>
							<div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 sm:py-5">
								<dt className="font-medium text-gray-500 text-sm">Status</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{session.invoice.invoice_status === "DRAFT"
										? "Entwurf"
										: session.invoice.invoice_status === "INMUTABLE_WRITTEN"
											? "Erstellt"
											: session.invoice.invoice_status === "PAID"
												? "Bezahlt"
												: session.invoice.invoice_status || "-"}
								</dd>
							</div>
						</dl>
					</div>
				</div>
			)}

			{/* Capture-Modal */}
			<CaptureModal
				isOpen={showCaptureModal}
				onClose={() => setShowCaptureModal(false)}
				onCapture={handleCapture}
				suggestedAmount={suggestedAmount}
				blockedAmount={session.blockedAmount}
			/>
		</div>
	);
}
