'use strict';

/**
 * OCPI Location Service für OCPI 2.2.1
 * Implementiert Funktionen zur Verarbeitung von Location-Daten
 */
module.exports = {
  /**
   * Konvertiert Location-Daten aus dem internen Format ins OCPI 2.2.1 Format
   * 
   * @param {Object} location - Die interne Location
   * @param {boolean} includeEvses - Ob EVSEs und Connectors einbezogen werden sollen
   * @returns {Object} - Die konvertierte OCPI Location
   */
  async convertToOcpiFormat(location, includeEvses = true) {
    if (!location) return null;

    // Basis-Location-Daten
    const ocpiLocation = {
      id: location.ocpiId || location.id.toString(),
      type: location.type || "UNKNOWN",
      name: location.name,
      address: location.address,
      city: location.city,
      postal_code: location.postalCode,
      country: location.country,
      coordinates: {
        latitude: "0",
        longitude: "0"
      },
      evses: [],
      last_updated: location.lastUpdated || new Date().toISOString()
    };

    // Koordinaten verarbeiten
    if (location.coordinates) {
      const coords = location.coordinates.split(',');
      if (coords.length === 2) {
        ocpiLocation.coordinates.latitude = coords[0].trim();
        ocpiLocation.coordinates.longitude = coords[1].trim();
      }
    }

    // EVSEs einbeziehen falls gewünscht
    if (includeEvses && location.evses && Array.isArray(location.evses)) {
      for (const evse of location.evses) {
        const ocpiEvse = await this.convertEvseToOcpiFormat(evse);
        if (ocpiEvse) {
          ocpiLocation.evses.push(ocpiEvse);
        }
      }
    }

    return ocpiLocation;
  },

  /**
   * Konvertiert EVSE-Daten aus dem internen Format ins OCPI 2.2.1 Format
   * 
   * @param {Object} evse - Der interne EVSE
   * @returns {Object} - Der konvertierte OCPI EVSE
   */
  async convertEvseToOcpiFormat(evse) {
    if (!evse) return null;

    const ocpiEvse = {
      uid: evse.uid || evse.id.toString(),
      evse_id: evse.evseId || "",
      status: evse.status || "UNKNOWN",
      connectors: [],
      last_updated: evse.lastUpdated || new Date().toISOString()
    };

    // Connectors einbeziehen
    if (evse.connectors && Array.isArray(evse.connectors)) {
      for (const connector of evse.connectors) {
        const ocpiConnector = this.convertConnectorToOcpiFormat(connector);
        if (ocpiConnector) {
          ocpiEvse.connectors.push(ocpiConnector);
        }
      }
    }

    return ocpiEvse;
  },

  /**
   * Konvertiert Connector-Daten aus dem internen Format ins OCPI 2.2.1 Format
   * 
   * @param {Object} connector - Der interne Connector
   * @returns {Object} - Der konvertierte OCPI Connector
   */
  convertConnectorToOcpiFormat(connector) {
    if (!connector) return null;

    return {
      id: connector.id.toString(),
      standard: connector.standard || "IEC_62196_T2",
      format: connector.format || "SOCKET",
      power_type: connector.powerType || "AC_3_PHASE",
      max_voltage: connector.maxVoltage || 400,
      max_amperage: connector.maxAmperage || 16,
      max_electric_power: connector.maxElectricPower || 11000,
      tariff_ids: connector.tariffIds || [],
      last_updated: connector.lastUpdated || new Date().toISOString()
    };
  },

  /**
   * Konvertiert OCPI Location-Daten ins interne Format
   * 
   * @param {Object} ocpiLocation - Die OCPI Location
   * @param {Object} mandantId - ID des Mandanten
   * @returns {Object} - Die konvertierte interne Location
   */
  convertFromOcpiFormat(ocpiLocation, mandantId) {
    if (!ocpiLocation) return null;

    return {
      ocpiId: ocpiLocation.id,
      name: ocpiLocation.name,
      address: ocpiLocation.address,
      city: ocpiLocation.city,
      postalCode: ocpiLocation.postal_code,
      country: ocpiLocation.country,
      coordinates: `${ocpiLocation.coordinates?.latitude || 0},${ocpiLocation.coordinates?.longitude || 0}`,
      type: ocpiLocation.type,
      lastUpdated: new Date(),
      mandant: mandantId
    };
  }
};