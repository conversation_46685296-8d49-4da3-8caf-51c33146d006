// backend/src/api/payter/controllers/payter.ts
import * as uiController from './terminal/ui-controller';
import * as paymentController from './terminal/payment-controller';
import * as webhookController from './terminal/webhook-controller';
import * as stateMachine from './terminal/state-machine';
import * as cardInfoCallback from './terminal/cardInfoCallback';
import * as authorizedCallback from "./terminal/authorizedCallback";
import * as uiActions from "./terminal/uiActions";

/**
 * Payter Controller
 * Haupteinstiegspunkt für Payter Terminal APIs
 * Organisiert in Module für bessere Übersichtlichkeit:
 * - api-client.ts: Utility-Funktionen für API-Aufrufe
 * - ui-controller.ts: Steuerung der Terminal-UI
 * - payment-controller.ts: Zahlungsverwaltung
 * - webhook-controller.ts: Webhook-Verwaltung
 * - state-machine.ts: Zustandsmaschine für Terminal-Screens
 */
module.exports = {
  // UI Controller Methoden
  getInit: uiActions.getInit,
  getChargingPoints: uiActions.getChargingPoints,
  getReading: uiActions.getReading,
  getChargingStarted: uiActions.getChargingStarted,
  getOutOfOrder: uiActions.getOutOfOrder,

  // Payment Controller Methoden
  getCallback: paymentController.getCallback,

  // Webhook Controller Methoden
  putWebhook: webhookController.putWebhook,
  postPayter: webhookController.postPayter,

  // State Machine Methoden
  postCallback: stateMachine.postCallback,
  postUiCallback: uiController.uiCallback,
  postStateCallback: stateMachine.stateCallback,
  authorizeCallback: authorizedCallback.authorizedCallback,
  cardInfoCallback: cardInfoCallback.cardInfoCallback,


};
