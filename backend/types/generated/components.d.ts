import type { Schema, Struct } from '@strapi/strapi';

export interface InvoicePositionInvoicePosition extends Struct.ComponentSchema {
  collectionName: 'components_invoice_position_invoice_positions';
  info: {
    description: '';
    displayName: 'InvoicePosition';
    icon: 'bulletList';
  };
  attributes: {
    amount: Schema.Attribute.Decimal & Schema.Attribute.Required;
    description: Schema.Attribute.String;
    pos: Schema.Attribute.Integer & Schema.Attribute.Required;
    sum_gross: Schema.Attribute.Decimal & Schema.Attribute.Required;
    sum_net: Schema.Attribute.Decimal & Schema.Attribute.Required;
    sum_tax: Schema.Attribute.Decimal & Schema.Attribute.Required;
    tax_rate: Schema.Attribute.Decimal &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<19>;
    title: Schema.Attribute.String & Schema.Attribute.Required;
    unit: Schema.Attribute.String & Schema.Attribute.Required;
    unit_price: Schema.Attribute.Decimal;
  };
}

export interface OcpiAdditionalGeoLocation extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_additional_geo_locations';
  info: {
    description: 'OCPI 2.2.1 AdditionalGeoLocation object';
    displayName: 'AdditionalGeoLocation';
  };
  attributes: {
    latitude: Schema.Attribute.String & Schema.Attribute.Required;
    longitude: Schema.Attribute.String & Schema.Attribute.Required;
    name: Schema.Attribute.Component<'ocpi.display-text', true>;
    type: Schema.Attribute.Enumeration<
      ['ENTRANCE', 'EXIT', 'PARKING_SPOT', 'LOCATION']
    > &
      Schema.Attribute.Required;
  };
}

export interface OcpiBusinessDetails extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_business_details';
  info: {
    description: 'OCPI 2.2.1 BusinessDetails object';
    displayName: 'BusinessDetails';
  };
  attributes: {
    logo: Schema.Attribute.Component<'ocpi.image', false>;
    name: Schema.Attribute.String & Schema.Attribute.Required;
    website: Schema.Attribute.String;
  };
}

export interface OcpiCdrLocation extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_cdr_locations';
  info: {
    description: 'OCPI CDR Location Component';
    displayName: 'CDR Location';
  };
  attributes: {
    address: Schema.Attribute.String;
    city: Schema.Attribute.String;
    connectorFormat: Schema.Attribute.String;
    connectorId: Schema.Attribute.String;
    connectorPowerType: Schema.Attribute.String;
    connectorStandard: Schema.Attribute.String;
    coordinates: Schema.Attribute.JSON;
    country: Schema.Attribute.String;
    evseId: Schema.Attribute.String;
    evseUid: Schema.Attribute.String;
    locationId: Schema.Attribute.String;
    name: Schema.Attribute.String;
    postalCode: Schema.Attribute.String;
  };
}

export interface OcpiCdrToken extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_cdr_tokens';
  info: {
    description: 'OCPI CDR Token Component';
    displayName: 'CDR Token';
  };
  attributes: {
    contractId: Schema.Attribute.String;
    countryCode: Schema.Attribute.String;
    issuer: Schema.Attribute.String;
    lastUpdated: Schema.Attribute.DateTime;
    partyId: Schema.Attribute.String;
    type: Schema.Attribute.String;
    uid: Schema.Attribute.String;
    visualNumber: Schema.Attribute.String;
  };
}

export interface OcpiDisplayText extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_display_texts';
  info: {
    description: 'OCPI 2.2.1 DisplayText object';
    displayName: 'DisplayText';
  };
  attributes: {
    language: Schema.Attribute.String & Schema.Attribute.Required;
    text: Schema.Attribute.Text & Schema.Attribute.Required;
  };
}

export interface OcpiEnergyMix extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_energy_mixes';
  info: {
    description: 'OCPI 2.2.1 EnergyMix object';
    displayName: 'EnergyMix';
  };
  attributes: {
    energyProductName: Schema.Attribute.String;
    energySources: Schema.Attribute.Component<'ocpi.energy-source', true>;
    environImpact: Schema.Attribute.Component<
      'ocpi.environmental-impact',
      true
    >;
    isGreenEnergy: Schema.Attribute.Boolean & Schema.Attribute.Required;
    supplierName: Schema.Attribute.String;
  };
}

export interface OcpiEnergySource extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_energy_sources';
  info: {
    description: 'OCPI 2.2.1 EnergySource object';
    displayName: 'EnergySource';
  };
  attributes: {
    percentage: Schema.Attribute.Float &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 100;
          min: 0;
        },
        number
      >;
    source: Schema.Attribute.Enumeration<
      [
        'NUCLEAR',
        'GENERAL_FOSSIL',
        'COAL',
        'GAS',
        'GENERAL_GREEN',
        'SOLAR',
        'WIND',
        'WATER',
      ]
    > &
      Schema.Attribute.Required;
  };
}

export interface OcpiEnvironmentalImpact extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_environmental_impacts';
  info: {
    description: 'OCPI 2.2.1 EnvironmentalImpact object';
    displayName: 'EnvironmentalImpact';
  };
  attributes: {
    amount: Schema.Attribute.Float & Schema.Attribute.Required;
    category: Schema.Attribute.Enumeration<
      ['NUCLEAR_WASTE', 'CARBON_DIOXIDE']
    > &
      Schema.Attribute.Required;
  };
}

export interface OcpiExceptionalPeriod extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_exceptional_periods';
  info: {
    description: 'OCPI 2.2.1 ExceptionalPeriod object';
    displayName: 'ExceptionalPeriod';
  };
  attributes: {
    periodBegin: Schema.Attribute.DateTime & Schema.Attribute.Required;
    periodEnd: Schema.Attribute.DateTime & Schema.Attribute.Required;
  };
}

export interface OcpiGeoLocation extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_geo_locations';
  info: {
    description: 'OCPI 2.2.1 GeoLocation object';
    displayName: 'GeoLocation';
  };
  attributes: {
    latitude: Schema.Attribute.String & Schema.Attribute.Required;
    longitude: Schema.Attribute.String & Schema.Attribute.Required;
  };
}

export interface OcpiHours extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_hours';
  info: {
    description: 'OCPI 2.2.1 Hours object';
    displayName: 'Hours';
  };
  attributes: {
    exceptionalClosings: Schema.Attribute.Component<
      'ocpi.exceptional-period',
      true
    >;
    exceptionalOpenings: Schema.Attribute.Component<
      'ocpi.exceptional-period',
      true
    >;
    regularHours: Schema.Attribute.Component<'ocpi.regular-hours', true>;
    twentyfourseven: Schema.Attribute.Boolean;
  };
}

export interface OcpiImage extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_images';
  info: {
    description: 'OCPI 2.2.1 Image object';
    displayName: 'Image';
  };
  attributes: {
    category: Schema.Attribute.Enumeration<
      [
        'CHARGER',
        'ENTRANCE',
        'LOCATION',
        'NETWORK',
        'OPERATOR',
        'OTHER',
        'OWNER',
      ]
    >;
    height: Schema.Attribute.Integer;
    thumbnail: Schema.Attribute.String;
    type: Schema.Attribute.String;
    url: Schema.Attribute.String & Schema.Attribute.Required;
    width: Schema.Attribute.Integer;
  };
}

export interface OcpiOcpiModul extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_ocpi_moduls';
  info: {
    displayName: 'OCPI Modul';
    icon: 'puzzle';
  };
  attributes: {
    Name: Schema.Attribute.String;
    Url: Schema.Attribute.String;
  };
}

export interface OcpiRegularHours extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_regular_hours';
  info: {
    description: 'OCPI 2.2.1 RegularHours object';
    displayName: 'RegularHours';
  };
  attributes: {
    periodBegin: Schema.Attribute.String & Schema.Attribute.Required;
    periodEnd: Schema.Attribute.String & Schema.Attribute.Required;
    weekday: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 7;
          min: 1;
        },
        number
      >;
  };
}

export interface OcpiStatusSchedule extends Struct.ComponentSchema {
  collectionName: 'components_ocpi_status_schedules';
  info: {
    description: 'OCPI 2.2.1 StatusSchedule object';
    displayName: 'StatusSchedule';
  };
  attributes: {
    periodBegin: Schema.Attribute.DateTime & Schema.Attribute.Required;
    periodEnd: Schema.Attribute.DateTime;
    status: Schema.Attribute.Enumeration<
      [
        'AVAILABLE',
        'BLOCKED',
        'CHARGING',
        'INOPERATIVE',
        'OUTOFORDER',
        'PLANNED',
        'REMOVED',
        'RESERVED',
        'UNKNOWN',
      ]
    > &
      Schema.Attribute.Required;
  };
}

export interface PricePrice extends Struct.ComponentSchema {
  collectionName: 'components_price_prices';
  info: {
    description: '';
    displayName: 'Price';
    icon: 'priceTag';
  };
  attributes: {
    blockingFee: Schema.Attribute.Integer;
    blockingFeeBeginAtMin: Schema.Attribute.Integer;
    blockingFeeDailyFrom: Schema.Attribute.Time;
    blockingFeeMax: Schema.Attribute.Integer;
    blockingFreeDailyTo: Schema.Attribute.Time;
    KwhFee: Schema.Attribute.Integer;
    minChargingEnergy: Schema.Attribute.Integer;
    minChargingTime: Schema.Attribute.Integer;
    SessionFee: Schema.Attribute.Integer;
  };
}

export interface TariffBlockFeeSchedule extends Struct.ComponentSchema {
  collectionName: 'components_tariff_block_fee_schedules';
  info: {
    description: 'Zeitintervalle f\u00FCr Blockiergeb\u00FChren';
    displayName: 'Block Fee Schedule';
  };
  attributes: {
    dayOfWeek: Schema.Attribute.Enumeration<
      [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ]
    > &
      Schema.Attribute.Required;
    endHour: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 23;
          min: 0;
        },
        number
      >;
    gracePeriod: Schema.Attribute.Integer;
    maxFee: Schema.Attribute.Decimal;
    perMinute: Schema.Attribute.Decimal & Schema.Attribute.Required;
    startHour: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 23;
          min: 0;
        },
        number
      >;
  };
}

export interface TariffDailySchedule extends Struct.ComponentSchema {
  collectionName: 'components_tariff_daily_schedules';
  info: {
    description: 'Tagesbasierter Preisplan mit st\u00FCndlichen Raten';
    displayName: 'Daily Schedule';
    icon: 'rotate';
  };
  attributes: {
    dayOfWeek: Schema.Attribute.Enumeration<
      [
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
        'sunday',
      ]
    > &
      Schema.Attribute.Required;
    HourlyRate: Schema.Attribute.Component<'tariff.hourly-rate', true>;
  };
}

export interface TariffHourlyRate extends Struct.ComponentSchema {
  collectionName: 'components_tariff_hourly_rates';
  info: {
    description: 'St\u00FCndliche Preisdaten';
    displayName: 'Hourly Rate';
    icon: 'clock';
  };
  attributes: {
    epex_base: Schema.Attribute.Decimal;
    hourFrom: Schema.Attribute.Integer &
      Schema.Attribute.Required &
      Schema.Attribute.SetMinMax<
        {
          max: 23;
          min: 0;
        },
        number
      >;
    hourTo: Schema.Attribute.Integer &
      Schema.Attribute.SetMinMax<
        {
          max: 23;
        },
        number
      >;
    pricePerKwh: Schema.Attribute.Decimal & Schema.Attribute.Required;
    priceType: Schema.Attribute.Enumeration<['EPEX', 'Festpreis']> &
      Schema.Attribute.Required;
    sessionFee: Schema.Attribute.Decimal & Schema.Attribute.Required;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'invoice-position.invoice-position': InvoicePositionInvoicePosition;
      'ocpi.additional-geo-location': OcpiAdditionalGeoLocation;
      'ocpi.business-details': OcpiBusinessDetails;
      'ocpi.cdr-location': OcpiCdrLocation;
      'ocpi.cdr-token': OcpiCdrToken;
      'ocpi.display-text': OcpiDisplayText;
      'ocpi.energy-mix': OcpiEnergyMix;
      'ocpi.energy-source': OcpiEnergySource;
      'ocpi.environmental-impact': OcpiEnvironmentalImpact;
      'ocpi.exceptional-period': OcpiExceptionalPeriod;
      'ocpi.geo-location': OcpiGeoLocation;
      'ocpi.hours': OcpiHours;
      'ocpi.image': OcpiImage;
      'ocpi.ocpi-modul': OcpiOcpiModul;
      'ocpi.regular-hours': OcpiRegularHours;
      'ocpi.status-schedule': OcpiStatusSchedule;
      'price.price': PricePrice;
      'tariff.block-fee-schedule': TariffBlockFeeSchedule;
      'tariff.daily-schedule': TariffDailySchedule;
      'tariff.hourly-rate': TariffHourlyRate;
    }
  }
}
