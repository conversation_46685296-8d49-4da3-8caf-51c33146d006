#!/bin/bash

# Findet alle Dateien größer als 50KB im backend-Verzeichnis
find backend -type f -size +50k \
  -not -path "*/node_modules/*" \
  -not -path "*/dist/*" \
  -not -path "*/.git/*" \
  -not -path "*/.tmp/*" \
  -not -path "*/.strapi/*" \
  -not -path "*/\.*" | sort > large_files.txt

# Zeige Dateien mit ihrer Größe an
COUNT=$(wc -l < large_files.txt)
echo "Gefundene große Dateien: $COUNT"

while read file; do
  size=$(du -h "$file" | cut -f1)
  echo "$size  $file"
done < large_files.txt

# Gesamtgröße berechnen
TOTAL_SIZE=$(cat large_files.txt | xargs du -ch 2>/dev/null | tail -n 1 | cut -f1)
echo "Gesamtgröße aller großen Dateien: $TOTAL_SIZE"
