"use client";

import axios from "axios";
import { useRouter } from "next/navigation";
import type React from "react";
import { createContext, useContext, useEffect, useState } from "react";

// Typen
interface User {
	id: number;
	username: string;
	email: string;
	role?: {
		id: number;
		name: string;
		type: string;
	};
	blocked?: boolean;
	confirmed?: boolean;
}

interface AuthContextType {
	user: User | null;
	token: string | null;
	login: (identifier: string, password: string) => Promise<void>;
	logout: () => void;
	loading: boolean;
	error: string | null;
}

// Default-Werte für den Kontext
const defaultAuthContext: AuthContextType = {
	user: null,
	token: null,
	login: async () => {},
	logout: () => {},
	loading: true,
	error: null,
};

// Kontext erstellen
const AuthContext = createContext<AuthContextType>(defaultAuthContext);

// Provider-Komponente
export function AuthProvider({ children }: { children: React.ReactNode }) {
	const [user, setUser] = useState<User | null>(null);
	const [token, setToken] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();

	// Bei Mounting überprüfen, ob ein Token im localStorage ist und versuchen, den Benutzer zu laden
	useEffect(() => {
		const initAuth = async () => {
			setLoading(true);
			const storedToken = localStorage.getItem("strapiToken");

			// Timeout für die Authentifizierung setzen (10 Sekunden)
			const authTimeout = setTimeout(() => {
				console.warn("Authentifizierungs-Timeout erreicht");
				// Bei Timeout Token entfernen und User zurücksetzen
				localStorage.removeItem("strapiToken");
				document.cookie =
					"strapiToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
				setUser(null);
				setToken(null);
				setLoading(false);
				setError(
					"Die Authentifizierung hat zu lange gedauert. Bitte versuchen Sie es erneut.",
				);
			}, 10000); // 10 Sekunden Timeout

			if (storedToken) {
				try {
					// Benutzer mit gespeichertem Token laden
					await fetchUser(storedToken);
				} catch (error) {
					console.error("Fehler beim Laden des Benutzers:", error);
					// Bei Fehler Token entfernen und User zurücksetzen
					localStorage.removeItem("strapiToken");
					document.cookie =
						"strapiToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
					setUser(null);
					setToken(null);
				}
			}

			// Timeout löschen, wenn die Authentifizierung abgeschlossen ist
			clearTimeout(authTimeout);
			setLoading(false);
		};

		initAuth();
	}, []);

	// Funktion zum Laden des Benutzers mit Token
	const fetchUser = async (userToken: string) => {
		try {
			const API_URL =
				process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337";

			const response = await axios.get(
				`${API_URL}/api/users/me?populate=role`,
				{
					headers: {
						Authorization: `Bearer ${userToken}`,
					},
				},
			);

			if (response.data) {
				setUser(response.data);
				setToken(userToken);
			} else {
				throw new Error("Keine Benutzerdaten erhalten");
			}
		} catch (error) {
			console.error("Fehler beim Abrufen des Benutzers:", error);
			throw error;
		}
	};

	// Login-Funktion
	const login = async (identifier: string, password: string) => {
		setLoading(true);
		setError(null);

		try {
			const API_URL =
				process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337";

			const response = await axios.post(`${API_URL}/api/auth/local`, {
				identifier,
				password,
			});

			if (response.data?.jwt) {
				// Token in localStorage speichern
				localStorage.setItem("strapiToken", response.data.jwt);

				// Auch ein Cookie für die Middleware setzen
				document.cookie = `strapiToken=${response.data.jwt}; path=/; max-age=${60 * 60 * 24 * 30}`; // 30 Tage

				// Benutzer mit dem neuen Token laden
				await fetchUser(response.data.jwt);

				// Zur Startseite weiterleiten
				router.push("/dashboard");
			} else {
				throw new Error("Login fehlgeschlagen");
			}
		} catch (error: any) {
			console.error("Login-Fehler:", error);

			// Übersichtliche Fehlermeldung erstellen
			if (error.response?.data?.error) {
				setError(error.response.data.error.message);
			} else {
				setError(
					"Login fehlgeschlagen. Bitte überprüfen Sie Ihre Anmeldedaten.",
				);
			}
		} finally {
			setLoading(false);
		}
	};

	// Logout-Funktion
	const logout = () => {
		// Token aus localStorage entfernen
		localStorage.removeItem("strapiToken");

		// Cookie entfernen
		document.cookie =
			"strapiToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

		// Benutzer und Token zurücksetzen
		setUser(null);
		setToken(null);

		// Zur Login-Seite weiterleiten
		router.push("/login");
	};

	// Kontext-Wert
	const value = {
		user,
		token,
		login,
		logout,
		loading,
		error,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook zum Verwenden des Auth-Kontexts
export function useAuth() {
	const context = useContext(AuthContext);
	if (!context) {
		throw new Error(
			"useAuth muss innerhalb eines AuthProviders verwendet werden",
		);
	}
	return context;
}
