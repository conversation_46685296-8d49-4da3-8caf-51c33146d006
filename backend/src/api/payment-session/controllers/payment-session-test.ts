/**
 * Test controller for payment-session
 * Contains test endpoints for development and testing purposes
 */

export default {
  /**
   * Updates the history field of a payment session
   * This is a test endpoint for development purposes
   * 
   * @param {Object} ctx - Koa context
   * @returns {Object} Updated payment session
   */
  async updateHistory(ctx) {
    try {
      const { id } = ctx.params;
      const { history } = ctx.request.body;
      
      if (!id) {
        return ctx.badRequest('Payment session ID is required');
      }
      
      if (!history) {
        return ctx.badRequest('History data is required');
      }

      // Find the payment session
      const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
        documentId: id,
      });

      if (!paymentSession) {
        return ctx.notFound('Payment session not found');
      }

       const updatedHistory = await strapi.service("api::payment-session.payment-session").formatHistory(
        paymentSession,
           history
      );

      // Update the payment session
      const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
        documentId: id,
        data: {
          history:updatedHistory
        }
      });

      return {
        success: true,
        data: updatedPaymentSession
      };
    } catch (error) {
      console.error('Error updating payment session history:', error);
      return ctx.badRequest(`Error updating payment session history: ${error.message}`);
    }
  }
};
