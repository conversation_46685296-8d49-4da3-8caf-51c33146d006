{"collectionName": "components_ocpi_cdr_locations", "info": {"displayName": "CDR Location", "description": "OCPI CDR Location Component"}, "options": {}, "attributes": {"locationId": {"type": "string", "description": "Location ID"}, "name": {"type": "string", "description": "Location name"}, "address": {"type": "string", "description": "Location address"}, "city": {"type": "string", "description": "Location city"}, "postalCode": {"type": "string", "description": "Location postal code"}, "country": {"type": "string", "description": "Location country"}, "coordinates": {"type": "json", "description": "Location coordinates (latitude, longitude)"}, "evseId": {"type": "string", "description": "EVSE ID"}, "evseUid": {"type": "string", "description": "EVSE UID"}, "connectorId": {"type": "string", "description": "Connector ID"}, "connectorStandard": {"type": "string", "description": "Connector standard (e.g. CHADEMO, IEC_62196_T2)"}, "connectorFormat": {"type": "string", "description": "Connector format (e.g. SOCKET, CABLE)"}, "connectorPowerType": {"type": "string", "description": "Connector power type (e.g. AC_1_PHASE, DC)"}}}