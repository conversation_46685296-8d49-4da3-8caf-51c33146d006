import type React from "react";
import { FiRefreshCcw } from "react-icons/fi";
import { toast } from "react-toastify";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { formatDateTime } from "~/lib/utils";
import { apiClient } from "~/services/api";

export interface PaymentConnection {
	id: number;
	documentId: string;
	name: string;
	apiKey: string;
	apiUrl: string;
	type: "Prod" | "Test";
	mandants?:  any[] ;
	terminals?: any[] ;
	createdAt: string;
	updatedAt: string;
	lastWebhookInit: string;
}

interface PaymentConnectionTableProps {
	connections: PaymentConnection[];
	onRefresh: () => void;
	filters: {
		name: string;
		apiUrl: string;
		type: string;
	};
	onFilterChange: (name: string, value: string) => void;
}

const PaymentConnectionTable: React.FC<PaymentConnectionTableProps> = ({
	connections,
	onRefresh,
	filters,
	onFilterChange,
}) => {
	const handleInitializeWebhook = async (documentId: string) => {
		try {
			// Dummy endpoint for webhook initialization
			const response = await apiClient.post(
				`/api/payter-connections/${documentId}/initialize-webhook`,
			);
			// Refresh the connection list after initializing webhook
			onRefresh();
			// Show success message
			toast.success("Webhook wurde erfolgreich initialisiert");
		} catch (error) {
			console.error("Fehler beim Initialisieren des Webhooks:", error);
			toast.error("Fehler beim Initialisieren des Webhooks");
		}
	};
	console.log(connections)

	return (
		<div className="space-y-4">
			{/* Filter row */}
			<div className="grid grid-cols-4 gap-4">
				<div>
					<label
						htmlFor="name-filter"
						className="mb-1 block font-medium text-gray-500 text-xs"
					>
						Name
					</label>
					<Input
						id="name-filter"
						type="text"
						placeholder="Nach Name filtern..."
						value={filters.name}
						onChange={(e) => onFilterChange("name", e.target.value)}
						className="w-full"
					/>
				</div>
				<div>
					<label
						htmlFor="apiUrl-filter"
						className="mb-1 block font-medium text-gray-500 text-xs"
					>
						API URL
					</label>
					<Input
						id="apiUrl-filter"
						type="text"
						placeholder="Nach API URL filtern..."
						value={filters.apiUrl}
						onChange={(e) => onFilterChange("apiUrl", e.target.value)}
						className="w-full"
					/>
				</div>
				<div>
					<label
						htmlFor="type-filter"
						className="mb-1 block font-medium text-gray-500 text-xs"
					>
						Typ
					</label>
					<select
						id="type-filter"
						value={filters.type}
						onChange={(e) => onFilterChange("type", e.target.value)}
						className="h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-0"
					>
						<option value="">Alle</option>
						<option value="Prod">Prod</option>
						<option value="Test">Test</option>
					</select>
				</div>
			</div>

			{/* Table */}
			<div className="overflow-x-auto">
				<table className="min-w-full bg-white">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								Name
							</th>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								API Key
							</th>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								API URL
							</th>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								Typ
							</th>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								Mandanten
							</th>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								Terminals
							</th>
							<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
								Aktionen
							</th>
						</tr>
					</thead>
					<tbody className="divide-y divide-gray-200">
						{connections.length === 0 ? (
							<tr>
								<td
									colSpan={7}
									className="px-6 py-4 text-center text-gray-500 text-sm"
								>
									Keine Payment-Connections gefunden.
								</td>
							</tr>
						) : (
							connections.map((connection) => (
								<tr key={connection.documentId}>
									<td className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 text-sm">
										{connection.name}
									</td>
									<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
										{/* Show only first few characters of API key for security */}
										{connection.apiKey
											? `${connection.apiKey.substring(0, 8)}...`
											: "-"}
									</td>
									<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
										{connection.apiUrl}
									</td>
									<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
										{connection.type}
									</td>
									<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
										{connection.mandants?.length || 0}
									</td>
									<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
										{connection.terminals?.length || 0}
									</td>
									<td className="whitespace-nowrap px-6 py-4 font-medium text-sm">
										<div className="space-y-2">
											<Button
												onClick={() =>
													handleInitializeWebhook(connection.documentId)
												}
												variant="default"
												className="btn-primary flex items-center"
											>
												<FiRefreshCcw className="mr-1" />
												Initialisiere Webhook
											</Button>

											{connection.lastWebhookInit ? (
												<div className="text-gray-500 text-xs">
													Zuletzt initialisiert:{" "}
													{formatDateTime(connection.lastWebhookInit)}
												</div>
											) : (
												<div className="text-gray-400 text-xs">
													Noch nicht initialisiert
												</div>
											)}
										</div>
									</td>
								</tr>
							))
						)}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default PaymentConnectionTable;
