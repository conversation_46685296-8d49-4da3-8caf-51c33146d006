{"kind": "collectionType", "collectionName": "ocpi_connections", "info": {"singularName": "ocpi-connection", "pluralName": "ocpi-connections", "displayName": "OCPI Connection", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> von OCPI Verbindungen und deren Credentials"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "initialSecret": {"type": "string", "unique": false}, "sendSecret": {"type": "string", "unique": true}, "receivingSecret": {"type": "string"}, "ocpiVersion": {"type": "enumeration", "enum": ["v221"], "default": "v221"}, "operatorId": {"type": "string"}, "connectionUrl": {"type": "string"}, "mandants": {"type": "relation", "relation": "manyToMany", "target": "api::mandant.mandant", "mappedBy": "ocpi_connections"}, "role": {"type": "enumeration", "enum": ["sender", "receiver"], "required": true}, "connectionStatus": {"type": "enumeration", "enum": ["new", "pending", "registered", "active", "inactive", "disconnected"], "default": "new"}, "remoteParty": {"type": "json", "comment": "Information über die Gegenstelle (z.B. roles-Array mit party_id, country_code, usw.)"}, "remoteModules": {"type": "json", "comment": "Liste der von der Gegenstelle unterstützten Module und Endpunkte"}, "lastConnection": {"type": "datetime"}, "type": {"type": "enumeration", "enum": ["Prod", "Test", "<PERSON>"]}, "partyId": {"type": "string", "maxLength": 3}, "countryCode": {"type": "string", "maxLength": 2, "default": "DE"}, "companyName": {"type": "string"}}}