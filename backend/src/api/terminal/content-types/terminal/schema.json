{"kind": "collectionType", "collectionName": "terminals", "info": {"singularName": "terminal", "pluralName": "terminals", "displayName": "Terminal", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"serialNumber": {"type": "string", "unique": true}, "terminalName": {"type": "string"}, "evses": {"type": "relation", "relation": "manyToMany", "target": "api::ocpi-evse.ocpi-evse", "inversedBy": "terminals"}, "payment_sessions": {"type": "relation", "relation": "oneToMany", "target": "api::payment-session.payment-session", "mappedBy": "terminal"}, "online": {"type": "boolean"}, "terminalState": {"type": "enumeration", "enum": ["OUT_OF_ORDER", "ACTIVE", "MAINTENANCE", "IDLE"]}, "lastUpdate": {"type": "datetime"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "terminals"}, "location": {"type": "relation", "relation": "manyToOne", "target": "api::ocpi-location.ocpi-location", "inversedBy": "terminals"}, "payter_connection": {"type": "relation", "relation": "manyToOne", "target": "api::payter-connection.payter-connection", "inversedBy": "terminals"}, "currentLanguage": {"type": "enumeration", "enum": ["de", "en", "fr", "pl", "cz", "dk", "nl", "it", "es", "sv", "no"], "default": "de"}}}