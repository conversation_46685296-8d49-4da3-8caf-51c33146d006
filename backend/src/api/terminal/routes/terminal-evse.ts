/**
 * Terminal-EVSE Router für die Verwaltung der Beziehung zwischen Terminals und EVSEs
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/terminals/:documentId/evse-count',
      handler: 'terminal-evse.getEvseCount',
      config: {
        policies: [],
        middlewares: [],
      }
    },
    {
      method: 'POST',
      path: '/terminals/:documentId/send-evse-count',
      handler: 'terminal-evse.sendEvseCountToTerminal',
      config: {
        policies: [],
        middlewares: [],
      }
    }
  ]
};
