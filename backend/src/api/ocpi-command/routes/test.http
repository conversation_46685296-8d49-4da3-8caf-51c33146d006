POST https://beta.ocpi.longship.io/ocpi/2.2.1/commands/START_SESSION
authorization: Token cab555cdda1489c8da93419de1dc5091
content-type: application/json

{
  "response_url": "https://jr.payter.eulektro.de/api/ocpi/commands/response/startSession/aj8mdmndrejkve8ejqyh0rf7",
  "location_id": "DEEUlSE0889",
  "evse_uid": "b38dfa4c-2b06-4576-b1ef-0f121ca50404",
  "connector_id": 1,
  "authorization_reference": "aaaaa",
  "token": {
    "uid": "PAYTER4d13410a2c7bdf9be",
    "type": "RFID",
    "issuer": "Eulektro GmbH",
    "whitelist": "NEVER",
    "last_updated": "2023-04-22T20:33:34Z",
    "party_id": "EUL",
    "country_code": "DE",
    "contract_id": "n/a",
    "valid": true
  }
}
