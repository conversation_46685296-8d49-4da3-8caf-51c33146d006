// src/app/(protected)/tarife/page.tsx
"use client";
import * as React from "react";
import {type JSX, useEffect, useState} from "react";
import { useAuth } from "../../../components/AuthContext";
import { formatEuro } from "../../../utils/formatters";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "../../../components/ui/tabs";

interface DailySchedule {
  id: string;
  dayOfWeek: string; // Änderung: dayOfWeek ist ein String (enum-Wert wie 'monday')
  // Alte Struktur für Abwärtskompatibilität
  hourlyRates?: {
    id: string;
    hour: number;
    pricePerKwh: number;
    sessionFee: number;
    priceType: string;
  }[];
  // Neue Struktur gemäß Backend-Schema
  HourlyRate?: {
    id: string;
    hourFrom: number;
    hourTo?: number;
    pricePerKwh: number;
    sessionFee: number;
    priceType: string;
    epex_base?: number;
  }[];
}

interface BlockFeeSchedule {
  id: string;
  dayOfWeek: string; // Änderung: dayOfWeek ist ein String (enum-Wert wie 'monday')
  startHour: number;
  endHour: number;
  perMinute: number; // Entspricht dem Backend-Schema
  maxFee: number; // Entspricht dem Backend-Schema
  gracePeriod?: number; // Minutes before blocking fee starts
  // Alte Feldnamen für Abwärtskompatibilität - werden nicht mehr verwendet
  blockFeePerMinute?: number;
  maxBlockFee?: number;
}

interface Tariff {
  id: string;
  Name: string;
  ValidFrom: string;
  ValidTo: string;
  chargerType?: "AC" | "DC";
  dailySchedules: DailySchedule[];
  blockFeeSchedules: BlockFeeSchedule[];
  mandants: {
    id: string;
    name: string;
  }[];
  terminals?: {
    id: string;
    Name?: string;
    serialNumber?: string;
    documentId?: string;
  }[];
  ocpi_evses?: {
    id: string;
    uid?: string;
    evse_id?: string;
    documentId?: string;
    status?: string;
  }[];
  ocpi_locations?: {
    id: string;
    name?: string;
    documentId?: string;
    country_code?: string;
    party_id?: string;
  }[];
}

// Funktion zur Generierung einer Farbe basierend auf Preis
function getPriceColor(pricePerKwh: number, sessionFee: number): string {
  // Kombiniere beide Preise zu einem eindeutigen Wert
  const combinedPrice = `${pricePerKwh.toFixed(2)}-${sessionFee.toFixed(2)}`;

  // Vordefinierte Farben für verschiedene Preisstufen
  const priceColors: Record<string, string> = {
    // Hier können wir später mehr Farben hinzufügen, wenn wir bestimmte Preise hervorheben wollen
  };

  // Wenn wir eine vordefinierte Farbe haben, verwende diese
  if (priceColors[combinedPrice]) {
    return priceColors[combinedPrice];
  }

  // Ansonsten generiere eine Farbe basierend auf dem Preis
  // Wir verwenden eine Hashfunktion, um eine konsistente Farbe zu erzeugen
  let hash = 0;
  for (let i = 0; i < combinedPrice.length; i++) {
    hash = combinedPrice.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Konvertiere den Hash in eine HSL-Farbe
  // Wir verwenden HSL, weil wir so die Helligkeit und Sättigung kontrollieren können
  const h = Math.abs(hash % 360); // Farbton (0-360)
  const s = 70 + Math.abs((hash >> 8) % 30); // Sättigung (70-100%)
  const l = 80 + Math.abs((hash >> 16) % 10); // Helligkeit (80-90%)

  return `hsl(${h}, ${s}%, ${l}%)`;
}

// Hour cell component for the weekly calendar
interface HourCellProps {
  hour: number;
  day: number;
  isSelected: boolean;
  hasPrice?: boolean;
  priceInfo?: { pricePerKwh: number; sessionFee: number };
  onToggle: (hour: number, day: number, shiftKey?: boolean) => void;
  compact?: boolean;
}

function HourCell({
  hour,
  day,
  isSelected,
  hasPrice,
  priceInfo,
  onToggle,
  compact = false,
}: HourCellProps) {
  // Bestimme die Hintergrundfarbe basierend auf dem Preis
  let bgColor = "bg-gray-50";
  let borderColor = "border-gray-200";
  let textColor = "text-gray-600";

  if (isSelected) {
    bgColor = "bg-blue-100";
    borderColor = "border-blue-500";
    textColor = "text-blue-600";
  } else if (hasPrice && priceInfo) {
    // Generiere eine Farbe basierend auf dem Preis
    const priceColor = getPriceColor(
      priceInfo.pricePerKwh,
      priceInfo.sessionFee,
    );
    // Verwende eine benutzerdefinierte Hintergrundfarbe mit CSS-Variable
    bgColor = "";
    borderColor = "border-gray-300";
    textColor = "text-gray-700";
  }

  // Handler für Klick-Events mit Shift-Erkennung
  const handleClick = (e: React.MouseEvent) => {
    try {
      onToggle(hour, day, e.shiftKey);
    } catch (error) {
      console.error("Fehler beim Klick auf Zelle:", error);
    }
  };

  return (
    <div
      className={`border cursor-pointer transition h-6 ${compact ? "p-0.5 text-xs" : "p-2"} ${bgColor} ${borderColor} hover:opacity-80`}
      style={
        hasPrice && priceInfo
          ? {
              backgroundColor: getPriceColor(
                priceInfo.pricePerKwh,
                priceInfo.sessionFee,
              ),
            }
          : undefined
      }
      onClick={handleClick}
      title={
        hasPrice && priceInfo
          ? `${formatEuro(priceInfo.pricePerKwh)}€/kWh + ${formatEuro(priceInfo.sessionFee)}€ Sessiongebühr`
          : undefined
      }
    >
      <div className="flex items-center justify-center h-full">
        {!compact && <span>{hour}:00</span>}
        {hasPrice && (
          <span className={`${textColor} text-xs font-bold`}>✓</span>
        )}
        {!hasPrice && isSelected && (
          <span className="text-blue-600 text-xs">●</span>
        )}
      </div>
    </div>
  );
}

// Funktion zur Gruppierung von Preisen nach Tagen und Preisen
function groupPricesByDayAndPrice(hourlyRates: {
  [key: string]: { pricePerKwh: number; sessionFee: number };
}) {
  const dayNames = ["Mo", "Di", "Mi", "Do", "Fr"];

  // Gruppiere nach Tagen und Preisen
  const dayGroups: Record<string, Record<string, {
    price: { pricePerKwh: number; sessionFee: number };
    hours: number[];
  }>> = {};

  // Gruppiere Stunden nach Tagen und Preisen
  Object.entries(hourlyRates).forEach(([key, priceInfo]) => {
    const [dayStr, hourStr] = key.split("-");
    const day = dayStr || "0";
    const hour = parseInt(hourStr || "0", 10);
    const priceKey = `${priceInfo.pricePerKwh.toFixed(2)}-${priceInfo.sessionFee.toFixed(2)}`;

    // Initialisiere Tag, wenn noch nicht vorhanden
    if (!dayGroups[day]) {
      dayGroups[day] = {};
    }

    // Initialisiere Preisgruppe, wenn noch nicht vorhanden
    if (!dayGroups[day][priceKey]) {
      dayGroups[day][priceKey] = {
        price: priceInfo,
        hours: [],
      };
    }

    // Füge Stunde zur Preisgruppe hinzu
    dayGroups[day][priceKey].hours.push(hour);
  });

  return { dayGroups, dayNames };
}

// Funktion zur Gruppierung von Blockiergebühren nach Tagen und Zeiträumen
function groupBlockingFeesByDay(blockingFeeSchedules: {
  [key: string]: { perMinute: number; maxFee: number; gracePeriod: number };
}) {
  const dayNames = ["Mo", "Di", "Mi", "Do", "Fr"];

  // Gruppiere nach Tagen und Gebühren
  const dayGroups: Record<string, Record<string, {
    fee: { perMinute: number; maxFee: number; gracePeriod: number };
    hours: number[];
  }>> = {};

  // Gruppiere Stunden nach Tagen und Gebühren
  Object.entries(blockingFeeSchedules).forEach(([key, feeInfo]) => {
    const [dayStr, hourStr] = key.split("-");
    const day = dayStr || "0";
    const hour = parseInt(hourStr || "0", 10);
    const feeKey = `${feeInfo.perMinute.toFixed(2)}-${feeInfo.maxFee.toFixed(2)}-${feeInfo.gracePeriod}`;

    // Initialisiere Tag, wenn noch nicht vorhanden
    if (!dayGroups[day]) {
      dayGroups[day] = {};
    }

    // Initialisiere Gebührgruppe, wenn noch nicht vorhanden
    if (!dayGroups[day][feeKey]) {
      dayGroups[day][feeKey] = {
        fee: feeInfo,
        hours: [],
      };
    }

    // Füge Stunde zur Gebührgruppe hinzu
    dayGroups[day][feeKey].hours.push(hour);
  });

  return { dayGroups, dayNames };
}

export default function TarifePage() {
  const { user, token, loading } = useAuth();
  const [tariffs, setTariffs] = useState<Tariff[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTariff, setSelectedTariff] = useState<Tariff | null>(null);

  // State for the weekly calendar
  const [selectedHours, setSelectedHours] = useState<{
    [key: string]: boolean;
  }>({});
  const [hourlyPrice, setHourlyPrice] = useState<string>("0.00");
  const [sessionFee, setSessionFee] = useState<string>("0.00");
  // Speichert die zuletzt ausgewählte Zelle für Shift-Klick-Funktionalität
  const [lastSelectedCell, setLastSelectedCell] = useState<{
    day: number;
    hour: number;
  } | null>(null);

  // Tab state
  const [activeTab, setActiveTab] = useState<string>("price");

  // Blocking fee state
  const [selectedBlockingHours, setSelectedBlockingHours] = useState<{
    [key: string]: boolean;
  }>({});
  const [blockingFeePerMinute, setBlockingFeePerMinute] =
    useState<string>("0.10");
  const [maxBlockingFee, setMaxBlockingFee] = useState<string>("10.00");
  const [gracePeriod, setGracePeriod] = useState<string>("30");
  const [blockingFeeSchedules, setBlockingFeeSchedules] = useState<{
    [key: string]: { perMinute: number; maxFee: number; gracePeriod: number };
  }>({});

  // New tariff form state
  const [newTariff, setNewTariff] = useState({
    Name: "",
    ValidFrom: new Date().toISOString().split("T")[0],
    ValidTo: new Date(new Date().setFullYear(new Date().getFullYear() + 10))
      .toISOString()
      .split("T")[0],
    chargerType: "AC",
  });

  // Edit tariff form state
  const [editTariff, setEditTariff] = useState<{
    id: string;
    Name: string;
    ValidFrom: string;
    ValidTo: string;
    chargerType: string;
  } | null>(null);

  // Edit mode state for hourly rates and blocking fees
  const [editHourlyRates, setEditHourlyRates] = useState<{
    [key: string]: { pricePerKwh: number; sessionFee: number };
  }>({});

  const [editBlockingFeeSchedules, setEditBlockingFeeSchedules] = useState<{
    [key: string]: { perMinute: number; maxFee: number; gracePeriod: number };
  }>({});

  useEffect(() => {
    const fetchTariffs = async () => {
      if (loading || !token) return;

      try {
        // Verwende einen populate-Parameter mit tiefer Verschachtelung für Strapi 5.x
        const response = await fetch(
            `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/tariffs?populate[0]=mandants&populate[1]=blockFeeSchedules&populate[2]=dailySchedules.HourlyRate&populate[3]=terminals&populate[4]=ocpi_evses&populate[5]=ocpi_locations`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            },
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Fehler beim Laden der Tarife:', errorText);
          throw new Error(`Failed to fetch tariffs: ${errorText}`);
        }

        const data = await response.json();
        console.log('Geladene Tarifdaten:', data);
        setTariffs(data.data);
      } catch (err: unknown) {

        if (err instanceof Error) {
          setError(`Error loading tariffs: ${err.message}`);
        }

      setError(`Error loading tariffs`);
      console.error('Fehler beim Laden der Tarife:', err);

      } finally {
        setIsLoading(false);
      }
    };

    fetchTariffs();
  }, [token, loading]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setNewTariff((prev) => ({ ...prev, [name]: value }));
  };

  const handleEditInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setEditTariff((prev) => prev ? ({ ...prev, [name]: value }) : null);
  };

  // Hilfsfunktion zum Auswählen eines Bereichs von Zellen
  const selectCellRange = (
    startCell: { day: number; hour: number },
    endCell: { day: number; hour: number },
  ) => {
    // Erstelle ein neues Objekt mit den ausgewählten Stunden
    const newSelectedHours = { ...selectedHours };

    // Berechne die Gesamtzahl der Stunden zwischen den beiden Zellen
    const startTotalHour = startCell.day * 24 + startCell.hour;
    const endTotalHour = endCell.day * 24 + endCell.hour;

    // Bestimme Start- und Endpunkt (unabhängig von der Reihenfolge der Auswahl)
    const minTotalHour = Math.min(startTotalHour, endTotalHour);
    const maxTotalHour = Math.max(startTotalHour, endTotalHour);

    // Markiere alle Zellen im Bereich
    for (let totalHour = minTotalHour; totalHour <= maxTotalHour; totalHour++) {
      const day = Math.floor(totalHour / 24);
      const hour = totalHour % 24;

      // Nur Tage 0-4 (Mo-Fr) berücksichtigen
      if (day >= 0 && day <= 4) {
        const key = `${day}-${hour}`;
        newSelectedHours[key] = true;
      }
    }

    return newSelectedHours;
  };

  // Hilfsfunktion zum Gruppieren von aufeinanderfolgenden Stunden
  const formatHoursRanges = (hours: number[]): string => {
    if (hours.length === 0) return "";
    if (hours.length === 1) return `${hours[0]}:00`;

    // Sortiere die Stunden
    hours.sort((a, b) => a - b);

    // Gruppiere aufeinanderfolgende Stunden
    const ranges: { start: number; end: number }[] = [];
    // Sicherstellen, dass hours[0] nicht undefined ist
    const firstHour = hours[0] ?? 0;
    let currentRange: { start: number; end: number } = { start: firstHour, end: firstHour };

    for (let i = 1; i < hours.length; i++) {
      const currentHour = hours[i] ?? 0;
      if (currentHour === currentRange.end + 1) {
        // Erweitere den aktuellen Bereich
        currentRange.end = currentHour;
      } else {
        // Speichere den aktuellen Bereich und starte einen neuen
        ranges.push({ ...currentRange });
        currentRange = { start: currentHour, end: currentHour };
      }
    }

    // Füge den letzten Bereich hinzu
    ranges.push({ ...currentRange });

    // Formatiere die Bereiche
    return ranges
      .map((range) => {
        if (range.start === range.end) {
          return `${range.start}:00`;
        } else {
          return `${range.start}:00 bis ${range.end}:00`;
        }
      })
      .join(", ");
  };

  // Toggle selection of an hour cell
  const toggleHourSelection = (
    hour: number,
    day: number,
    shiftKey: boolean = false,
  ) => {
    try {
      const key = `${day}-${hour}`;
      const currentCell = { day, hour };

      // Wenn Shift gedrückt ist und es eine vorherige Auswahl gibt
      if (shiftKey && lastSelectedCell) {
        // Wähle den Bereich zwischen der letzten und der aktuellen Zelle aus
        const newSelectedHours = selectCellRange(lastSelectedCell, currentCell);
        setSelectedHours(newSelectedHours);
      } else {
        // Normales Umschalten der Auswahl
        setSelectedHours((prev) => ({
          ...prev,
          [key]: !prev[key],
        }));
      }

      // Aktualisiere die zuletzt ausgewählte Zelle
      setLastSelectedCell(currentCell);
    } catch (error) {
      console.error("Fehler in toggleHourSelection:", error);
    }
  };

  // Toggle all hours for a specific day
  const toggleAllHoursForDay = (day: number) => {
    try {
      // Check if all hours for this day are already selected
      const allSelected = Array.from({ length: 24 }, (_, hour) => {
        const key = `${day}-${hour}`;
        return !!selectedHours[key];
      }).every(Boolean);

      // If all are selected, deselect all; otherwise, select all
      const newSelectedHours = { ...selectedHours };

      for (let hour = 0; hour < 24; hour++) {
        const key = `${day}-${hour}`;
        newSelectedHours[key] = !allSelected;
      }

      setSelectedHours(newSelectedHours);

      // Aktualisiere die zuletzt ausgewählte Zelle auf die letzte Zelle des Tages
      if (!allSelected) {
        setLastSelectedCell({ day, hour: 23 });
      }
    } catch (error) {
      console.error("Fehler in toggleAllHoursForDay:", error);
    }
  };

  // Toggle all days for a specific hour
  const toggleAllHoursForTime = (hour: number) => {
    try {
      // Check if all days for this hour are already selected
      const allSelected = Array.from({ length: 5 }, (_, day) => {
        const key = `${day}-${hour}`;
        return !!selectedHours[key];
      }).every(Boolean);

      // If all are selected, deselect all; otherwise, select all
      const newSelectedHours = { ...selectedHours };

      for (let day = 0; day < 5; day++) {
        const key = `${day}-${hour}`;
        newSelectedHours[key] = !allSelected;
      }

      setSelectedHours(newSelectedHours);

      // Aktualisiere die zuletzt ausgewählte Zelle auf die letzte Zelle der Stunde
      if (!allSelected) {
        setLastSelectedCell({ day: 4, hour });
      }
    } catch (error) {
      console.error("Fehler in toggleAllHoursForTime:", error);
    }
  };

  // State für Erfolgsmeldung
  const [successMessage, setSuccessMessage] = useState<string>("");

  // Apply price to selected hours
  const applyPriceToSelectedHours = () => {
    // Konvertiere die Preise zu Zahlen
    const pricePerKwh = parseFloat(hourlyPrice);
    const sessionFeeValue = parseFloat(sessionFee);

    if (isNaN(pricePerKwh) || isNaN(sessionFeeValue)) {
      setError("Bitte geben Sie gültige Preise ein");
      setSuccessMessage("");
      return;
    }

    // Speichere die Preise für die ausgewählten Stunden
    const updatedRates = { ...hourlyRates };

    Object.keys(selectedHours).forEach((key) => {
      if (selectedHours[key]) {
        updatedRates[key] = {
          pricePerKwh,
          sessionFee: sessionFeeValue,
        };
      }
    });

    setHourlyRates(updatedRates);

    // Zeige Erfolgsmeldung
    const selectedCount = Object.values(selectedHours).filter(Boolean).length;
    setError("");
    setSuccessMessage(`Preise für ${selectedCount} Stunde(n) festgelegt`);

    // Leere die Auswahl
    setSelectedHours({});
  };

  // Delete prices for selected hours
  const deleteSelectedPrices = () => {
    // Prüfe, ob Stunden ausgewählt sind
    const hasSelected = Object.values(selectedHours).some(
      (selected) => selected,
    );
    if (!hasSelected) {
      setError("Bitte wählen Sie mindestens eine Stunde aus");
      setSuccessMessage("");
      return;
    }

    // Entferne die Preise für die ausgewählten Stunden
    const updatedRates = { ...hourlyRates };

    let deletedCount = 0;
    Object.keys(selectedHours).forEach((key) => {
      if (selectedHours[key] && updatedRates[key]) {
        delete updatedRates[key];
        deletedCount++;
      }
    });

    setHourlyRates(updatedRates);

    // Zeige Erfolgsmeldung
    setError("");
    if (deletedCount > 0) {
      setSuccessMessage(`Preise für ${deletedCount} Stunde(n) gelöscht`);
    } else {
      setSuccessMessage("Keine Preise zum Löschen gefunden");
    }

    // Leere die Auswahl
    setSelectedHours({});
  };

  // Reset all prices
  const resetAllPrices = () => {
    // Bestätigungsdialog
    if (Object.keys(hourlyRates).length === 0) {
      setError("Keine Preise zum Zurücksetzen vorhanden");
      setSuccessMessage("");
      return;
    }

    if (
      window.confirm(
        "Möchten Sie wirklich alle konfigurierten Preise zurücksetzen?",
      )
    ) {
      setHourlyRates({});
      setSelectedHours({});
      setError("");
      setSuccessMessage("Alle Preise wurden zurückgesetzt");
    }
  };

  // Toggle blocking hour selection
  const toggleBlockingHourSelection = (
    day: number,
    hour: number,
    shiftKey: boolean = false,
  ) => {
    const cellKey = `${day}-${hour}`;

    // Wenn Shift gedrückt wird und es eine vorherige Auswahl gibt, wähle alle Zellen dazwischen aus
    if (shiftKey && lastSelectedCell) {
      const startDay = Math.min(day, lastSelectedCell.day);
      const endDay = Math.max(day, lastSelectedCell.day);
      const startHour = Math.min(hour, lastSelectedCell.hour);
      const endHour = Math.max(hour, lastSelectedCell.hour);

      const newSelection = { ...selectedBlockingHours };

      // Setze alle Zellen im Bereich
      for (let d = startDay; d <= endDay; d++) {
        for (let h = startHour; h <= endHour; h++) {
          const key = `${d}-${h}`;
          newSelection[key] = true;
        }
      }

      setSelectedBlockingHours(newSelection);
    } else {
      // Normale Umschaltung für eine einzelne Zelle
      setSelectedBlockingHours((prev) => ({
        ...prev,
        [cellKey]: !prev[cellKey],
      }));
    }

    // Aktualisiere die zuletzt ausgewählte Zelle
    setLastSelectedCell({ day, hour });
  };

  // Apply blocking fee to selected hours
  const applyBlockingFeeToSelectedHours = () => {
    // Konvertiere die Werte zu Zahlen
    const perMinute = parseFloat(blockingFeePerMinute);
    const maxFee = parseFloat(maxBlockingFee);
    const gracePeriodValue = parseInt(gracePeriod, 10);

    if (isNaN(perMinute) || isNaN(maxFee) || isNaN(gracePeriodValue)) {
      setError("Bitte geben Sie gültige Werte ein");
      setSuccessMessage("");
      return;
    }

    // Speichere die Blockiergebühren für die ausgewählten Stunden
    const updatedSchedules = { ...blockingFeeSchedules };

    Object.keys(selectedBlockingHours).forEach((key) => {
      if (selectedBlockingHours[key]) {
        updatedSchedules[key] = {
          perMinute,
          maxFee,
          gracePeriod: gracePeriodValue,
        };
      }
    });

    setBlockingFeeSchedules(updatedSchedules);

    // Zeige Erfolgsmeldung
    const selectedCount = Object.values(selectedBlockingHours).filter(
      Boolean,
    ).length;
    setError("");
    setSuccessMessage(
      `Blockiergebühren für ${selectedCount} Stunde(n) festgelegt`,
    );

    // Leere die Auswahl
    setSelectedBlockingHours({});
  };

  // Delete blocking fees for selected hours
  const deleteBlockingFeesForSelectedHours = () => {
    const updatedSchedules = { ...blockingFeeSchedules };
    let deletedCount = 0;

    Object.keys(selectedBlockingHours).forEach((key) => {
      if (selectedBlockingHours[key] && updatedSchedules[key]) {
        delete updatedSchedules[key];
        deletedCount++;
      }
    });

    setBlockingFeeSchedules(updatedSchedules);

    // Zeige Erfolgsmeldung
    setError("");
    if (deletedCount > 0) {
      setSuccessMessage(
        `Blockiergebühren für ${deletedCount} Stunde(n) gelöscht`,
      );
    } else {
      setSuccessMessage("Keine Blockiergebühren zum Löschen gefunden");
    }

    // Leere die Auswahl
    setSelectedBlockingHours({});
  };

  // Reset all blocking fees
  const resetAllBlockingFees = () => {
    // Bestätigungsdialog
    if (Object.keys(blockingFeeSchedules).length === 0) {
      setError("Keine Blockiergebühren zum Zurücksetzen vorhanden");
      setSuccessMessage("");
      return;
    }

    if (
      window.confirm(
        "Möchten Sie wirklich alle konfigurierten Blockiergebühren zurücksetzen?",
      )
    ) {
      setBlockingFeeSchedules({});
      setSelectedBlockingHours({});
      setError("");
      setSuccessMessage("Alle Blockiergebühren wurden zurückgesetzt");
    }
  };

  // Toggle all blocking hours for a specific time
  const toggleAllBlockingHoursForTime = (hour: number) => {
    const newSelection = { ...selectedBlockingHours };
    const allSelected = Array.from(
      { length: 5 },
      (_, day) => `${day}-${hour}`,
    ).every((key) => selectedBlockingHours[key]);

    // Wenn alle ausgewählt sind, deselektiere alle, ansonsten selektiere alle
    Array.from({ length: 5 }, (_, day) => {
      const key = `${day}-${hour}`;
      newSelection[key] = !allSelected;
    });

    setSelectedBlockingHours(newSelection);
  };

  // Toggle all blocking hours for a specific day
  const toggleAllBlockingHoursForDay = (day: number) => {
    const newSelection = { ...selectedBlockingHours };
    const allSelected = Array.from(
      { length: 24 },
      (_, hour) => `${day}-${hour}`,
    ).every((key) => selectedBlockingHours[key]);

    // Wenn alle ausgewählt sind, deselektiere alle, ansonsten selektiere alle
    Array.from({ length: 24 }, (_, hour) => {
      const key = `${day}-${hour}`;
      newSelection[key] = !allSelected;
    });

    setSelectedBlockingHours(newSelection);
  };

  // Check if any hours are selected
  const hasSelectedHours = Object.values(selectedHours).some(
    (selected) => selected,
  );

  // Speichert die Preise für ausgewählte Stunden
  const [hourlyRates, setHourlyRates] = useState<{
    [key: string]: { pricePerKwh: number; sessionFee: number };
  }>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Leere Erfolgsmeldung und Fehlermeldung
    setSuccessMessage("");
    setError("");

    if (!token) return;

    try {
      // Erstelle die Tagesprofile basierend auf den gespeicherten Preisen
      const dailySchedules: Array<{
        dayOfWeek: string;
        HourlyRate: Array<{
          hourFrom: number;
          hourTo: number;
          pricePerKwh: number;
          sessionFee: number;
          priceType: string;
        }>;
      }> = [];

      // Gruppiere die Stunden nach Wochentagen
      const hoursByDay: Record<string, Record<string, { pricePerKwh: number; sessionFee: number }>> = {};

      // Initialisiere die Tage
      for (let day = 0; day < 5; day++) {
        hoursByDay[day.toString()] = {};
      }

      // Fülle die Stunden pro Tag
      Object.entries(hourlyRates).forEach(([key, rates]) => {
        const [dayStr, hourStr] = key.split("-");
        const day = dayStr || "0";
        const hour = hourStr || "0";
        if (hoursByDay[day]) {
          hoursByDay[day][hour] = rates;
        }
      });

      // Erstelle die Tagesprofile
      Object.entries(hoursByDay).forEach(([day, hours]) => {
        if (Object.keys(hours).length > 0) {
          const dayMapping: Record<string, string> = {
            "0": "monday",
            "1": "tuesday",
            "2": "wednesday",
            "3": "thursday",
            "4": "friday",
          };
          const dayOfWeek = dayMapping[day] || "monday";

          const hourlyRateEntries = Object.entries(hours).map(
            ([hour, rates]) => {
              const hourFromValue = parseInt(hour);
              return {
                hourFrom: hourFromValue,
                hourTo: hourFromValue < 23 ? hourFromValue + 1 : 23, // Stelle sicher, dass hourTo nicht größer als 23 ist
                pricePerKwh: rates.pricePerKwh,
                sessionFee: rates.sessionFee,
                priceType: "Festpreis",
              };
            },
          );

          dailySchedules.push({
            dayOfWeek,
            HourlyRate: hourlyRateEntries,
          });
        }
      });

      // Erstelle die Blockiergebühren-Zeitpläne basierend auf den gespeicherten Gebühren
      const blockFeeSchedules: Array<{
        dayOfWeek: string;
        startHour: number;
        endHour: number;
        perMinute: number;
        maxFee: number;
        gracePeriod: number;
        blockFeePerMinute: number;
        maxBlockFee: number;
      }> = [];

      // Gruppiere die Stunden nach Wochentagen
      const blockingFeesByDay: Record<string, Record<string, {
        perMinute: number;
        maxFee: number;
        gracePeriod: number;
      }>> = {};

      // Initialisiere die Tage
      for (let day = 0; day < 5; day++) {
        blockingFeesByDay[day.toString()] = {};
      }

      // Fülle die Stunden pro Tag
      Object.entries(blockingFeeSchedules).forEach(([key, fees]) => {
        const [dayStr, hourStr] = key.split("-");
        const day = dayStr || "0";
        const hour = hourStr || "0";
        if (blockingFeesByDay[day]) {
          blockingFeesByDay[day][hour] = fees;
        }
      });

      // Erstelle die Blockiergebühren-Zeitpläne
      Object.entries(blockingFeesByDay).forEach(([day, hours]) => {
        if (Object.keys(hours).length > 0) {
          const dayMapping: Record<string, string> = {
            "0": "monday",
            "1": "tuesday",
            "2": "wednesday",
            "3": "thursday",
            "4": "friday",
          };
          const dayOfWeek = dayMapping[day] || "monday";

          // Gruppiere aufeinanderfolgende Stunden mit gleichen Gebühren
          const hourEntries = Object.entries(hours);
          if (hourEntries.length > 0) {
            // Sortiere nach Stunden
            hourEntries.sort((a, b) => parseInt(a[0]) - parseInt(b[0]));

            // Gruppiere nach Gebühren
            const feeGroups: Array<{
              startHour: number;
              endHour: number;
              fee: { perMinute: number; maxFee: number; gracePeriod: number };
            }> = [];
            let currentGroup: {
              startHour: number;
              endHour: number;
              fee: { perMinute: number; maxFee: number; gracePeriod: number };
            } | null = null;

            hourEntries.forEach(([hourStr, fee], index) => {
              const hour = parseInt(hourStr);
              const feeKey = `${fee.perMinute}-${fee.maxFee}-${fee.gracePeriod}`;

              if (!currentGroup) {
                // Erste Stunde
                currentGroup = {
                  startHour: hour,
                  endHour: hour,
                  fee,
                };
              } else {
                const currentFeeKey = `${currentGroup.fee.perMinute}-${currentGroup.fee.maxFee}-${currentGroup.fee.gracePeriod}`;

                if (
                  feeKey === currentFeeKey &&
                  hour === currentGroup.endHour + 1
                ) {
                  // Aufeinanderfolgende Stunde mit gleicher Gebühr
                  currentGroup.endHour = hour;
                } else {
                  // Neue Gebühr oder Lücke in den Stunden
                  feeGroups.push(currentGroup);
                  currentGroup = {
                    startHour: hour,
                    endHour: hour,
                    fee,
                  };
                }
              }

              // Letzte Gruppe hinzufügen
              if (index === hourEntries.length - 1 && currentGroup) {
                feeGroups.push(currentGroup);
              }
            });

            // Füge die Blockiergebühren-Zeitpläne hinzu
            feeGroups.forEach((group) => {
              blockFeeSchedules.push({
                dayOfWeek,
                startHour: group.startHour,
                endHour: group.endHour,
                perMinute: group.fee.perMinute,
                maxFee: group.fee.maxFee,
                gracePeriod: group.fee.gracePeriod,
                // Entferne die Abwärtskompatibilitätsfelder, da sie nicht im Schema definiert sind
                blockFeePerMinute: group.fee.perMinute,
                maxBlockFee: group.fee.maxFee,

              });
            });
          }
        }
      });

      // Erstelle den Tarif
      // Für Debugging-Zwecke: Logge die Daten, die an die API gesendet werden
      const requestData = {
        data: {
          Name: newTariff.Name,
          ValidFrom: new Date(newTariff.ValidFrom || new Date().toISOString()).toISOString(),
          ValidTo: new Date(newTariff.ValidTo || new Date().toISOString()).toISOString(),
          chargerType: newTariff.chargerType,
          dailySchedules,
          blockFeeSchedules,
        },
      };
      console.log('Sending tariff data to API:', JSON.stringify(requestData, null, 2));

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/tariffs`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestData),
        },
      );

      if (!response.ok) {
        // Versuche, die Fehlerantwort zu lesen
        const errorText = await response.text();
        console.error('API-Fehlerantwort:', errorText);
        try {
          const errorJson = JSON.parse(errorText);
          throw new Error(`Fehler beim Erstellen des Tarifs: ${errorJson.error?.message || JSON.stringify(errorJson)}`);
        } catch (parseError) {
          throw new Error(`Fehler beim Erstellen des Tarifs: ${errorText || response.statusText}`);
        }
      }

      // Erfolgsmeldung und Modal schließen
      setShowModal(false);

      // Tarife neu laden mit populate-Parameter mit tiefer Verschachtelung für Strapi 5.x
      const fetchTariffsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/tariffs?populate[0]=mandants&populate[1]=blockFeeSchedules&populate[2]=dailySchedules.HourlyRate&populate[3]=terminals&populate[4]=ocpi_evses&populate[5]=ocpi_locations`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (fetchTariffsResponse.ok) {
        const data = await fetchTariffsResponse.json();
        console.log('Neu geladene Tarifdaten nach Erstellung:', data);
        setTariffs(data.data);
      } else {
        const errorText = await fetchTariffsResponse.text();
        console.error('Fehler beim Neuladen der Tarife:', errorText);
      }

      // Formular zurücksetzen
      setNewTariff({
        Name: "",
        ValidFrom: new Date().toISOString().split("T")[0],
        ValidTo: new Date(new Date().setFullYear(new Date().getFullYear() + 10))
          .toISOString()
          .split("T")[0],
        chargerType: "AC",
      });
      // Preise zurücksetzen
      setHourlyRates({});
      setSelectedHours({});
      setHourlyPrice("0.00");
      setSessionFee("0.00");
      // Blockiergebühren zurücksetzen
      setBlockingFeeSchedules({});
      setSelectedBlockingHours({});
      setBlockingFeePerMinute("0.10");
      setMaxBlockingFee("10.00");
      setGracePeriod("30");
      // Tab zurücksetzen
      setActiveTab("price");
    } catch (err) {
      console.error("Fehler beim Erstellen des Tarifs:", err);
      setError("Fehler beim Erstellen des Tarifs");
    }
  };

  const handleTariffClick = (tariff: Tariff) => {
    console.log('Tarif zum Bearbeiten ausgewählt:', tariff);
    console.log('Verknüpfte Terminals:', tariff.terminals);
    console.log('Verknüpfte EVSEs:', tariff.ocpi_evses);
    console.log('Verknüpfte Standorte:', tariff.ocpi_locations);
    console.log('Verknüpfte Mandanten:', tariff.mandants);
    console.log('Benutzerrolle:', user?.role?.name);
    setSelectedTariff(tariff);
    // Initialize edit form with tariff data
    setEditTariff({
      id: tariff.id,
      Name: tariff.Name,
      ValidFrom: new Date(tariff.ValidFrom).toISOString().split('T')[0] || "",
      ValidTo: new Date(tariff.ValidTo).toISOString().split('T')[0] || "",
      chargerType: tariff.chargerType || 'AC',
    });

    // Initialize hourly rates and blocking fees from tariff data
    const newHourlyRates: { [key: string]: { pricePerKwh: number; sessionFee: number } } = {};
    const newBlockingFeeSchedules: { [key: string]: { perMinute: number; maxFee: number; gracePeriod: number } } = {};

    // Process daily schedules
    if (tariff.dailySchedules && Array.isArray(tariff.dailySchedules)) {
      tariff.dailySchedules.forEach(schedule => {
        const dayMapping: Record<string, number> = {
          'monday': 0,
          'tuesday': 1,
          'wednesday': 2,
          'thursday': 3,
          'friday': 4,
          'saturday': 5,
          'sunday': 6
        };

        const day = dayMapping[schedule.dayOfWeek.toString()];

        if (day !== undefined && schedule.HourlyRate && Array.isArray(schedule.HourlyRate)) {
          schedule.HourlyRate.forEach(rate => {
            // Verwende hourFrom statt hour
            const hourFrom = rate.hourFrom;
            const key = `${day}-${hourFrom}`;
            newHourlyRates[key] = {
              pricePerKwh: rate.pricePerKwh,
              sessionFee: rate.sessionFee
            };
          });
        }
      });
    }

    // Process blocking fee schedules
    if (tariff.blockFeeSchedules && Array.isArray(tariff.blockFeeSchedules)) {
      tariff.blockFeeSchedules.forEach(schedule => {
        const dayMapping: Record<string, number> = {
          'monday': 0,
          'tuesday': 1,
          'wednesday': 2,
          'thursday': 3,
          'friday': 4,
          'saturday': 5,
          'sunday': 6
        };

        const day = dayMapping[schedule.dayOfWeek.toString()];

        if (day !== undefined) {
          for (let hour = schedule.startHour; hour <= schedule.endHour; hour++) {
            const key = `${day}-${hour}`;
            newBlockingFeeSchedules[key] = {
              perMinute: schedule.perMinute || 0,
              maxFee: schedule.maxFee || 0,
              gracePeriod: schedule.gracePeriod || 0
            };
          }
        }
      });
    }

    console.log('Stündliche Raten für Bearbeitung:', newHourlyRates);
    console.log('Blockiergebühren für Bearbeitung:', newBlockingFeeSchedules);
    setEditHourlyRates(newHourlyRates);
    setEditBlockingFeeSchedules(newBlockingFeeSchedules);
  };

  const handleEditTariff = () => {
    setShowEditModal(true);
    setSelectedTariff(null); // Close the detail view
  };

  const handleUpdateTariff = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editTariff || !token) return;

    // Leere Erfolgsmeldung und Fehlermeldung
    setSuccessMessage("");
    setError("");

    try {
      // Erstelle die Tagesprofile basierend auf den gespeicherten Preisen
      const dailySchedules: Array<{
        dayOfWeek: string;
        HourlyRate: Array<{
          hourFrom: number;
          hourTo: number;
          pricePerKwh: number;
          sessionFee: number;
          priceType: string;
        }>;
      }> = [];

      // Gruppiere die Stunden nach Wochentagen
      const hoursByDay: Record<string, Record<string, { pricePerKwh: number; sessionFee: number }>> = {};

      // Initialisiere die Tage
      for (let day = 0; day < 5; day++) {
        hoursByDay[day.toString()] = {};
      }

      // Fülle die Stunden pro Tag
      Object.entries(editHourlyRates).forEach(([key, rates]) => {
        const [dayStr, hourStr] = key.split("-");
        const day = dayStr || "0";
        const hour = hourStr || "0";
        if (hoursByDay[day]) {
          hoursByDay[day][hour] = rates;
        }
      });

      // Erstelle die Tagesprofile
      Object.entries(hoursByDay).forEach(([day, hours]) => {
        if (Object.keys(hours).length > 0) {
          const dayMapping: Record<string, string> = {
            "0": "monday",
            "1": "tuesday",
            "2": "wednesday",
            "3": "thursday",
            "4": "friday",
          };
          const dayOfWeek = dayMapping[day] || "monday";

          const hourlyRateEntries = Object.entries(hours).map(
            ([hour, rates]) => {
              const hourFromValue = parseInt(hour);
              return {
                hourFrom: hourFromValue,
                hourTo: hourFromValue < 23 ? hourFromValue + 1 : 23, // Stelle sicher, dass hourTo nicht größer als 23 ist
                pricePerKwh: rates.pricePerKwh,
                sessionFee: rates.sessionFee,
                priceType: "Festpreis",
              };
            },
          );

          dailySchedules.push({
            dayOfWeek,
            HourlyRate: hourlyRateEntries,
          });
        }
      });

      // Erstelle die Blockiergebühren-Zeitpläne basierend auf den gespeicherten Gebühren
      const blockFeeSchedules: Array<{
        dayOfWeek: string;
        startHour: number;
        endHour: number;
        perMinute: number;
        maxFee: number;
        gracePeriod: number;
        blockFeePerMinute: number;
        maxBlockFee: number;
      }> = [];

      // Gruppiere die Stunden nach Wochentagen
      const blockingFeesByDay: Record<string, Record<string, {
        perMinute: number;
        maxFee: number;
        gracePeriod: number;
      }>> = {};

      // Initialisiere die Tage
      for (let day = 0; day < 5; day++) {
        blockingFeesByDay[day.toString()] = {};
      }

      // Fülle die Stunden pro Tag
      Object.entries(editBlockingFeeSchedules).forEach(([key, fees]) => {
        const [dayStr, hourStr] = key.split("-");
        const day = dayStr || "0";
        const hour = hourStr || "0";
        if (blockingFeesByDay[day]) {
          blockingFeesByDay[day][hour] = fees;
        }
      });

      // Erstelle die Blockiergebühren-Zeitpläne
      Object.entries(blockingFeesByDay).forEach(([day, hours]) => {
        if (Object.keys(hours).length > 0) {
          const dayMapping: Record<string, string> = {
            "0": "monday",
            "1": "tuesday",
            "2": "wednesday",
            "3": "thursday",
            "4": "friday",
          };
          const dayOfWeek = dayMapping[day] || "monday";

          // Gruppiere aufeinanderfolgende Stunden mit gleichen Gebühren
          const hourEntries = Object.entries(hours);
          if (hourEntries.length > 0) {
            // Sortiere nach Stunden
            hourEntries.sort((a, b) => parseInt(a[0]) - parseInt(b[0]));

            // Gruppiere nach Gebühren
            const feeGroups: Array<{
              startHour: number;
              endHour: number;
              fee: { perMinute: number; maxFee: number; gracePeriod: number };
            }> = [];
            let currentGroup: {
              startHour: number;
              endHour: number;
              fee: { perMinute: number; maxFee: number; gracePeriod: number };
            } | null = null;

            hourEntries.forEach(([hourStr, fee], index) => {
              const hour = parseInt(hourStr);
              const feeKey = `${fee.perMinute}-${fee.maxFee}-${fee.gracePeriod}`;

              if (!currentGroup) {
                // Erste Stunde
                currentGroup = {
                  startHour: hour,
                  endHour: hour,
                  fee,
                };
              } else {
                const currentFeeKey = `${currentGroup.fee.perMinute}-${currentGroup.fee.maxFee}-${currentGroup.fee.gracePeriod}`;

                if (
                  feeKey === currentFeeKey &&
                  hour === currentGroup.endHour + 1
                ) {
                  // Aufeinanderfolgende Stunde mit gleicher Gebühr
                  currentGroup.endHour = hour;
                } else {
                  // Neue Gebühr oder Lücke in den Stunden
                  feeGroups.push(currentGroup);
                  currentGroup = {
                    startHour: hour,
                    endHour: hour,
                    fee,
                  };
                }
              }

              // Letzte Gruppe hinzufügen
              if (index === hourEntries.length - 1 && currentGroup) {
                feeGroups.push(currentGroup);
              }
            });

            // Füge die Blockiergebühren-Zeitpläne hinzu
            feeGroups.forEach((group) => {
              blockFeeSchedules.push({
                dayOfWeek,
                startHour: group.startHour,
                endHour: group.endHour,
                perMinute: group.fee.perMinute,
                maxFee: group.fee.maxFee,
                gracePeriod: group.fee.gracePeriod,
                blockFeePerMinute:group.fee.perMinute,
                maxBlockFee: group.fee.maxFee,
                // Entferne die Abwärtskompatibilitätsfelder, da sie nicht im Schema definiert sind
              });
            });
          }
        }
      });

      // Update den Tarif
      // Für Debugging-Zwecke: Logge die Daten, die an die API gesendet werden
      const requestData = {
        data: {
          Name: editTariff.Name,
          ValidFrom: new Date(editTariff.ValidFrom || new Date().toISOString()).toISOString(),
          ValidTo: new Date(editTariff.ValidTo || new Date().toISOString()).toISOString(),
          chargerType: editTariff.chargerType,
          dailySchedules,
          blockFeeSchedules,
        },
      };
      console.log('Sending tariff update data to API:', JSON.stringify(requestData, null, 2));

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/tariffs/${editTariff.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestData),
        },
      );

      if (!response.ok) {
        // Versuche, die Fehlerantwort zu lesen
        const errorText = await response.text();
        console.error('API-Fehlerantwort bei Update:', errorText);
        try {
          const errorJson = JSON.parse(errorText);
          throw new Error(`Fehler beim Aktualisieren des Tarifs: ${errorJson.error?.message || JSON.stringify(errorJson)}`);
        } catch (parseError) {
          throw new Error(`Fehler beim Aktualisieren des Tarifs: ${errorText || response.statusText}`);
        }
      }

      // Erfolgsmeldung und Modal schließen
      setShowEditModal(false);

      // Tarife neu laden mit populate-Parameter mit tiefer Verschachtelung für Strapi 5.x
      const fetchTariffsResponse = await fetch(
        `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/tariffs?populate[0]=mandants&populate[1]=blockFeeSchedules&populate[2]=dailySchedules.HourlyRate&populate[3]=terminals&populate[4]=ocpi_evses&populate[5]=ocpi_locations`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      );

      if (fetchTariffsResponse.ok) {
        const data = await fetchTariffsResponse.json();
        console.log('Neu geladene Tarifdaten nach Update:', data);
        setTariffs(data.data);
        setSuccessMessage("Tarif erfolgreich aktualisiert");
      } else {
        const errorText = await fetchTariffsResponse.text();
        console.error('Fehler beim Neuladen der Tarife nach Update:', errorText);
      }

      // Formular zurücksetzen
      setEditTariff(null);
      setEditHourlyRates({});
      setEditBlockingFeeSchedules({});

    } catch (err) {
      console.error("Fehler beim Aktualisieren des Tarifs:", err);
      setError("Fehler beim Aktualisieren des Tarifs");
    }
  };

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-white p-6 shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="font-bold text-2xl">Tarife</h1>
        <button
          onClick={() => setShowModal(true)}
          className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          <i className="fas fa-plus mr-2" />
          Tarif hinzufügen
        </button>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-100 p-4 text-red-700">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="mb-4 rounded-md bg-green-100 p-4 text-green-700">
          {successMessage}
        </div>
      )}

      {isLoading ? (
        <div className="py-4 text-center">Loading tariffs...</div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {tariffs.length === 0 ? (
            <div className="col-span-full py-8 text-center text-gray-500">
              Keine Tarife vorhanden. Fügen Sie einen neuen Tarif hinzu.
            </div>
          ) : (
            tariffs.map((tariff) => (
              <div
                key={tariff.id}
                onClick={() => handleTariffClick(tariff)}
                className="cursor-pointer rounded-lg border border-gray-200 p-4 transition hover:bg-gray-50"
              >
                <h2 className="mb-2 font-semibold text-lg">{tariff.Name}</h2>
                <div className="text-gray-600 text-sm">
                  <p>
                    <span className="font-medium">Gültig von:</span>{" "}
                    {new Date(tariff.ValidFrom).toLocaleDateString()}
                  </p>
                  <p>
                    <span className="font-medium">Gültig bis:</span>{" "}
                    {new Date(tariff.ValidTo).toLocaleDateString()}
                  </p>
                  <p className="mt-2">
                    <span className="font-medium">Anzahl Tagesprofile:</span>{" "}
                    {tariff.dailySchedules.length}
                  </p>
                  <p>
                    <span className="font-medium">Anzahl Blockgebühren:</span>{" "}
                    {tariff.blockFeeSchedules.length}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Modal for adding new tariff */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-3xl rounded-lg bg-white p-6 max-h-[90vh] overflow-y-auto">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="font-bold text-xl">Neuen Tarif hinzufügen</h2>
              <button
                onClick={() => {
                  setShowModal(false);
                  setSuccessMessage("");
                  setError("");
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <i className="fas fa-times" />
              </button>
            </div>

            {error && (
              <div className="mb-4 rounded-md bg-red-100 p-4 text-red-700">
                {error}
              </div>
            )}

            {successMessage && (
              <div className="mb-4 rounded-md bg-green-100 p-4 text-green-700">
                {successMessage}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="mb-1 block text-gray-700" htmlFor="Name">
                  Tarifname
                </label>
                <input
                  id="Name"
                  name="Name"
                  type="text"
                  value={newTariff.Name}
                  onChange={handleInputChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="mb-4">
                  <label
                    className="mb-1 block text-gray-700"
                    htmlFor="ValidFrom"
                  >
                    Gültig von
                  </label>
                  <input
                    id="ValidFrom"
                    name="ValidFrom"
                    type="date"
                    value={newTariff.ValidFrom}
                    onChange={handleInputChange}
                    // src/app/(protected)/tarife/page.tsx (continued)
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label className="mb-1 block text-gray-700" htmlFor="ValidTo">
                    Gültig bis
                  </label>
                  <input
                    id="ValidTo"
                    name="ValidTo"
                    type="date"
                    value={newTariff.ValidTo}
                    onChange={handleInputChange}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>

              <div className="mb-4">
                <label
                  className="mb-1 block text-gray-700"
                  htmlFor="chargerType"
                >
                  Ladetyp
                </label>
                <select
                  id="chargerType"
                  name="chargerType"
                  value={newTariff.chargerType}
                  onChange={handleInputChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="AC">AC</option>
                  <option value="DC">DC</option>
                </select>
              </div>

              <div className="mt-4 border-t pt-4">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  className="w-full"
                >
                  <TabsList className="w-full mb-4">
                    <TabsTrigger value="price" className="flex-1">
                      Preise
                    </TabsTrigger>
                    <TabsTrigger value="blockingFee" className="flex-1">
                      Blockiergebühren
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="price">
                    <h3 className="mb-2 font-medium">
                      Wochenkalender für Preise
                    </h3>
                    <p className="mb-4 text-gray-500 text-sm">
                      Wählen Sie Stunden aus und legen Sie Preise fest.{" "}
                      <span className="text-blue-600 font-medium">Tipp:</span>{" "}
                      Halten Sie die Shift-Taste gedrückt und klicken Sie auf
                      zwei Zellen, um einen Bereich auszuwählen.
                    </p>

                    {/* Zwei-Spalten-Layout für Desktop, einspaltig für Mobile */}
                    <div className="flex flex-col md:flex-row md:gap-4">
                      {/* Linke Spalte: Wochenkalender */}
                      <div className="md:w-1/2">
                        <div className="mb-4">
                          <div className="overflow-x-auto">
                            <div className="grid grid-cols-6 gap-0.5">
                              {/* Header row */}
                              <div className="h-6 text-xs font-medium flex items-center justify-center">
                                {/* Empty cell for top-left corner */}
                              </div>

                              {/* Day headers */}
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllHoursForDay(0)}
                                title="Alle Stunden für Montag auswählen/abwählen"
                              >
                                Mo
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllHoursForDay(1)}
                                title="Alle Stunden für Dienstag auswählen/abwählen"
                              >
                                Di
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllHoursForDay(2)}
                                title="Alle Stunden für Mittwoch auswählen/abwählen"
                              >
                                Mi
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllHoursForDay(3)}
                                title="Alle Stunden für Donnerstag auswählen/abwählen"
                              >
                                Do
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllHoursForDay(4)}
                                title="Alle Stunden für Freitag auswählen/abwählen"
                              >
                                Fr
                              </div>

                              {/* Hour rows */}
                              {Array.from({ length: 24 }, (_, hour) => (
                                <React.Fragment key={`hour-${hour}`}>
                                  {/* Hour label */}
                                  <div
                                    className="h-6 text-xs font-medium flex items-center justify-end pr-1 cursor-pointer hover:bg-gray-100 rounded"
                                    onClick={() => toggleAllHoursForTime(hour)}
                                    title={`Alle Tage für ${hour}:00 Uhr auswählen/abwählen`}
                                  >
                                    {hour}:00
                                  </div>

                                  {/* Day cells for this hour */}
                                  {Array.from({ length: 5 }, (_, day) => {
                                    const cellKey = `${day}-${hour}`;
                                    const hasPrice = !!hourlyRates[cellKey];
                                    return (
                                      <div key={cellKey} className="h-6">
                                        <HourCell
                                          hour={hour}
                                          day={day}
                                          isSelected={!!selectedHours[cellKey]}
                                          hasPrice={hasPrice}
                                          priceInfo={hourlyRates[cellKey]}
                                          onToggle={toggleHourSelection}
                                          compact={true}
                                        />
                                      </div>
                                    );
                                  })}
                                </React.Fragment>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Price settings for selected hours */}
                        <div className="mt-2 p-3 border rounded-md bg-gray-50 text-sm">
                          <h4 className="font-medium mb-2 text-sm">
                            Preise für ausgewählte Stunden festlegen
                          </h4>
                          <div className="flex gap-2 mb-2">
                            <div className="flex-1">
                              <label
                                className="mb-1 block text-gray-700 text-xs"
                                htmlFor="hourlyPrice"
                              >
                                Preis pro kWh (€)
                              </label>
                              <input
                                id="hourlyPrice"
                                type="number"
                                step="0.01"
                                min="0"
                                value={hourlyPrice}
                                onChange={(e) => setHourlyPrice(e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            <div className="flex-1">
                              <label
                                className="mb-1 block text-gray-700 text-xs"
                                htmlFor="sessionFee"
                              >
                                Sessiongebühr (€)
                              </label>
                              <input
                                id="sessionFee"
                                type="number"
                                step="0.01"
                                min="0"
                                value={sessionFee}
                                onChange={(e) => setSessionFee(e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            <div className="flex items-end">
                              <button
                                type="button"
                                onClick={applyPriceToSelectedHours}
                                disabled={!hasSelectedHours}
                                className={`rounded-md px-3 py-1 text-white text-sm ${hasSelectedHours ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-400 cursor-not-allowed"}`}
                              >
                                Preis anwenden
                              </button>
                            </div>
                          </div>

                          {/* Aktionsbuttons */}
                          <div className="flex gap-2 mt-3 border-t pt-3">
                            <button
                              type="button"
                              onClick={deleteSelectedPrices}
                              disabled={!hasSelectedHours}
                              className={`flex items-center rounded-md px-3 py-1 text-sm ${hasSelectedHours ? "text-red-600 border border-red-300 hover:bg-red-50" : "text-gray-400 border border-gray-200 cursor-not-allowed"}`}
                            >
                              <i className="fas fa-trash-alt mr-1 text-xs"></i>
                              Ausgewählte Preise löschen
                            </button>
                            <button
                              type="button"
                              onClick={resetAllPrices}
                              className="flex items-center rounded-md px-3 py-1 text-sm text-gray-600 border border-gray-300 hover:bg-gray-50"
                            >
                              <i className="fas fa-undo mr-1 text-xs"></i>
                              Alle Preise zurücksetzen
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Rechte Spalte: Zusammenfassung der konfigurierten Preise */}
                      <div className="md:w-1/2 mt-4 md:mt-0">
                        {Object.keys(hourlyRates).length > 0 ? (
                          <div className="p-3 border rounded-md bg-white h-full">
                            <h4 className="font-medium mb-3 text-sm">
                              Konfigurierte Preise (
                              {Object.keys(hourlyRates).length} Stunde(n))
                            </h4>
                            <div className="text-xs">
                              {/* Gruppiere nach Tagen und Preisen */}
                              {(() => {
                                // Verwende die Gruppierungsfunktion
                                const { dayGroups, dayNames } =
                                  groupPricesByDayAndPrice(hourlyRates);

                                // Für jeden Tag
                                return Object.entries(dayGroups).map(
                                  ([day, priceGroups]) => {
                                    // Anzahl der verschiedenen Preise für diesen Tag
                                    const priceCount =
                                      Object.keys(priceGroups).length;

                                    return (
                                      <div
                                        key={day}
                                        className="mb-4 pb-2 border-b border-gray-100 last:border-0"
                                      >
                                        <div className="flex justify-between items-center mb-1">
                                          <span className="font-medium">
                                            {dayNames[parseInt(day)]}:
                                          </span>
                                          {priceCount > 1 && (
                                            <span className="text-xs bg-yellow-100 text-yellow-800 px-1.5 py-0.5 rounded-full">
                                              {priceCount} verschiedene Preise
                                            </span>
                                          )}
                                        </div>

                                        {/* Für jeden Preis dieses Tages */}
                                        {Object.entries(priceGroups).map(
                                          ([priceKey, { price, hours }]) => {
                                            // Sortiere die Stunden
                                            hours.sort((a, b) => a - b);

                                            return (
                                              <div
                                                key={priceKey}
                                                className={`mt-2 ${priceCount > 1 ? "pl-2 border-l-2 border-gray-200" : ""}`}
                                              >
                                                <div className="flex justify-between">
                                                  <span className="text-gray-600">
                                                    {formatEuro(
                                                      price.pricePerKwh,
                                                    )}
                                                    €/kWh +{" "}
                                                    {formatEuro(
                                                      price.sessionFee,
                                                    )}
                                                    €
                                                  </span>
                                                  <span className="text-gray-500 text-xs">
                                                    {hours.length} Std.
                                                  </span>
                                                </div>
                                                <div
                                                  className="w-full h-2 mt-1 rounded-sm"
                                                  style={{
                                                    backgroundColor:
                                                      getPriceColor(
                                                        price.pricePerKwh,
                                                        price.sessionFee,
                                                      ),
                                                  }}
                                                  title={`${formatEuro(price.pricePerKwh)}€/kWh + ${formatEuro(price.sessionFee)}€`}
                                                ></div>
                                                <div className="text-gray-600 mt-1">
                                                  Stunden:{" "}
                                                  {formatHoursRanges(hours)}
                                                </div>
                                              </div>
                                            );
                                          },
                                        )}
                                      </div>
                                    );
                                  },
                                );
                              })()}
                            </div>
                          </div>
                        ) : (
                          <div className="p-3 border rounded-md bg-white h-full flex items-center justify-center text-gray-500 text-sm">
                            <p>Noch keine Preise konfiguriert</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="blockingFee">
                    <h3 className="mb-2 font-medium">
                      Wochenkalender für Blockiergebühren
                    </h3>
                    <p className="mb-4 text-gray-500 text-sm">
                      Wählen Sie Stunden aus, in denen Blockiergebühren anfallen
                      sollen.{" "}
                      <span className="text-blue-600 font-medium">Tipp:</span>{" "}
                      Halten Sie die Shift-Taste gedrückt und klicken Sie auf
                      zwei Zellen, um einen Bereich auszuwählen.
                    </p>

                    {/* Zwei-Spalten-Layout für Desktop, einspaltig für Mobile */}
                    <div className="flex flex-col md:flex-row md:gap-4">
                      {/* Linke Spalte: Wochenkalender */}
                      <div className="md:w-1/2">
                        <div className="mb-4">
                          <div className="overflow-x-auto">
                            <div className="grid grid-cols-6 gap-0.5">
                              {/* Header row */}
                              <div className="h-6 text-xs font-medium flex items-center justify-center">
                                {/* Empty cell for top-left corner */}
                              </div>

                              {/* Day headers */}
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllBlockingHoursForDay(0)}
                                title="Alle Stunden für Montag auswählen/abwählen"
                              >
                                Mo
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllBlockingHoursForDay(1)}
                                title="Alle Stunden für Dienstag auswählen/abwählen"
                              >
                                Di
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllBlockingHoursForDay(2)}
                                title="Alle Stunden für Mittwoch auswählen/abwählen"
                              >
                                Mi
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllBlockingHoursForDay(3)}
                                title="Alle Stunden für Donnerstag auswählen/abwählen"
                              >
                                Do
                              </div>
                              <div
                                className="h-6 text-xs font-medium flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                                onClick={() => toggleAllBlockingHoursForDay(4)}
                                title="Alle Stunden für Freitag auswählen/abwählen"
                              >
                                Fr
                              </div>

                              {/* Hour rows */}
                              {Array.from({ length: 24 }, (_, hour) => (
                                <React.Fragment key={`blocking-hour-${hour}`}>
                                  {/* Hour label */}
                                  <div
                                    className="h-6 text-xs font-medium flex items-center justify-end pr-1 cursor-pointer hover:bg-gray-100 rounded"
                                    onClick={() =>
                                      toggleAllBlockingHoursForTime(hour)
                                    }
                                    title={`Alle Tage für ${hour}:00 Uhr auswählen/abwählen`}
                                  >
                                    {hour}:00
                                  </div>

                                  {/* Day cells for this hour */}
                                  {Array.from({ length: 5 }, (_, day) => {
                                    const cellKey = `${day}-${hour}`;
                                    const hasFee =
                                      !!blockingFeeSchedules[cellKey];
                                    return (
                                      <div key={cellKey} className="h-6">
                                        <div
                                          className={`border cursor-pointer transition h-6 p-0.5 text-xs ${hasFee ? "bg-orange-200 border-orange-400" : "bg-gray-100 border-gray-200"} hover:opacity-80`}
                                          onClick={(e) =>
                                            toggleBlockingHourSelection(
                                              day,
                                              hour,
                                              e.shiftKey,
                                            )
                                          }
                                        >
                                          <div className="flex items-center justify-center h-full">
                                            {hasFee && (
                                              <span className="text-orange-600 text-xs font-bold">
                                                ✓
                                              </span>
                                            )}
                                            {!hasFee &&
                                              selectedBlockingHours[
                                                cellKey
                                              ] && (
                                                <span className="text-blue-600 text-xs">
                                                  ●
                                                </span>
                                              )}
                                          </div>
                                        </div>
                                      </div>
                                    );
                                  })}
                                </React.Fragment>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Blocking fee settings for selected hours */}
                        <div className="mt-2 p-3 border rounded-md bg-gray-50 text-sm">
                          <h4 className="font-medium mb-2 text-sm">
                            Blockiergebühren für ausgewählte Stunden festlegen
                          </h4>
                          <div className="mb-4">
                            <p className="text-gray-500 text-xs mb-2">
                              Blockiergebühren werden nur während der
                              ausgewählten Stunden berechnet. Wenn Sie z.B.
                              keine Stunden zwischen 18:00 und 8:00 Uhr
                              auswählen, werden in diesem Zeitraum keine
                              Blockiergebühren erhoben.
                            </p>
                          </div>
                          <div className="grid grid-cols-1 gap-2 mb-2">
                            <div>
                              <label
                                className="mb-1 block text-gray-700 text-xs"
                                htmlFor="blockingFeePerMinute"
                              >
                                Gebühr pro Minute (€)
                              </label>
                              <input
                                id="blockingFeePerMinute"
                                type="number"
                                step="0.01"
                                min="0"
                                value={blockingFeePerMinute}
                                onChange={(e) =>
                                  setBlockingFeePerMinute(e.target.value)
                                }
                                className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            <div>
                              <label
                                className="mb-1 block text-gray-700 text-xs"
                                htmlFor="maxBlockingFee"
                              >
                                Maximale Gebühr (€)
                              </label>
                              <input
                                id="maxBlockingFee"
                                type="number"
                                step="0.01"
                                min="0"
                                value={maxBlockingFee}
                                onChange={(e) =>
                                  setMaxBlockingFee(e.target.value)
                                }
                                className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            <div>
                              <label
                                className="mb-1 block text-gray-700 text-xs"
                                htmlFor="gracePeriod"
                              >
                                Karenzzeit (Minuten)
                              </label>
                              <input
                                id="gracePeriod"
                                type="number"
                                step="1"
                                min="0"
                                value={gracePeriod}
                                onChange={(e) => setGracePeriod(e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                              <p className="text-gray-500 text-xs mt-1">
                                Zeitraum in Minuten, bevor Blockiergebühren
                                anfallen.
                              </p>
                            </div>
                          </div>
                          <div className="flex gap-2 mt-4">
                            <button
                              type="button"
                              onClick={applyBlockingFeeToSelectedHours}
                              disabled={
                                !Object.values(selectedBlockingHours).some(
                                  (selected) => selected,
                                )
                              }
                              className={`rounded-md px-3 py-1 text-white text-sm ${Object.values(selectedBlockingHours).some((selected) => selected) ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-400 cursor-not-allowed"}`}
                            >
                              Gebühren anwenden
                            </button>
                            <button
                              type="button"
                              onClick={deleteBlockingFeesForSelectedHours}
                              disabled={
                                !Object.values(selectedBlockingHours).some(
                                  (selected) => selected,
                                )
                              }
                              className={`rounded-md px-3 py-1 text-white text-sm ${Object.values(selectedBlockingHours).some((selected) => selected) ? "bg-red-600 hover:bg-red-700" : "bg-gray-400 cursor-not-allowed"}`}
                            >
                              Löschen
                            </button>
                            <button
                              type="button"
                              onClick={resetAllBlockingFees}
                              disabled={
                                Object.keys(blockingFeeSchedules).length === 0
                              }
                              className={`rounded-md px-3 py-1 text-white text-sm ${Object.keys(blockingFeeSchedules).length > 0 ? "bg-gray-600 hover:bg-gray-700" : "bg-gray-400 cursor-not-allowed"}`}
                            >
                              Alle zurücksetzen
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Rechte Spalte: Zusammenfassung der konfigurierten Blockiergebühren */}
                      <div className="md:w-1/2 mt-4 md:mt-0">
                        {Object.keys(blockingFeeSchedules).length > 0 ? (
                          <div className="p-3 border rounded-md bg-white h-full">
                            <h4 className="font-medium mb-3 text-sm">
                              Konfigurierte Blockiergebühren (
                              {Object.keys(blockingFeeSchedules).length}{" "}
                              Stunde(n))
                            </h4>
                            <div className="text-xs">
                              {(() => {
                                // Verwende die Gruppierungsfunktion für Blockiergebühren
                                const { dayGroups, dayNames } =
                                  groupBlockingFeesByDay(blockingFeeSchedules);

                                // Für jeden Tag
                                return Object.entries(dayGroups).map(
                                  ([day, feeGroups]) => {
                                    // Anzahl der verschiedenen Gebühren für diesen Tag
                                    const feeCount =
                                      Object.keys(feeGroups).length;

                                    if (feeCount === 0) return null;

                                    return (
                                      <div key={day} className="mb-3">
                                        <div className="font-medium text-gray-700">
                                          {dayNames[parseInt(day)]}
                                        </div>
                                        {Object.entries(feeGroups).map(
                                          ([feeKey, { fee, hours }]) => {
                                            return (
                                              <div
                                                key={feeKey}
                                                className={`mt-2 ${feeCount > 1 ? "pl-2 border-l-2 border-gray-200" : ""}`}
                                              >
                                                <div className="flex justify-between">
                                                  <span className="text-gray-600">
                                                    {formatEuro(fee.perMinute)}
                                                    €/min (max.{" "}
                                                    {formatEuro(fee.maxFee)}€)
                                                  </span>
                                                  <span className="text-gray-500 text-xs">
                                                    {hours.length} Std.
                                                  </span>
                                                </div>
                                                <div
                                                  className="w-full h-2 mt-1 rounded-sm bg-orange-200"
                                                  title={`${formatEuro(fee.perMinute)}€/min (max. ${formatEuro(fee.maxFee)}€)`}
                                                ></div>
                                                <div className="text-gray-600 mt-1">
                                                  <div>
                                                    Stunden:{" "}
                                                    {formatHoursRanges(hours)}
                                                  </div>
                                                  <div>
                                                    Karenzzeit:{" "}
                                                    {fee.gracePeriod} Minuten
                                                  </div>
                                                </div>
                                              </div>
                                            );
                                          },
                                        )}
                                      </div>
                                    );
                                  },
                                );
                              })()}
                            </div>
                          </div>
                        ) : (
                          <div className="p-3 border rounded-md bg-white h-full flex items-center justify-center text-gray-500 text-sm">
                            <p>Noch keine Blockiergebühren konfiguriert</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="mt-6 flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false);
                    setSuccessMessage("");
                    setError("");
                  }}
                  className="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
                >
                  Abbrechen
                </button>
                <button
                  type="submit"
                  className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
                >
                  Tarif erstellen
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal for viewing tariff details */}
      {selectedTariff && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="max-h-[90vh] w-full max-w-5xl overflow-y-auto rounded-lg bg-white p-6">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="font-bold text-xl">{selectedTariff.Name}</h2>
              <div className="flex gap-2">
                <button
                  onClick={() => setSelectedTariff(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <i className="fas fa-times" />
                </button>
              </div>
            </div>

            <div className="mb-6 grid grid-cols-3 gap-4">
              <div>
                <p className="text-gray-500 text-sm">Gültig von</p>
                <p>{new Date(selectedTariff.ValidFrom).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-gray-500 text-sm">Gültig bis</p>
                <p>{new Date(selectedTariff.ValidTo).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-gray-500 text-sm">Ladetyp</p>
                <p>{selectedTariff.chargerType || "Nicht definiert"}</p>
              </div>
            </div>

            <div className="mb-8">
              <div className="mb-4">
                <h3 className="font-semibold text-lg">Tagesprofile</h3>
              </div>

              {!selectedTariff.dailySchedules || selectedTariff.dailySchedules.length === 0 ? (
                <p className="py-4 text-center text-gray-500">
                  Keine Tagesprofile vorhanden
                </p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full border border-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border-b px-4 py-2 text-left">Tag</th>
                        <th className="border-b px-4 py-2 text-left">Stunden</th>
                        <th className="border-b px-4 py-2 text-left">Preise</th>
                      </tr>
                    </thead>
                    <tbody>
                      {selectedTariff.dailySchedules.map((schedule) => {
                        const dayMapping: Record<string, string> = {
                          'monday': 'Montag',
                          'tuesday': 'Dienstag',
                          'wednesday': 'Mittwoch',
                          'thursday': 'Donnerstag',
                          'friday': 'Freitag',
                          'saturday': 'Samstag',
                          'sunday': 'Sonntag'
                        };

                        const dayName = dayMapping[schedule.dayOfWeek] || schedule.dayOfWeek;
                        const hourlyRates = schedule.HourlyRate || [];

                        return (
                          <tr key={schedule.id} className="border-b">
                            <td className="px-4 py-2 align-top">
                              {dayName}
                            </td>
                            <td className="px-4 py-2 align-top">
                              {hourlyRates.length > 0 ? (
                                <ul className="list-disc pl-5">
                                  {(() => {
                                    // Sortiere die Stunden nach hourFrom
                                    const sortedRates = [...hourlyRates].sort((a, b) => a.hourFrom - b.hourFrom);

                                    // Gruppiere zusammenhängende Stunden mit gleichen Preisen
                                    const groups: {
                                      startHour: number;
                                      endHour: number;
                                      pricePerKwh: number;
                                      sessionFee: number;
                                    }[] = [];

                                    let currentGroup: {
                                      startHour: number;
                                      endHour: number;
                                      pricePerKwh: number;
                                      sessionFee: number;
                                    } | null = null;

                                    sortedRates.forEach((rate) => {
                                      const endHour = rate.hourTo || (rate.hourFrom + 1);

                                      if (!currentGroup) {
                                        // Erste Stunde
                                        currentGroup = {
                                          startHour: rate.hourFrom,
                                          endHour: endHour,
                                          pricePerKwh: rate.pricePerKwh,
                                          sessionFee: rate.sessionFee
                                        };
                                      } else if (
                                        rate.hourFrom === currentGroup.endHour &&
                                        rate.pricePerKwh === currentGroup.pricePerKwh &&
                                        rate.sessionFee === currentGroup.sessionFee
                                      ) {
                                        // Zusammenhängender Zeitraum mit gleichen Preisen
                                        currentGroup.endHour = endHour;
                                      } else {
                                        // Neuer Zeitraum oder andere Preise
                                        groups.push(currentGroup);
                                        currentGroup = {
                                          startHour: rate.hourFrom,
                                          endHour: endHour,
                                          pricePerKwh: rate.pricePerKwh,
                                          sessionFee: rate.sessionFee
                                        };
                                      }
                                    });

                                    // Letzten Zeitraum hinzufügen
                                    if (currentGroup) {
                                      groups.push(currentGroup);
                                    }

                                    return groups.map((group, index) => (
                                      <li key={index}>
                                        {group.startHour}:00 - {group.endHour}:00
                                      </li>
                                    ));
                                  })()}
                                </ul>
                              ) : (
                                <span className="text-gray-500">Keine Stunden definiert</span>
                              )}
                            </td>
                            <td className="px-4 py-2 align-top">
                              {hourlyRates.length > 0 ? (
                                <ul className="list-disc pl-5">
                                  {(() => {
                                    // Sortiere die Stunden nach hourFrom
                                    const sortedRates = [...hourlyRates].sort((a, b) => a.hourFrom - b.hourFrom);

                                    // Gruppiere zusammenhängende Stunden mit gleichen Preisen
                                    const groups: {
                                      startHour: number;
                                      endHour: number;
                                      pricePerKwh: number;
                                      sessionFee: number;
                                    }[] = [];

                                    let currentGroup: {
                                      startHour: number;
                                      endHour: number;
                                      pricePerKwh: number;
                                      sessionFee: number;
                                    } | null = null;

                                    sortedRates.forEach((rate) => {
                                      const endHour = rate.hourTo || (rate.hourFrom + 1);

                                      if (!currentGroup) {
                                        // Erste Stunde
                                        currentGroup = {
                                          startHour: rate.hourFrom,
                                          endHour: endHour,
                                          pricePerKwh: rate.pricePerKwh,
                                          sessionFee: rate.sessionFee
                                        };
                                      } else if (
                                        rate.hourFrom === currentGroup.endHour &&
                                        rate.pricePerKwh === currentGroup.pricePerKwh &&
                                        rate.sessionFee === currentGroup.sessionFee
                                      ) {
                                        // Zusammenhängender Zeitraum mit gleichen Preisen
                                        currentGroup.endHour = endHour;
                                      } else {
                                        // Neuer Zeitraum oder andere Preise
                                        groups.push(currentGroup);
                                        currentGroup = {
                                          startHour: rate.hourFrom,
                                          endHour: endHour,
                                          pricePerKwh: rate.pricePerKwh,
                                          sessionFee: rate.sessionFee
                                        };
                                      }
                                    });

                                    // Letzten Zeitraum hinzufügen
                                    if (currentGroup) {
                                      groups.push(currentGroup);
                                    }

                                    return groups.map((group, index) => (
                                      <li key={index}>
                                        {group.pricePerKwh.toFixed(2)}€/kWh + {group.sessionFee.toFixed(2)}€ Sessiongebühr
                                        <div
                                          className="w-full h-2 mt-1 rounded-sm"
                                          style={{
                                            backgroundColor: getPriceColor(group.pricePerKwh, group.sessionFee)
                                          }}
                                        ></div>
                                      </li>
                                    ));
                                  })()}
                                </ul>
                              ) : (
                                <span className="text-gray-500">Keine Preise definiert</span>
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            <div className="mb-8">
              <div className="mb-4">
                <h3 className="font-semibold text-lg">Blockgebühren</h3>
              </div>

              {!selectedTariff.blockFeeSchedules || selectedTariff.blockFeeSchedules.length === 0 ? (
                <p className="py-4 text-center text-gray-500">
                  Keine Blockgebühren vorhanden
                </p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full border border-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border-b px-4 py-2 text-left">Tag</th>
                        <th className="border-b px-4 py-2 text-left">Zeitraum</th>
                        <th className="border-b px-4 py-2 text-left">Gebühr/Min</th>
                        <th className="border-b px-4 py-2 text-left">Max Gebühr</th>
                        <th className="border-b px-4 py-2 text-left">Karenzzeit</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(() => {
                        // Gruppiere Blockiergebühren nach Tag und Gebühren
                        const dayMapping: Record<string, string> = {
                          'monday': 'Montag',
                          'tuesday': 'Dienstag',
                          'wednesday': 'Mittwoch',
                          'thursday': 'Donnerstag',
                          'friday': 'Freitag',
                          'saturday': 'Samstag',
                          'sunday': 'Sonntag'
                        };

                        // Gruppiere nach Tagen
                        const schedulesByDay: Record<string, typeof selectedTariff.blockFeeSchedules> = {};

                        selectedTariff.blockFeeSchedules.forEach(schedule => {
                          const day = schedule.dayOfWeek;
                          if (!schedulesByDay[day]) {
                            schedulesByDay[day] = [];
                          }
                          schedulesByDay[day].push(schedule);
                        });

                        // Für jeden Tag, gruppiere zusammenhängende Zeiträume mit gleichen Gebühren
                        const rows: JSX.Element[] = [];

                        Object.entries(schedulesByDay).forEach(([day, schedules]) => {
                          const dayName = dayMapping[day] || day;

                          // Sortiere nach Startzeit
                          const sortedSchedules = [...schedules].sort((a, b) => a.startHour - b.startHour);

                          // Gruppiere zusammenhängende Zeiträume mit gleichen Gebühren
                          const groups: {
                            startHour: number;
                            endHour: number;
                            perMinute: number;
                            maxFee: number;
                            gracePeriod: number;
                          }[] = [];

                          let currentGroup: {
                            startHour: number;
                            endHour: number;
                            perMinute: number;
                            maxFee: number;
                            gracePeriod: number;
                          } | null = null;

                          sortedSchedules.forEach(schedule => {
                            const perMinute = schedule.perMinute || schedule.blockFeePerMinute || 0;
                            const maxFee = schedule.maxFee || schedule.maxBlockFee || 0;
                            const gracePeriod = schedule.gracePeriod || 0;

                            if (!currentGroup) {
                              // Erste Stunde
                              currentGroup = {
                                startHour: schedule.startHour,
                                endHour: schedule.endHour,
                                perMinute,
                                maxFee,
                                gracePeriod
                              };
                            } else if (
                              schedule.startHour === currentGroup.endHour + 1 &&
                              perMinute === currentGroup.perMinute &&
                              maxFee === currentGroup.maxFee &&
                              gracePeriod === currentGroup.gracePeriod
                            ) {
                              // Zusammenhängender Zeitraum mit gleichen Gebühren
                              currentGroup.endHour = schedule.endHour;
                            } else {
                              // Neuer Zeitraum oder andere Gebühren
                              groups.push(currentGroup);
                              currentGroup = {
                                startHour: schedule.startHour,
                                endHour: schedule.endHour,
                                perMinute,
                                maxFee,
                                gracePeriod
                              };
                            }
                          });

                          // Letzten Zeitraum hinzufügen
                          if (currentGroup) {
                            groups.push(currentGroup);
                          }

                          // Zeilen für jeden gruppierten Zeitraum erstellen
                          groups.forEach((group, index) => {
                            rows.push(
                              <tr key={`${day}-${index}`} className="border-b">
                                <td className="px-4 py-2">
                                  {dayName}
                                </td>
                                <td className="px-4 py-2">
                                  {group.startHour}:00 - {group.endHour}:00
                                </td>
                                <td className="px-4 py-2">
                                  {group.perMinute.toFixed(2)} €
                                </td>
                                <td className="px-4 py-2">
                                  {group.maxFee.toFixed(2)} €
                                </td>
                                <td className="px-4 py-2">
                                  {group.gracePeriod} Minuten
                                </td>
                              </tr>
                            );
                          });
                        });

                        return rows;
                      })()}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Verknüpfungen */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {/* Mandanten - nur für Administratoren sichtbar */}
              {user?.role?.name === "Administrator" && (
                <div className="border rounded-lg p-4">
                  <h3 className="font-semibold text-lg mb-3">Verknüpfte Mandanten</h3>
                  {!selectedTariff.mandants || selectedTariff.mandants.length === 0 ? (
                    <p className="text-gray-500 text-center py-2">Keine Mandanten verknüpft</p>
                  ) : (
                    <ul className="list-disc pl-5">
                      {selectedTariff.mandants.map((mandant) => (
                        <li key={mandant.id} className="mb-1">
                          {mandant.name || 'Unbenannter Mandant'}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}

              {/* Terminals */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3">Verknüpfte Terminals</h3>
                {!selectedTariff.terminals || selectedTariff.terminals.length === 0 ? (
                  <p className="text-gray-500 text-center py-2">Keine Terminals verknüpft</p>
                ) : (
                  <ul className="list-disc pl-5">
                    {selectedTariff.terminals.map((terminal) => (
                      <li key={terminal.id} className="mb-1">
                        {terminal.Name || terminal.serialNumber || 'Unbenanntes Terminal'}
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              {/* EVSEs */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3">Verknüpfte EVSEs</h3>
                {!selectedTariff.ocpi_evses || selectedTariff.ocpi_evses.length === 0 ? (
                  <p className="text-gray-500 text-center py-2">Keine EVSEs verknüpft</p>
                ) : (
                  <ul className="list-disc pl-5">
                    {selectedTariff.ocpi_evses.map((evse) => (
                      <li key={evse.id} className="mb-1">
                        {evse.evse_id || evse.uid || 'Unbenanntes EVSE'}
                        {evse.status && <span className="ml-2 text-xs bg-gray-100 px-2 py-0.5 rounded-full">{evse.status}</span>}
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              {/* Locations */}
              <div className="border rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3">Verknüpfte Standorte</h3>
                {!selectedTariff.ocpi_locations || selectedTariff.ocpi_locations.length === 0 ? (
                  <p className="text-gray-500 text-center py-2">Keine Standorte verknüpft</p>
                ) : (
                  <ul className="list-disc pl-5">
                    {selectedTariff.ocpi_locations.map((location) => (
                      <li key={location.id} className="mb-1">
                        {location.name || `${location.country_code || ''}${location.party_id ? `/${location.party_id}` : ''}` || 'Unbenannter Standort'}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>

            <div className="mt-8 flex justify-end gap-2">
              <button
                type="button"
                onClick={() => setSelectedTariff(null)}
                className="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
              >
                Schließen
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
