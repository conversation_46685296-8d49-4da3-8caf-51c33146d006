/**
 * terminal-message-log-actions controller
 */

module.exports = {
  /**
   * Loggt eine Nachricht vom Server zum Terminal
   */
  async logToTerminal(ctx) {
    try {
      const { terminalId, payload, paymentSessionId, mandantId } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID ist erforderlich');
      }

      const logger = strapi.service('api::terminal-message-log.terminal-message-log');
      const result = await logger.logToTerminal(terminalId, payload, {
        paymentSessionId,
        mandantId
      });

      return result;
    } catch (error) {
      console.error('Fehler beim Loggen der Server-zu-Terminal-Nachricht:', error);
      return ctx.badRequest('Fehler beim Loggen der Nachricht');
    }
  },

  /**
   * Loggt eine Nachricht vom Terminal zum Server
   */
  async logFromTerminal(ctx) {
    try {
      const { terminalId, payload, paymentSessionId, mandantId } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID ist erforderlich');
      }

      const logger = strapi.service('api::terminal-message-log.terminal-message-log');
      const result = await logger.logFromTerminal(terminalId, payload, {
        paymentSessionId,
        mandantId
      });

      return result;
    } catch (error) {
      console.error('Fehler beim Loggen der Terminal-zu-Server-Nachricht:', error);
      return ctx.badRequest('Fehler beim Loggen der Nachricht');
    }
  },

  /**
   * Loggt einen Fehler in der Terminal-Kommunikation
   */
  async logError(ctx) {
    try {
      const { terminalId, payload, direction, paymentSessionId, mandantId } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID ist erforderlich');
      }

      const logger = strapi.service('api::terminal-message-log.terminal-message-log');
      const result = await logger.logError(terminalId, payload, {
        direction,
        paymentSessionId,
        mandantId
      });

      return result;
    } catch (error) {
      console.error('Fehler beim Loggen des Terminal-Fehlers:', error);
      return ctx.badRequest('Fehler beim Loggen des Fehlers');
    }
  },

  /**
   * Loggt eine Debug-Nachricht in der Terminal-Kommunikation
   */
  async logDebug(ctx) {
    try {
      const { terminalId, payload, direction, paymentSessionId, mandantId } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID ist erforderlich');
      }

      const logger = strapi.service('api::terminal-message-log.terminal-message-log');
      const result = await logger.logDebug(terminalId, payload, {
        direction,
        paymentSessionId,
        mandantId
      });

      return result;
    } catch (error) {
      console.error('Fehler beim Loggen der Debug-Nachricht:', error);
      return ctx.badRequest('Fehler beim Loggen der Debug-Nachricht');
    }
  },

  /**
   * Ruft alle eindeutigen Terminal-IDs ab
   */
  async getTerminalIds(ctx) {
    try {
      // Mandantenfilter aus dem Benutzerkontext oder den Query-Parametern
      let mandantFilter = '';
      let params = [];

    // Wenn ein mandantId-Parameter übergeben wurde, diesen verwenden
      if (ctx.query.mandantId) {
        mandantFilter = ' WHERE "mandant" = ?';
        params.push(ctx.query.mandantId);
      }
    // Andernfalls, falls der authentifizierte Benutzer einen Mandanten hat, diesen verwenden
      else if (ctx.state?.user?.mandant) {
        // In Strapi 5 sollten wir documentId anstelle von id verwenden
        const mandantId = ctx.state.user.mandant.documentId || ctx.state.user.mandant.documentId;
        mandantFilter = ' WHERE `mandant` = ?';
        params.push(mandantId);
      }

    // SQL-Abfrage für eindeutige Terminal-IDs mit dem korrekten Spaltennamen
    const query = `SELECT DISTINCT \`terminal_id\` FROM \`terminal_message_logs\` ${mandantFilter} ORDER BY \`terminal_id\``;
      console.log("Terminal-IDs Query:", query, params);

      // Query ausführen
      const result = await strapi.db.connection.raw(query, params);

      // Extrahiere die IDs aus dem Ergebnis je nach Datenbanktyp
      let terminalIds = [];

      if (result && result.rows) {
      // Für PostgreSQL
      terminalIds = result.rows.map(row => row.terminal_id);
      } else if (Array.isArray(result) && result[0]) {
      // Für MySQL
      terminalIds = result.map(row => row.terminal_id);
      }

      console.log(`Found ${terminalIds.length} unique terminal IDs`);
      return terminalIds;
    } catch (error) {
      console.error('Fehler beim Abrufen der Terminal-IDs:', error);
      return [];
    }
  }


};
