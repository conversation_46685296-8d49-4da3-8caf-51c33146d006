// src/app/(protected)/users/page.tsx
"use client";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useAuth } from "~/components/AuthContext";

interface User {
	id: string;
	username: string;
	email: string;
	role: {
		name: string;
	};
	createdAt: string;
}

export default function UsersPage() {
	const { user, token, loading } = useAuth();
	const router = useRouter();
	const [users, setUsers] = useState<User[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState("");

	// Form state for new user
	const [newUser, setNewUser] = useState({
		username: "",
		email: "",
		role: "user",
	});
	const [showModal, setShowModal] = useState(false);
	const [formError, setFormError] = useState("");
	const [successMessage, setSuccessMessage] = useState("");

	// Redirect if not admin
	useEffect(() => {
		if (!loading && user && user.role?.name !== "Administrator") {
			router.push("/dashboard");
		}
	}, [user, loading, router]);

	// Fetch users
	useEffect(() => {
		const fetchUsers = async () => {
			if (loading || !user || !token) return;

			try {
				const response = await fetch(
					`${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/users?populate=role`,
					{
						headers: {
							Authorization: `Bearer ${token}`,
						},
					},
				);

				if (!response.ok) {
					throw new Error("Failed to fetch users");
				}

				const data = await response.json();
				setUsers(data);
			} catch (err) {
				setError("Error loading users");
				console.error(err);
			} finally {
				setIsLoading(false);
			}
		};

		fetchUsers();
	}, [user, token, loading]);

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
	) => {
		const { name, value } = e.target;
		setNewUser((prev) => ({ ...prev, [name]: value }));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setFormError("");

		try {
			const response = await fetch(
				`${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/users`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						Authorization: `Bearer ${token}`,
					},
					body: JSON.stringify({
						...newUser,
						password: Math.random().toString(36).slice(-8), // Generate random password
						sendEmailConfirmation: true,
					}),
				},
			);

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.message || "Failed to create user");
			}

			// Reset form and close modal
			setNewUser({ username: "", email: "", role: "user" });
			setShowModal(false);
			setSuccessMessage(
				"User created successfully! An email with login information has been sent.",
			);

			// Reload users
			const updatedUsersRes = await fetch(
				`${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/users?populate=role`,
				{
					headers: {
						Authorization: `Bearer ${token}`,
					},
				},
			);

			if (updatedUsersRes.ok) {
				const updatedUsers = await updatedUsersRes.json();
				setUsers(updatedUsers);
			}

			// Clear success message after 5 seconds
			setTimeout(() => {
				setSuccessMessage("");
			}, 5000);
		} catch (err) {
			if (err instanceof Error) {
				setFormError(err.message || "Error creating user");
			}
		}
	};

	if (loading || (user && user?.role?.name !== "Administrator")) {
		return (
			<div className="flex h-full items-center justify-center">
				<p>Loading...</p>
			</div>
		);
	}

	return (
		<div className="rounded-lg bg-white p-6 shadow-sm">
			<div className="mb-6 flex items-center justify-between">
				<h1 className="font-bold text-2xl">Nutzerverwaltung</h1>
				<button
					onClick={() => setShowModal(true)}
					className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
				>
					<i className="fas fa-plus mr-2" />
					Nutzer hinzufügen
				</button>
			</div>

			{successMessage && (
				<div className="mb-4 rounded-md bg-green-100 p-4 text-green-700">
					{successMessage}
				</div>
			)}

			{error && (
				<div className="mb-4 rounded-md bg-red-100 p-4 text-red-700">
					{error}
				</div>
			)}

			{isLoading ? (
				<div className="py-4 text-center">Loading users...</div>
			) : (
				<div className="overflow-x-auto">
					<table className="min-w-full bg-white">
						<thead>
							<tr className="bg-gray-100 text-gray-700 text-sm uppercase leading-normal">
								<th className="px-6 py-3 text-left">ID</th>
								<th className="px-6 py-3 text-left">Username</th>
								<th className="px-6 py-3 text-left">Email</th>
								<th className="px-6 py-3 text-left">Role</th>
								<th className="px-6 py-3 text-left">Created At</th>
								<th className="px-6 py-3 text-center">Actions</th>
							</tr>
						</thead>
						<tbody className="text-gray-600 text-sm">
							{users.map((user) => (
								<tr
									key={user.id}
									className="border-gray-200 border-b hover:bg-gray-50"
								>
									<td className="px-6 py-3 text-left">{user.id}</td>
									<td className="px-6 py-3 text-left">{user.username}</td>
									<td className="px-6 py-3 text-left">{user.email}</td>
									<td className="px-6 py-3 text-left">{user.role.name}</td>
									<td className="px-6 py-3 text-left">
										{new Date(user.createdAt).toLocaleDateString()}
									</td>
									<td className="px-6 py-3 text-center">
										<button className="mr-3 text-blue-600 hover:text-blue-900">
											<i className="fas fa-edit" />
										</button>
										<button className="text-red-600 hover:text-red-900">
											<i className="fas fa-trash" />
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			)}

			{/* Modal for adding new user */}
			{showModal && (
				<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
					<div className="w-full max-w-md rounded-lg bg-white p-6">
						<div className="mb-4 flex items-center justify-between">
							<h2 className="font-bold text-xl">Neuen Nutzer hinzufügen</h2>
							<button
								onClick={() => setShowModal(false)}
								className="text-gray-500 hover:text-gray-700"
							>
								<i className="fas fa-times" />
							</button>
						</div>

						{formError && (
							<div className="mb-4 rounded-md bg-red-100 p-3 text-red-700">
								{formError}
							</div>
						)}

						<form onSubmit={handleSubmit}>
							<div className="mb-4">
								<label className="mb-1 block text-gray-700" htmlFor="username">
									Username
								</label>
								<input
									id="username"
									name="username"
									type="text"
									value={newUser.username}
									onChange={handleInputChange}
									className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
									required
								/>
							</div>

							<div className="mb-4">
								<label className="mb-1 block text-gray-700" htmlFor="email">
									Email
								</label>
								<input
									id="email"
									name="email"
									type="email"
									value={newUser.email}
									onChange={handleInputChange}
									className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
									required
								/>
							</div>

							<div className="mb-4">
								<label className="mb-1 block text-gray-700" htmlFor="role">
									Role
								</label>
								<select
									id="role"
									name="role"
									value={newUser.role}
									onChange={handleInputChange}
									className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
								>
									<option value="user">User</option>
									<option value="admin">Admin</option>
								</select>
							</div>

							<div className="mt-6 flex justify-end gap-2">
								<button
									type="button"
									onClick={() => setShowModal(false)}
									className="rounded-md border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50"
								>
									Cancel
								</button>
								<button
									type="submit"
									className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
								>
									Add User
								</button>
							</div>
						</form>
					</div>
				</div>
			)}
		</div>
	);
}
