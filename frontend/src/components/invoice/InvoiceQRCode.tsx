"use client";

import { QRCodeSVG } from "qrcode.react";
import React from "react";
import { Card, CardContent } from "~/components/ui/card";

interface InvoiceQRCodeProps {
	sessionId: string;
	size?: number;
}

export function InvoiceQRCode({ sessionId, size = 256 }: InvoiceQRCodeProps) {
	// Basis-URL aus der Umgebungsvariable oder Standard-URL
	const baseUrl =
		process.env.NEXT_PUBLIC_FRONTEND_URL || window.location.origin;

	// QR-Code-URL erstellen
	const qrCodeUrl = `${baseUrl}/invoice/${sessionId}`;

	return (
		<div className="flex flex-col items-center justify-center">
			<Card className="mx-auto w-full max-w-sm">
				<CardContent className="flex flex-col items-center justify-center p-6">
					<div className="rounded-lg border-4 border-white bg-white p-2 shadow-md">
						<QRCodeSVG
							value={qrCodeUrl}
							size={size}
							level="H" // Höchste Fehlerkorrektur
							includeMargin={true}
						/>
					</div>
					<p className="mt-4 break-all text-center text-gray-500 text-sm">
						{qrCodeUrl}
					</p>
				</CardContent>
			</Card>
		</div>
	);
}
