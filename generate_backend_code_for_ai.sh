#!/bin/bash

# Array mit den Projekten
projects=("backend" "frontend")

for project in "${projects[@]}"; do
    # Ausgabeverzeichnis für das jeweilige Projekt erstellen
    output_dir="ai_context_${project}"
    mkdir -p "$output_dir"

    # 1. Projektstruktur speichern
    find "$project" -type d -not -path "*/node_modules/*" -not -path "*/dist/*" -not -path "*/.git/*" | sort > "$output_dir/1_projektstruktur.txt"

    # 2. Wichtige Konfigurationsdateien separat speichern
    find "$project" -name "*.json" -not -path "*/node_modules/*" -not -path "*/dist/*" | xargs cat > "$output_dir/2_konfiguration.txt"

    # Für das Frontend (Next.js) zusätzlich weitere Konfigurationsdateien erfassen
    if [ "$project" == "frontend" ]; then
      if [ -f "$project/next.config.js" ]; then
          echo "### DATEI: $project/next.config.js ###" >> "$output_dir/2_konfiguration.txt"
          cat "$project/next.config.js" >> "$output_dir/2_konfiguration.txt"
          echo -e "\n" >> "$output_dir/2_konfiguration.txt"
      fi
      for env_file in "$project"/.env*; do
          if [ -f "$env_file" ]; then
              echo "### DATEI: $env_file ###" >> "$output_dir/2_konfiguration.txt"
              cat "$env_file" >> "$output_dir/2_konfiguration.txt"
              echo -e "\n" >> "$output_dir/2_konfiguration.txt"
          fi
      done
    fi

    # 3. Code nach Kategorien organisieren
    if [ "$project" == "backend" ]; then
      # Routen-Dateien (z. B. "*route*.js" oder "*router*.js")
      find "$project" -name "*route*.js" -o -name "*router*.js" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
          echo -e "\n### DATEI: $file ###\n"
          # Entferne leere Zeilen und reine Kommentarzeilen
          cat "$file" | grep -v "^\s*$" | grep -v "^\s*//"
          echo -e "\n"
      done > "$output_dir/3_routes.txt"

      # Controller
      find "$project" -name "*controller*.js" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
          echo -e "\n### DATEI: $file ###\n"
          cat "$file"
          echo -e "\n"
      done > "$output_dir/4_controllers.txt"

      # Modelle (Modelle/Schemas)
      find "$project" -name "*model*.js" -o -name "*schema*.js" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
          echo -e "\n### DATEI: $file ###\n"
          cat "$file"
          echo -e "\n"
      done > "$output_dir/5_models.txt"

      # Services/Utilities
      find "$project" -name "*service*.js" -o -name "*util*.js" -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
          echo -e "\n### DATEI: $file ###\n"
          cat "$file"
          echo -e "\n"
      done > "$output_dir/6_services.txt"
    fi

    if [ "$project" == "frontend" ]; then
      # Seiten (Next.js Pages)
      find "$project/pages" -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
          echo -e "\n### DATEI: $file (Seite) ###\n"
          cat "$file" | grep -v "^\s*$" | grep -v "^\s*//"
          echo -e "\n"
      done > "$output_dir/3_pages.txt"

      # Komponenten
      if [ -d "$project/components" ]; then
        find "$project/components" -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
            echo -e "\n### DATEI: $file (Komponente) ###\n"
            cat "$file" | grep -v "^\s*$" | grep -v "^\s*//"
            echo -e "\n"
        done > "$output_dir/4_components.txt"
      fi

      # API-Routen (in Next.js unter pages/api)
      if [ -d "$project/pages/api" ]; then
        find "$project/pages/api" -type f \( -name "*.js" -o -name "*.ts" \) -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
            echo -e "\n### DATEI: $file (API) ###\n"
            cat "$file" | grep -v "^\s*$" | grep -v "^\s*//"
            echo -e "\n"
        done > "$output_dir/5_api.txt"
      fi
    fi

    # 4. Sonstiger Code (Dateien kleiner als 30k)
    find "$project" -type f -size -30k -not -path "*/node_modules/*" -not -path "*/dist/*" -not -path "*/.tmp/*" \
      -not -path "*/.strapi/*" -not -path "*/\.*" \
      -not -name "*.png" -not -name "*.jpg" -not -name "*.jpeg" -not -name "*.gif" \
      -not -name "*.svg" -not -name "*.ico" -not -name "*.webp" -not -name "*.bmp" -not -name "*.tiff" \
      -not -name "*route*.js" -not -name "*router*.js" \
      -not -name "*controller*.js" -not -name "*model*.js" -not -name "*schema*.js" \
      -not -name "*service*.js" -not -name "*util*.js" \
      -not -name "*.json" | sort | while read file; do
        echo -e "\n### DATEI: $file ###\n"
        cat "$file" | grep -v "^\s*$" | sed '/^\s*\/\//d; /^\s*\/\*.*\*\//d'
        echo -e "\n"
    done > "$output_dir/7_sonstiger_code.txt"

    # 5. Große Dateien (30k bis 200k) mit wichtigen Teilen (Imports, Exports, Funktionen)
    find "$project" -type f -size +30k -size -200k -not -path "*/node_modules/*" -not -path "*/dist/*" | sort | while read file; do
      echo -e "\n### DATEI: $file (GEKÜRZT) ###\n"
      echo -e "// Imports:\n"
      grep -E "^import|^const.*require" "$file"
      echo -e "\n// Exports und Funktionen:\n"
      grep -E "^export|^module.exports|function\s+\w+\s*\(|const\s+\w+\s*=\s*(\([^)]*\)|\s*async\s*\([^)]*\))|class\s+\w+" "$file"
      echo -e "\n"
    done > "$output_dir/8_grosse_dateien.txt"

    # 6. Zusammenfassung erstellen
    echo "# Projektübersicht für $project" > "$output_dir/0_zusammenfassung.txt"
    if [ "$project" == "backend" ]; then
      echo "## Dateien nach Kategorie:" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/3_routes.txt") Routen-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/4_controllers.txt") Controller-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/5_models.txt") Modell-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/6_services.txt") Service/Utility-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/7_sonstiger_code.txt") Sonstige Code-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/8_grosse_dateien.txt") Große Dateien (gekürzt)" >> "$output_dir/0_zusammenfassung.txt"
    elif [ "$project" == "frontend" ]; then
      echo "## Dateien nach Kategorie:" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/3_pages.txt") Seiten-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      if [ -f "$output_dir/4_components.txt" ]; then
        echo "- $(grep -c "### DATEI:" "$output_dir/4_components.txt") Komponenten-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      fi
      if [ -f "$output_dir/5_api.txt" ]; then
        echo "- $(grep -c "### DATEI:" "$output_dir/5_api.txt") API-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      fi
      echo "- $(grep -c "### DATEI:" "$output_dir/7_sonstiger_code.txt") Sonstige Code-Dateien" >> "$output_dir/0_zusammenfassung.txt"
      echo "- $(grep -c "### DATEI:" "$output_dir/8_grosse_dateien.txt") Große Dateien (gekürzt)" >> "$output_dir/0_zusammenfassung.txt"
    fi

    # 7. Alles in eine Datei pro Projekt zusammenführen
    if [ "$project" == "backend" ]; then
      cat "$output_dir/0_zusammenfassung.txt" "$output_dir/1_projektstruktur.txt" "$output_dir/2_konfiguration.txt" \
      "$output_dir/3_routes.txt" "$output_dir/4_controllers.txt" "$output_dir/5_models.txt" "$output_dir/6_services.txt" \
      "$output_dir/7_sonstiger_code.txt" "$output_dir/8_grosse_dateien.txt" > "${project}_code_for_ai.txt"
    elif [ "$project" == "frontend" ]; then
      cat "$output_dir/0_zusammenfassung.txt" "$output_dir/1_projektstruktur.txt" "$output_dir/2_konfiguration.txt" \
      "$output_dir/3_pages.txt" $( [ -f "$output_dir/4_components.txt" ] && echo "$output_dir/4_components.txt" ) $( [ -f "$output_dir/5_api.txt" ] && echo "$output_dir/5_api.txt" ) \
      "$output_dir/7_sonstiger_code.txt" "$output_dir/8_grosse_dateien.txt" > "${project}_code_for_ai.txt"
    fi

    echo "Fertig! Code für $project wurde in ${project}_code_for_ai.txt zusammengefasst und nach Kategorien in $output_dir/ aufgeteilt."
done
