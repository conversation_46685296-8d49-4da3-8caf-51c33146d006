'use strict';

import {displayScreen} from "../../payter/controllers/terminal/uiActions";
import {CommandResultType} from "../types/command-types";

/**
 * Bricht eine laufende Payter-Sitzung ab
 * @param {string} sessionId - Die ID der abzubrechenden Sitzung
 * @param {Object} logger - Der Logger-Service
 * @returns {Object} Das Ergebnis des Abbruchs
 */
export async function cancelPayterSession(paymentIntent, logger) {
    try {
        if(!logger)
        {
            logger = strapi.service('api::ocpi-log.ocpi-logger');
        }
        await logger.info(`Abbruch der Payter-Sitzung ${paymentIntent} angefordert`, {
            endpoint: 'payter/cancel',
            action: 'REQUEST',
            paymentIntent
        });

        // Payment-Session finden
        const paymentSession = await strapi.documents('api::payment-session.payment-session').findFirst({
            filters: {
                paymentIntent
            },
            populate: {
                mandant: true,
                terminal: {
                    populate: {
                        mandant: true,
                        payter_connection: true,
                    }
                }
            }
        });

        if (!paymentSession) {
            await logger.error(`Payment-Session mit SessionID ${paymentIntent} nicht gefunden`, {
                endpoint: 'payter/cancel',
                action: 'ERROR',
                paymentIntent
            });

            return {
                success: false,
                status: 404,
                message: `Payment session with session ID ${paymentIntent} not found`
            };
        }

        // Prüfen, ob die Session bereits abgeschlossen oder abgebrochen wurde
        if (['commited', 'cancelled', 'failed'].includes(paymentSession.paymentSessionState)) {
            await logger.warn(`Payment-Session ${paymentIntent} ist bereits im Status ${paymentSession.paymentSessionState} und kann nicht abgebrochen werden`, {
                endpoint: 'payter/cancel',
                action: 'ERROR',
                paymentIntent,
                state: paymentSession.paymentSessionState
            });

            return {
                success: false,
                status: 409,
                message: `Payment session with ID ${paymentIntent} is already in state ${paymentSession.paymentSessionState} and cannot be canceled`
            };
        }
        const ctx = {
            state: {
                terminal: paymentSession.terminal
            }
        }

        // Payter API-Client abrufen
        const { apiClient } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

        // Anfrage an die Payter API senden, um die Sitzung abzubrechen
        try {
            const response = await apiClient.post(`/terminals/${paymentSession.terminal.serialNumber}/sessions/${paymentIntent}/cancel`);

            if (response.data.state != "CANCELLED") {
                return {
                    success: false,
                    status: 500,
                    message: "Session could not be canceled"
                };
            }
            // Erfolgreiche Antwort verarbeiten
            await logger.info(`Payter-Sitzung ${paymentIntent} erfolgreich abgebrochen`, {
                endpoint: 'payter/cancel',
                action: 'SUCCESS',
                paymentIntent,
                response: response.data
            });

            // Payment-Session aktualisieren
            await strapi.documents('api::payment-session.payment-session').update({
                documentId: paymentSession.documentId,
                data: {
                    lastUpdated: new Date(),
                    closedAt: new Date(),
                    state: response.data.state
                }
            });

            // Erfolgreiche Antwort zurückgeben
            return {
                success: true,
                status: 200,
                message: "Session successfully canceled",
                data: response.data
            };
        } catch (apiError) {
            // Fehler bei der Payter API-Anfrage
            let errorStatus = 500;
            let errorMessage = 'Internal server error';
            let payterResponse = null;

            // Fehlerantwort von Payter verarbeiten
            if (apiError.response) {
                errorStatus = apiError.response.status;
                errorMessage = apiError.response.data?.message || 'Unknown error';
                payterResponse = apiError.response.data;

                // Spezifische Fehlerbehandlung basierend auf dem Statuscode
                switch (errorStatus) {
                    case 404:
                        errorMessage = 'Terminal not found or session not found';
                        break;
                    case 409:
                        errorMessage = 'Session already committed or cancelled';
                        const updatedHistory = await strapi.service('api::payment-session.payment-session').formatHistory(
                            paymentSession.documentId,
                            {
                                action: 'cancel_error',
                                timestamp: new Date().toISOString(),
                                error: apiError.response.data
                            }
                        );

                        // Session als abgebrochen markieren, da sie bereits abgebrochen wurde
                        await strapi.documents('api::payment-session.payment-session').update({
                            documentId: paymentSession.documentId,
                            data: {
                                paymentSessionState: 'canceled',
                                closedAt: new Date(),
                                history: updatedHistory

                            }
                        });
                        break;
                }
            }

            await logger.error(`Fehler beim Abbrechen der Payter-Sitzung ${paymentIntent}: ${errorMessage}`, {
                endpoint: 'payter/cancel',
                action: 'ERROR',
                paymentIntent,
                error: {
                    status: errorStatus,
                    message: errorMessage,
                    response: payterResponse
                }
            });

            // Fehlerantwort zurückgeben
            return {
                success: false,
                status: errorStatus,
                message: `Error canceling session: ${errorMessage}`,
                data: payterResponse
            };
        }
    } catch (error) {
        // Allgemeine Fehlerbehandlung
        strapi.log.error('Fehler beim Abbrechen der Payter-Sitzung:', error);

        return {
            success: false,
            status: 500,
            message: `Server error: ${error.message}`
        };
    }
}

/**
 * OCPI Command Response Controller
 * Verarbeitet Antworten auf OCPI-Befehle
 */



interface OcpiResponse {
    status_code: number;
    status_message: string;
    timestamp: string;
}

/**
 * A set of functions called "actions" for `ocpi-response`
 */
module.exports = {
    /**
     * Verarbeitet die Antwort auf einen StartSession-Befehl
     * @param {Object} ctx - Der Kontext des Requests
     * @returns {Object} Die OCPI-Antwort
     */
    async startSession(ctx) {
        try {
            const authorizationReference = ctx.params.authorizationReference;
            const command_result = ctx.request.body.result;

            // Logger-Service referenzieren
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            await logger.info(`StartSession-Antwort erhalten für ${authorizationReference}`, {
                endpoint: 'command/START_SESSION',
                action: 'RESPONSE',
                authorizationReference,
                command_result
            });

            // Payment-Session finden
            const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
                documentId: authorizationReference,
                populate: {
                    mandant: true,
                    terminal: {
                        populate: {
                            mandant: true,
                            payter_connection: true,
                        }
                    }
                }
            });

            if (!paymentSession) {
                await logger.error(`Payment-Session mit ID ${authorizationReference} nicht gefunden`, {
                    endpoint: 'command/START_SESSION',
                    action: 'RESPONSE',
                    authorizationReference,
                    command_result
                });

                return ctx.send({
                    status_code: 2003,
                    status_message: `Payment session with ID ${authorizationReference} not found`,
                    timestamp: new Date().toISOString()
                }, 404);
            }

            // Status der Payment-Session aktualisieren
            let commandStatus = 'pending';

            switch (command_result) {
                case CommandResultType.ACCEPTED:
                    commandStatus = 'accepted';
                    // Weitere Aktionen für akzeptierte Sessions
                    await logger.info(`StartSession-Befehl akzeptiert für ${authorizationReference}`, {
                        endpoint: 'command/START_SESSION',
                        action: 'ACCEPTED',
                        authorizationReference
                    });
                    break;
                case CommandResultType.TIMEOUT:
                    commandStatus = 'timeout';
                    // Weitere Aktionen für Timeout
                    await logger.warn(`StartSession-Befehl Timeout für ${authorizationReference}`, {
                        endpoint: 'command/START_SESSION',
                        action: 'TIMEOUT',
                        authorizationReference
                    });
                    break;
                case CommandResultType.CANCELED_RESERVATION:
                case CommandResultType.EVSE_OCCUPIED:
                case CommandResultType.EVSE_INOPERATIVE:
                case CommandResultType.FAILED:
                case CommandResultType.NOT_SUPPORTED:
                case CommandResultType.REJECTED:
                case CommandResultType.UNKNOWN_RESERVATION:
                default:
                    commandStatus = 'failed';
                    // Payment-Session abbrechen
                    await logger.error(`StartSession-Befehl fehlgeschlagen für ${authorizationReference}: ${command_result}`, {
                        endpoint: 'command/START_SESSION',
                        action: 'FAILED',
                        authorizationReference,
                        command_result
                    });

                    // Payter-Sitzung abbrechen, falls vorhanden
                    if (paymentSession.paymentIntent) {
                        await logger.info(`Versuche, Payter-Sitzung ${paymentSession.paymentIntent} abzubrechen`, {
                            endpoint: 'command/START_SESSION',
                            action: 'CANCEL_PAYTER_SESSION',
                            authorizationReference,
                            sessionId: paymentSession.paymentIntent
                        });

                        const cancelResult = await cancelPayterSession(paymentSession.paymentIntent, logger);

                        if (cancelResult.success) {
                            await logger.info(`Payter-Sitzung ${paymentSession.paymentIntent} erfolgreich abgebrochen`, {
                                endpoint: 'command/START_SESSION',
                                action: 'CANCEL_SUCCESS',
                                authorizationReference,
                                sessionId: paymentSession.paymentIntent,
                                result: cancelResult
                            });
                        } else {
                            await logger.warn(`Payter-Sitzung ${paymentSession.paymentIntent} konnte nicht abgebrochen werden: ${cancelResult.message}`, {
                                endpoint: 'command/START_SESSION',
                                action: 'CANCEL_FAILED',
                                authorizationReference,
                                sessionId: paymentSession.paymentIntent,
                                result: cancelResult
                            });
                        }
                    }

                    // Fehlermeldung auf dem Terminal anzeigen
                    try {
                        ctx.state.terminal = paymentSession.terminal;
                        const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);
                        await displayScreen(ctx, apiClient, callback.ui, 'screen-start-command-response-error');
                    } catch (displayError) {
                        await logger.error(`Fehler beim Anzeigen der Fehlermeldung auf dem Terminal: ${displayError.message}`, {
                            endpoint: 'command/START_SESSION',
                            action: 'DISPLAY_ERROR',
                            authorizationReference,
                            error: {
                                message: displayError.message,
                                stack: displayError.stack
                            }
                        });
                    }
                    break;
            }

            const updatedHistory = await strapi.service('api::payment-session.payment-session').formatHistory(
                paymentSession.documentId,
                {
                    action: 'command_response',
                    result: command_result,
                    timestamp: new Date().toISOString()
                }
            );

            // Payment-Session aktualisieren
            const sessionState = commandStatus === 'accepted' ? 'authorized' : 'canceled';
            await strapi.documents('api::payment-session.payment-session').update({
                documentId: paymentSession.documentId,
                data: {
                    paymentSessionState: sessionState,
                    commandResult: command_result,
                    history: updatedHistory,
                    closedAt: sessionState === 'canceled' ? new Date() : null
                }
            });

            await logger.info(`Payment-Session ${authorizationReference} aktualisiert: ${sessionState}`, {
                endpoint: 'command/START_SESSION',
                action: 'UPDATE_SESSION',
                authorizationReference,
                commandStatus,
                sessionState
            });

            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            // Fehlerbehandlung
            strapi.log.error('Fehler bei der Verarbeitung der StartSession-Antwort:', error);

            return ctx.send({
                status_code: 3000,
                status_message: `Server error: ${error.message}`,
                timestamp: new Date().toISOString()
            }, 500);
        }
    },

    /**
     * Verarbeitet die Antwort auf einen StopSession-Befehl
     * @param {Object} ctx - Der Kontext des Requests
     * @returns {Object} Die OCPI-Antwort
     */
    async stopSession(ctx) {
        try {
            const sessionId = ctx.params.sessionId;
            const command_result = ctx.request.body.result;

            // Logger-Service referenzieren
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            await logger.info(`StopSession-Antwort erhalten für ${sessionId}`, {
                endpoint: 'command/STOP_SESSION',
                action: 'RESPONSE',
                sessionId,
                command_result
            });

            // Payment-Session finden
            // const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
            //     filters: {
            //         sessionId: sessionId
            //     },
            //     populate: {
            //         mandant: true,
            //         terminal: {
            //             populate: {
            //                 payter_connection: true,
            //             }
            //         }
            //     }
            // });

            // if (!paymentSession) {
            //     await logger.error(`Payment-Session mit SessionID ${sessionId} nicht gefunden`, {
            //         endpoint: 'command/STOP_SESSION',
            //         action: 'RESPONSE',
            //         sessionId,
            //         command_result
            //     });
            //
            //     return ctx.send({
            //         status_code: 2003,
            //         status_message: `Payment session with session ID ${sessionId} not found`,
            //         timestamp: new Date().toISOString()
            //     }, 404);
            // }

            // Status der Payment-Session aktualisieren
            let sessionStatus = 'stopping';

            switch (command_result) {
                case CommandResultType.ACCEPTED:
                    sessionStatus = 'stopped';
                    // Weitere Aktionen für akzeptierte Sessions
                    await logger.info(`StopSession-Befehl akzeptiert für ${sessionId}`, {
                        endpoint: 'command/STOP_SESSION',
                        action: 'ACCEPTED',
                        sessionId
                    });
                    break;
                case CommandResultType.TIMEOUT:
                    sessionStatus = 'timeout';
                    // Weitere Aktionen für Timeout
                    await logger.warn(`StopSession-Befehl Timeout für ${sessionId}`, {
                        endpoint: 'command/STOP_SESSION',
                        action: 'TIMEOUT',
                        sessionId
                    });
                    break;
                default:
                    sessionStatus = 'error';
                    // Fehlerbehandlung
                    await logger.error(`StopSession-Befehl fehlgeschlagen für ${sessionId}: ${command_result}`, {
                        endpoint: 'command/STOP_SESSION',
                        action: 'FAILED',
                        sessionId,
                        command_result
                    });
                    break;
            }

            // Payment-Session aktualisieren
            // await strapi.documents('api::payment-session.payment-session').update({
            //     documentId: paymentSession.documentId,
            //     data: {
            //         status: sessionStatus,
            //         commandResult: command_result,
            //         lastUpdated: new Date(),
            //         endTime: sessionStatus === 'stopped' ? new Date() : null
            //     }
            // });

            await logger.info(`Payment-Session ${sessionId} aktualisiert: ${sessionStatus}`, {
                endpoint: 'command/STOP_SESSION',
                action: 'UPDATE_SESSION',
                sessionId,
                sessionStatus
            });

            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            // Fehlerbehandlung
            strapi.log.error('Fehler bei der Verarbeitung der StopSession-Antwort:', error);

            return ctx.send({
                status_code: 3000,
                status_message: `Server error: ${error.message}`,
                timestamp: new Date().toISOString()
            }, 500);
        }
    },

};
