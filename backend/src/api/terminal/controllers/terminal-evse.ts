/**
 * Terminal-EVSE Controller für die Verwaltung der Beziehung zwischen Terminals und EVSEs
 */


// Interface für ein EVSE
interface EVSE {
  id: number;
  documentId: string;
  EvseId: string;
  PhysicalReference?: string;
  Charger?: string;
  PowerType?: string;
  EvseStatus?: string;
  EvseUid?: string;
}

// Interface für ein Terminal mit EVSEs
interface TerminalWithEvses {
  id: number;
  documentId: string;
  serialNumber: string;
  terminalName?: string;
  online?: boolean;
  state?: string;
  lastUpdate?: string;
  evses?: EVSE[];
}

export default {
  /**
   * Ermittelt die Anzahl der EVSEs für ein Terminal
   * @param {Object} ctx - Der Kontext der Anfrage
   * @returns {Object} - Die Anzahl der EVSEs
   */
  async getEvseCount(ctx) {
    try {
      const { documentId } = ctx.params;

      if (!documentId) {
        return ctx.badRequest('Terminal Document ID ist erforderlich');
      }

      // Terminal mit EVSEs abrufen
      const terminal = await strapi.documents('api::terminal.terminal').findOne({
        documentId,
        populate: { evses: true }
      });

      // Typ-Assertion mit dem definierten Interface
      const terminalWithEvses = terminal as unknown as TerminalWithEvses;

      if (!terminal) {
        return ctx.notFound('Terminal nicht gefunden');
      }

      // Anzahl der EVSEs ermitteln
      const evseCount = terminalWithEvses.evses?.length || 0;

      return ctx.send({
        status_code: 1000,
        status_message: "Success",
        data: {
          terminalId: terminal.serialNumber,
          terminalName: terminal.terminalName,
          evseCount
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Fehler beim Ermitteln der EVSE-Anzahl:', error);
      return ctx.internalServerError('Ein Fehler ist aufgetreten');
    }
  },

  /**
   * Überträgt die Anzahl der EVSEs an das Terminal
   * @param {Object} ctx - Der Kontext der Anfrage
   * @returns {Object} - Das Ergebnis der Übertragung
   */
  async sendEvseCountToTerminal(ctx) {
    try {
      const { documentId } = ctx.params;
      const { environment = 'test' } = ctx.request.body || {};

      if (!documentId) {
        return ctx.badRequest('Terminal Document ID ist erforderlich');
      }

      // Terminal mit EVSEs abrufen
      const terminal = await strapi.documents('api::terminal.terminal').findOne({
        documentId,
        populate: { evses: true }
      });

      // Typ-Assertion mit dem definierten Interface
      const terminalWithEvses = terminal as unknown as TerminalWithEvses;

      if (!terminal) {
        return ctx.notFound('Terminal nicht gefunden');
      }

      // Anzahl der EVSEs ermitteln
      const evseCount = terminalWithEvses.evses?.length || 0;

      ctx.state.terminal = terminal;

      // Payter API Client initialisieren
      const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

      if (!apiClient || !callback.ui) {
        return ctx.internalServerError('Payter API Client konnte nicht initialisiert werden');
      }

      // serialNumber des Terminals abrufen
      const terminalId = terminal.serialNumber;

      // Ladepunkte-Screen mit dynamisch erzeugten Ladepunkten an das Terminal senden
      const screenData = {
        type: "selection",
        id: "screen-list-charging-points",
        properties: {
          title: "Ladepunkte",
          message: `Bitte wählen Sie einen Ladepunkt:`,
          type: "3"
        }
      };

      // Dynamisch die Ladepunkte aus den EVSEs des Terminals erzeugen
      const evses = (terminal as any).evses || [];
      if (evses.length > 0) {
        // Sortiere die EVSEs nach PhysicalReference oder EvseId, um eine konsistente Reihenfolge zu gewährleisten
        const sortedEvses = [...evses].sort((a: any, b: any) => {
          const aRef = a.PhysicalReference || a.EvseId || '';
          const bRef = b.PhysicalReference || b.EvseId || '';
          return aRef.localeCompare(bRef);
        });

        // Füge jeden EVSE als Ladepunkt hinzu
        sortedEvses.forEach((evse: any, index: number) => {
          const itemKey = `items.cp${index + 1}`;
          const labelKey = `${itemKey}.label`;

          // Verwende EvseId oder PhysicalReference oder einen Standardwert
          const displayName = evse.PhysicalReference || evse.EvseId || `Ladepunkt *${(index + 1).toString().padStart(2, '0')}`;

          // Füge den Ladepunkt zum Screen hinzu
          screenData.properties[itemKey] = "";
          screenData.properties[labelKey] = displayName;
        });
      } else {
        // Fallback, wenn keine EVSEs vorhanden sind
        screenData.properties["items.cp1"] = "";
        screenData.properties["items.cp1.label"] = "Ladepunkt *01";
      }

      // Sende die Anfrage an die Payter API
      const url = `/terminals/${terminalId}/ui`;
      const params = new URLSearchParams({ callbackUrl: callback.ui });
      const response = await apiClient.post(`${url}?${params.toString()}`, screenData);

      return ctx.send({
        status_code: 1000,
        status_message: `Ladepunkte-Anzahl (${evseCount}) erfolgreich an Terminal ${terminalId} übertragen`,
        data: {
          terminalId,
          terminalName: terminal.terminalName,
          evseCount,
          payter_response: response.data
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Fehler beim Übertragen der EVSE-Anzahl an das Terminal:', error);
      return ctx.internalServerError('Ein Fehler ist aufgetreten');
    }
  }
};
