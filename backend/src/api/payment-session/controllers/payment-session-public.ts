'use strict';

/**
 * Öffentlicher Controller für Payment-Sessions
 * Bietet Endpunkte für den Zugriff auf Rechnungsdaten ohne Authentifizierung
 */

import {calculatePriceByTimeAndKwh} from "../../invoice/services/payment-capture";

/**
 * Maskiert eine E-Mail-Adresse, aber behält den ersten und letzten Buchstaben vor dem @,
 * sowie den ersten und letzten Buchstaben der Domain und die TLD bei.
 * Beispiel: <EMAIL> -> m********n@e*****e.com
 * @param {string} email - Die zu maskierende E-Mail-Adresse
 * @returns {string} Die maskierte E-Mail-Adresse
 */
const maskEmail = (email) => {
  if (!email || typeof email !== 'string' || !email.includes('@')) {
    return email;
  }

  const [localPart, domainPart] = email.split('@');

  // Lokalen Teil maskieren (ersten und letzten Buchstaben beibehalten)
  let maskedLocalPart;
  if (localPart.length <= 2) {
    maskedLocalPart = localPart; // Zu kurz zum Maskieren
  } else {
    maskedLocalPart = localPart[0] + '*'.repeat(localPart.length - 2) + localPart[localPart.length - 1];
  }

  // Domain-Teil maskieren (ersten und letzten Buchstaben der Domain beibehalten, TLD vollständig anzeigen)
  const domainParts = domainPart.split('.');
  const tld = domainParts.pop(); // TLD extrahieren (z.B. 'com', 'de', etc.)
  const domain = domainParts.join('.'); // Rest der Domain

  let maskedDomain;
  if (domain.length <= 2) {
    maskedDomain = domain; // Zu kurz zum Maskieren
  } else {
    maskedDomain = domain[0] + '*'.repeat(domain.length - 2) + domain[domain.length - 1];
  }

  return `${maskedLocalPart}@${maskedDomain}.${tld}`;
};

module.exports = {
    /**
     * Gibt öffentliche Informationen zu einer Payment-Session zurück
     * @param {Object} ctx - Der Kontext des Requests
     * @returns {Object} Die öffentlichen Informationen zur Payment-Session
     */
    async findPublic(ctx) {
        try {
            const {sessionId} = ctx.params;

            // Suche nach der Payment-Session anhand der sessionId oder documentId
            const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
                documentId: sessionId,
                fields: ['showPriceDateTime', 'maskedPan', 'costumerEmailForInvoice', 'paymentSessionState'],
                populate: {
                    terminal: {
                        fields: ['serialNumber', 'terminalName']
                    },
                    mandant: {
                        fields: ['name'],
                        populate: {
                            logo: {
                                fields: ['url', 'width', 'height']
                            }
                        },
                    },
                    ocpi_evse: {
                        populate: {
                            location: {
                                fields: ['name', 'address', 'city', 'postalCode', 'country'],
                                populate: {
                                    coordinates: true,
                                }
                            },
                        },
                    },
                    ocpi_session: {
                        fields: ['startTime', 'endTime', 'kwh', 'ocpiStatus']
                    },
                    ocpi_cdr: {
                        fields: ['createdAt']
                    },
                    invoice: {
                        fields: ['invoice_number', 'invoice_status']
                    }
                }

            });

            if (!paymentSession) {
                return ctx.notFound('Payment-Session nicht gefunden');
            }

            // Hole die Preisinformationen, falls verfügbar
            let tariff = null;
            if (paymentSession && paymentSession.ocpi_session && paymentSession.ocpi_evse.documentId) {
                try {
                    tariff = await strapi.service('api::tariff.tariff').getTariff(
                        paymentSession.ocpi_evse.documentId,
                        new Date(paymentSession.showPriceDateTime)
                    );
                } catch (priceError) {
                    strapi.log.error('Fehler beim Abrufen der Preisinformationen:', priceError);
                }
            }

            // E-Mail-Adresse maskieren, falls vorhanden
            if (paymentSession.costumerEmailForInvoice) {
                paymentSession.costumerEmailForInvoice = maskEmail(paymentSession.costumerEmailForInvoice);
            }

            const totalTimeInSeconds = (new Date(paymentSession.ocpi_session.lastUpdated).getTime() - new Date(paymentSession.ocpi_session.startTime).getTime()) / 1000;

            const currentPrice = calculatePriceByTimeAndKwh(tariff, {totalEnergy: paymentSession.ocpi_session.kwh, totalTime: totalTimeInSeconds});

            return {paymentSession: paymentSession, tariff: tariff, currentPrice: currentPrice};
        } catch
            (error) {
            strapi.log.error('Fehler beim Abrufen der öffentlichen Payment-Session-Daten:', error);
            return ctx.badRequest('Fehler beim Abrufen der Daten');
        }
    }
    ,

    /**
     * Speichert die E-Mail-Adresse für eine Payment-Session
     * @param {Object} ctx - Der Kontext des Requests
     * @returns {Object} Die aktualisierte Payment-Session
     */
    async saveEmail(ctx) {
        try {
            const {sessionId} = ctx.params;
            const {email} = ctx.request.body;

            if (!email || typeof email !== 'string' || !email.includes('@')) {
                return ctx.badRequest('Ungültige E-Mail-Adresse');
            }

            // Suche nach der Payment-Session anhand der sessionId oder documentId
            const paymentSession = await strapi.documents('api::payment-session.payment-session').findFirst({
                filters: {
                    documentId: sessionId
                }
            });

            if (!paymentSession) {
                return ctx.notFound('Payment-Session nicht gefunden');
            }

            // E-Mail-Adresse speichern
            const updatedSession = await strapi.documents('api::payment-session.payment-session').update({
                documentId: sessionId,
                data: {
                    costumerEmailForInvoice: email
                }
            });

            // Maskiere die E-Mail-Adresse in der Antwort
            const responseSession = { ...updatedSession };
            if (responseSession.costumerEmailForInvoice) {
                responseSession.costumerEmailForInvoice = maskEmail(responseSession.costumerEmailForInvoice);
            }

            return {success: true, paymentSession: responseSession};
        } catch (error) {
            strapi.log.error('Fehler beim Speichern der E-Mail-Adresse:', error);
            return ctx.badRequest('Fehler beim Speichern der E-Mail-Adresse');
        }
    }
    ,

    /**
     * Prüft, ob eine Rechnung für eine Payment-Session verfügbar ist
     * @param {Object} ctx - Der Kontext des Requests
     * @returns {Object} Die Payment-Session mit Rechnungsinformationen
     */
    async checkInvoice(ctx) {
        try {
            const {sessionId} = ctx.params;

            // Suche nach der Payment-Session anhand der sessionId oder documentId
            const paymentSession = await strapi.documents('api::payment-session.payment-session').findFirst({
                filters: {
                    documentId: sessionId
                },
                fields: ['paymentSessionState'],
                populate: {
                    invoice: {
                        fields: ['invoice_number', 'invoice_status']
                    },
                    ocpi_session: {
                        fields: ['ocpiStatus']
                    }
                }
            });

            if (!paymentSession) {
                return ctx.notFound('Payment-Session nicht gefunden');
            }

            // Prüfen, ob eine Rechnung verfügbar ist
            const invoiceAvailable = !!paymentSession.invoice &&
                paymentSession.invoice.invoice_status !== 'DRAFT';

            return {
                success: true,
                paymentSession: paymentSession,
                invoiceAvailable: invoiceAvailable
            };
        } catch (error) {
            strapi.log.error('Fehler beim Prüfen der Rechnungsverfügbarkeit:', error);
            return ctx.badRequest('Fehler beim Prüfen der Rechnungsverfügbarkeit');
        }
    }
};
