POST http://localhost:1337/api/ocpi/2.2.1/cdrs
Content-Type: application/json
Authorization: Token 10f414dbc0ebc0316be09bcfc3b79529

{
  "country_code": "DE",
  "party_id": "EUL",
  "id": "DEEUL45D2F08BBF6FC8D46440767453ED2CE",
  "start_date_time": "2025-04-17T12:57:43.022Z",
  "end_date_time": "2025-04-17T16:56:43.022Z",
  "session_id": "DEEUL45D2F08BBF6FC8D46440767453ED2CE",
  "cdr_token": {
    "country_code": "DE",
    "party_id": "EUL",
    "uid": "PAYTERF9CB857AEF72AE",
    "type": "AD_HOC_USER",
    "contract_id": "a44i0v7rcnamiq204c7s2hrl"
  },
  "auth_method": "COMMAND",
  "authorization_reference": "a44i0v7rcnamiq204c7s2hrl",
  "cdr_location": {
    "id": "DEEUlSE0779",
    "name": "Westerstraße",
    "address": "Westerstraße 77",
    "city": "Bremen",
    "postal_code": "28199",
    "country": "DEU",
    "coordinates": {
      "latitude": "0.000000",
      "longitude": "0.000000"
    },
    "evse_uid": "6fff58bb-721a-47a7-9801-02345b268f74",
    "evse_id": "DE*EUL*ESIMULATOR007*1",
    "connector_id": "1",
    "connector_standard": "CHADEMO",
    "connector_format": "SOCKET",
    "connector_power_type": "AC_1_PHASE"
  },
  "currency": "EUR",
  "tariffs": [
    {
      "country_code": "DE",
      "party_id": "EUL",
      "id": "DEEULTDEFAULT",
      "currency": "EUR",
      "type": "REGULAR",
      "tariff_alt_text": [
        {
          "language": "en",
          "text": "Default tenant tariff"
        },
        {
          "language": "nl",
          "text": "Default tenant tariff"
        }
      ],
      "elements": [
        {
          "price_components": [
            {
              "type": "FLAT",
              "price": 1,
              "step_size": 1
            },
            {
              "type": "ENERGY",
              "price": 0.35,
              "step_size": 1
            }
          ]
        }
      ],
      "last_updated": "2022-12-14T12:45:36.999Z"
    }
  ],
  "charging_periods": [
    {
      "start_date_time": "2025-04-17T12:57:43.022Z",
      "dimensions": [
        {
          "type": "ENERGY",
          "volume": 0.3
        }
      ],
      "tariff_id": "DEEULTDEFAULT"
    },
    {
      "start_date_time": "2025-04-17T13:02:43.022Z",
      "dimensions": [
        {
          "type": "ENERGY",
          "volume": 0
        }
      ],
      "tariff_id": "DEEULTDEFAULT"
    }
  ],
  "total_cost": {
    "excl_vat": 1.105
  },
  "total_fixed_cost": {
    "excl_vat": 1
  },
  "total_energy": 0.3,
  "total_energy_cost": {
    "excl_vat": 0.105
  },
  "total_time": 3.9833,
  "total_time_cost": {
    "excl_vat": 0
  },
  "total_parking_time": 0,
  "total_parking_cost": {
    "excl_vat": 0
  },
  "credit": false,
  "last_updated": "2025-04-17T17:00:14.660Z",
  "home_charging_compensation": false
}
