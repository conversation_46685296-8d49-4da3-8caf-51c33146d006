/**
 * Bootstrap file for Strapi
 * This file is executed during the bootstrap phase of Strapi
 * It performs initialization tasks
 */

import * as fs from 'fs';
import * as path from 'path';

export default async () => {
  // Ensure the upload directory exists
  const uploadDir = './public/uploads';
  const absoluteUploadDir = path.resolve(uploadDir);

  try {
    // Check if the directory exists, if not create it
    if (!fs.existsSync(absoluteUploadDir)) {
      fs.mkdirSync(absoluteUploadDir, { recursive: true });
      console.log(`Created upload directory: ${absoluteUploadDir}`);
    }

    // Check write permissions
    try {
      const testFile = path.join(absoluteUploadDir, '.write-test');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      console.log(`Upload directory ${absoluteUploadDir} is writable`);
    } catch (error) {
      console.error(`Upload directory ${absoluteUploadDir} is not writable:`, error);
    }

    // Check if the fonts directory exists
    const fontsDir = './public/font/OpenSans';
    const absoluteFontsDir = path.resolve(fontsDir);
    if (!fs.existsSync(absoluteFontsDir)) {
      console.error(`Fonts directory does not exist: ${absoluteFontsDir}`);
      // Create the directory structure
      fs.mkdirSync(absoluteFontsDir, { recursive: true });
      console.log(`Created fonts directory: ${absoluteFontsDir}`);
    } else {
      console.log(`Fonts directory exists: ${absoluteFontsDir}`);
      // Check if the font files exist
      const regularFont = path.join(absoluteFontsDir, 'OpenSans-Regular.ttf');
      const boldFont = path.join(absoluteFontsDir, 'OpenSans-Bold.ttf');

      if (!fs.existsSync(regularFont)) {
        console.error(`Regular font file does not exist: ${regularFont}`);
      } else {
        console.log(`Regular font file exists: ${regularFont}`);
      }

      if (!fs.existsSync(boldFont)) {
        console.error(`Bold font file does not exist: ${boldFont}`);
      } else {
        console.log(`Bold font file exists: ${boldFont}`);
      }
    }

    // Check if the logo directory exists
    const logoDir = './public/logo';
    const absoluteLogoDir = path.resolve(logoDir);
    if (!fs.existsSync(absoluteLogoDir)) {
      console.error(`Logo directory does not exist: ${absoluteLogoDir}`);
      // Create the directory
      fs.mkdirSync(absoluteLogoDir, { recursive: true });
      console.log(`Created logo directory: ${absoluteLogoDir}`);
    } else {
      console.log(`Logo directory exists: ${absoluteLogoDir}`);
      // Check if the logo file exists
      const logoFile = path.join(absoluteLogoDir, 'EULEKTRO_21697c_R33_G105_B124.png');
      if (!fs.existsSync(logoFile)) {
        console.error(`Logo file does not exist: ${logoFile}`);
      } else {
        console.log(`Logo file exists: ${logoFile}`);
      }
    }

    // Check if the invoice folder exists
    const invoiceFolder = strapi.config.get('server.invoiceFolder', '/tmp/invoices');
    console.log(`Invoice folder configured as: ${invoiceFolder}`);

    if (!fs.existsSync(invoiceFolder)) {
      console.error(`Invoice folder does not exist: ${invoiceFolder}`);
      try {
        // Create the directory
        fs.mkdirSync(invoiceFolder, { recursive: true });
        console.log(`Created invoice folder: ${invoiceFolder}`);
      } catch (error) {
        console.error(`Failed to create invoice folder: ${error.message}`);
      }
    } else {
      console.log(`Invoice folder exists: ${invoiceFolder}`);
      // Check write permissions
      try {
        const testFile = path.join(invoiceFolder, '.write-test');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        console.log(`Invoice folder ${invoiceFolder} is writable`);
      } catch (error) {
        console.error(`Invoice folder ${invoiceFolder} is not writable: ${error.message}`);
      }
    }
  } catch (error) {
    console.error(`Error setting up directories:`, error);
  }

  console.log('Bootstrap completed successfully');
};
