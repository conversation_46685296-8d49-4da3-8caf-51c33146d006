{"kind": "collectionType", "collectionName": "terminal_message_logs", "info": {"singularName": "terminal-message-log", "pluralName": "terminal-message-logs", "displayName": "TerminalMessageLog", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"direction": {"type": "enumeration", "enum": ["ServerToTerminal", "TerminalToServer", "WebToServer", "ServerToWeb", "Unknown"]}, "terminalId": {"type": "string"}, "payload": {"type": "json"}, "paymentSessionId": {"type": "string"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "terminal_message_logs"}, "messageType": {"type": "enumeration", "enum": ["info", "warning", "error", "debug"]}, "responsePayload": {"type": "json"}}}