{"kind": "collectionType", "collectionName": "ocpi_evses", "info": {"singularName": "ocpi-evse", "pluralName": "ocpi-evses", "displayName": "OCPI EVSE", "description": "OCPI 2.2.1 EVSE object"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"evseId": {"type": "string", "comment": "Uniquely identifies the EVSE within the CPOs platform (and suboperator platforms). For example: DE*ABC*E45B*78C."}, "uid": {"type": "string", "required": true, "comment": "Unique identifier of the EVSE within the CPOs platform (and suboperator platforms)."}, "labelForTerminal": {"type": "string", "required": true, "maxLength": 12, "comment": "Label for terminal Select List"}, "ocpiStatus": {"type": "enumeration", "enum": ["AVAILABLE", "BLOCKED", "CHARGING", "INOPERATIVE", "OUTOFORDER", "PLANNED", "REMOVED", "RESERVED", "UNKNOWN"], "required": false, "comment": "Indicates the current status of the EVSE.", "default": "UNKNOWN"}, "statusSchedule": {"type": "component", "repeatable": true, "component": "ocpi.status-schedule", "comment": "Indicates a planned status update of the EVSE."}, "capabilities": {"type": "json", "comment": "List of functionalities that the EVSE is capable of."}, "connectors": {"type": "json", "comment": "List of connectors attached to the EVSE."}, "floorLevel": {"type": "string", "comment": "Level on which the EVSE is located (in garage buildings) in the locally displayed numbering scheme."}, "coordinates": {"type": "component", "repeatable": false, "component": "ocpi.geo-location", "comment": "Coordinates of the EVSE."}, "physicalReference": {"type": "string", "comment": "A number/string printed on the EVSE for visual identification."}, "directions": {"type": "component", "repeatable": true, "component": "ocpi.display-text", "comment": "Multi-language human-readable directions when more detailed instructions are required to reach the EVSE."}, "parkingRestrictions": {"type": "json", "comment": "The restrictions that apply to the parking spot."}, "images": {"type": "component", "repeatable": true, "component": "ocpi.image", "comment": "Links to images related to the EVSE such as photos or logos."}, "lastUpdated": {"type": "datetime", "required": true, "comment": "Timestamp when this EVSE or one of its Connectors was last updated."}, "location": {"type": "relation", "relation": "manyToOne", "target": "api::ocpi-location.ocpi-location", "inversedBy": "evses"}, "terminals": {"type": "relation", "relation": "manyToMany", "target": "api::terminal.terminal", "mappedBy": "evses"}, "payment_sessions": {"type": "relation", "relation": "oneToMany", "target": "api::payment-session.payment-session", "mappedBy": "ocpi_evse"}}}