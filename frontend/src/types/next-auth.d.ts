// frontend/src/types/next-auth.d.ts
import NextAuth from "next-auth";
import type { JWT } from "next-auth/jwt";

declare module "next-auth" {
	interface Session {
		user: {
			id: string;
			name?: string | null;
			email?: string | null;
			image?: string | null;
			role?: string; // Hier wird das role-Attribut hinzugefügt
		};
		jwt?: JWT; // Optional, falls JWT verwendet wird
	}

	interface User {
		role?: string; // Erweitert den User-Typ um das role-Attribut
	}
}

declare module "next-auth/jwt" {
	interface JWT {
		role?: string; // Damit das role-Attribut auch im JWT verfügbar ist
	}
}
