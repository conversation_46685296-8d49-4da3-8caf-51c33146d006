"use client";

import { useEffect, useState } from "react";

interface TerminalModalProps {
	isOpen: boolean;
	onClose: () => void;
	onSave: (data: TerminalFormData) => void;
	initialData?: TerminalFormData;
	isEditing: boolean;
}

export interface TerminalFormData {
	id?: string;
	model: string;
	serialNumber: string;
	location?: string;
}

export default function TerminalModal({
	isOpen,
	onClose,
	onSave,
	initialData,
	isEditing,
}: TerminalModalProps) {
	const [formData, setFormData] = useState<TerminalFormData>({
		model: "",
		serialNumber: "",
		location: "",
	});

	// Beim <PERSON>nen des Modals die Daten initialisieren
	useEffect(() => {
		if (initialData && isOpen) {
			setFormData({
				id: initialData.id,
				model: initialData.model,
				serialNumber: initialData.serialNumber,
				location: initialData.location,
			});
		} else if (isOpen) {
			// Zurücksetzen des Formulars beim <PERSON> für einen neuen Eintrag
			setFormData({
				model: "",
				serialNumber: "",
				location: "",
			});
		}
	}, [initialData, isOpen]);

	const handleChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
	) => {
		const { name, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSave(formData);
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 z-50 overflow-y-auto">
			<div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
				<div className="fixed inset-0 transition-opacity" aria-hidden="true">
					<div className="absolute inset-0 bg-gray-500 opacity-75" />
				</div>

				<span
					className="hidden sm:inline-block sm:h-screen sm:align-middle"
					aria-hidden="true"
				>
					&#8203;
				</span>

				<div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
					<div className="sm:flex sm:items-start">
						<div className="mt-3 w-full text-center sm:mt-0 sm:text-left">
							<h3 className="font-medium text-gray-900 text-lg leading-6">
								{isEditing
									? "Terminal bearbeiten"
									: "Neues Terminal hinzufügen"}
							</h3>
							<div className="mt-2">
								<form onSubmit={handleSubmit} className="space-y-4">
									<div>
										<label
											htmlFor="model"
											className="block font-medium text-gray-700 text-sm"
										>
											Modell / Name
										</label>
										<input
											type="text"
											name="model"
											id="model"
											required
											value={formData.model}
											onChange={handleChange}
											className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
										/>
									</div>

									<div>
										<label
											htmlFor="serialNumber"
											className="block font-medium text-gray-700 text-sm"
										>
											Seriennummer
										</label>
										<input
											type="text"
											name="serialNumber"
											id="serialNumber"
											required
											value={formData.serialNumber}
											onChange={handleChange}
											className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
										/>
									</div>

									<div>
										<label
											htmlFor="location"
											className="block font-medium text-gray-700 text-sm"
										>
											Standort (Optional)
										</label>
										<input
											type="text"
											name="location"
											id="location"
											value={formData.location}
											onChange={handleChange}
											className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
										/>
									</div>

									<div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
										<button
											type="submit"
											className="btn-primary inline-flex w-full justify-center sm:ml-3 sm:w-auto"
										>
											Speichern
										</button>
										<button
											type="button"
											onClick={onClose}
											className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto"
										>
											Abbrechen
										</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
