# curl -X GET http://localhost:1337/content-type-builder/content-types
#  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
GET http://localhost:1337/api/content-type-builder/content-types
Content-Type: application/json
Authorization: Bearer e2b97a9ca2c0316e31e870c1e228e8fb83c5503f1afdf5ba0b1d6746828259871b7bbb98eb1288521a88de5d137e57b401e7deefc2a7d3746c72e2892ccaca54067f7ae90c057793bd0b8d2eae120fb7e4d77d97626f84c9208e29dd8dc0ba52206589057fbe7282acfbbd9ae35eea71ff0567bdec37f6cb646d096d59243cbe

<> 2025-02-11T134050.200.json
<> 2025-02-11T113932.200.json
<> 2025-02-11T105200.200.json


###

GET https://jr.payter.eulektro.de/api/ocpi/versions
Authorization: Token a47bfd0283a292ebd71031eacc3ea748


### Longship fragt welche Module wir supporten. Wir schicken die Antwort
GET https://jr.payter.eulektro.de/api/ocpi/versions/2.2.1
Authorization: Token 92e832fe00de91be037a719bcb4e7a8d

###
GET https://beta.ocpi.longship.io/ocpi/versions
Authorization: Token 9775c711353e6dd2d42d3f7dc53bad0f


<> 2025-02-11T201448.401.json
<> 2025-02-11T200927.200.json


### wir schauen welchen Module es gibt und speichern diese sinnvoll ab
GET https://beta.ocpi.longship.io/ocpi/versions/2.2.1
Authorization: Token 9775c711353e6dd2d42d3f7dc53bad0f


### wir schicken unser sender secret an longship und erwarten ein receiving secret von longship
POST https://beta.ocpi.longship.io/ocpi/2.2.1/credentials
authorization: Token 202ef9186c3d9a10dc13f1803bdc8c9d
content-type: application/json

{
  "url": "https://jr.payter.eulektro.de/api/ocpi/versions",
  "token": "f76efc14d6d18320984941e7ac1953f5",
  "roles": [{
    "role": "EMSP",
    "party_id": "EUL",
    "country_code": "DE",
    "business_details": {
      "name": "Eulektro GmbH 5"
    }
  }]
}

###
# curl -H "Authorization: Token 92e832fe00de91be037a719bcb4e7a8d" https://jr.payter.eulektro.de/api/ocpi/versions/2.2.1
GET https://jr.payter.eulektro.de/api/ocpi/versions/2.2.1
Authorization: Token 92e832fe00de91be037a719bcb4e7a8d



###
GET https://jr.payter.eulektro.de/api/ocpi/2.2.1/credentials
Authorization: Token 92e832fe00de91be037a719bcb4e7a8d


####

### wir schicken unser sender secret an longship und erwarten ein receiving secret von longship
POST https://beta.ocpi.longship.io/ocpi/2.2.1/credentials
Authorization: Token 48af5a02dfe1c0a75fd592ba5e78d3d4
Content-Type: application/json

{
  "url": "https://jr.payter.eulektro.de/api/ocpi/versions",
  "token": "f76efc14d6d18320984941e7ac1953f8",
  "roles": [{
    "role": "EMSP",
    "party_id": "EUL",
    "country_code": "DE",
    "business_details": {
      "name": "Eulektro GmbH",
      "website": "https://eulektro.de",
      "logo": {
        "url": "https://eulektro.de/logo.png",
        "thumbnail": "https://eulektro.de/brand/Eulektro_Logo.svg",
        "category": "OPERATOR",
        "type": "png",
        "width": 512,
        "height": 512
      }
    }
  }]
}
