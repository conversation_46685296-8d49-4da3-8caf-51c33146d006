import type React from "react";

interface CustomSelectProps
	extends React.SelectHTMLAttributes<HTMLSelectElement> {
	options: { value: string; label: string }[];
	label?: string;
}

export const GradientCustomSelect: React.FC<CustomSelectProps> = ({
	options,
	label,
	id,
	className = "",
	...props
}) => {
	return (
		<div className="w-full">
			{label && (
				<label
					htmlFor={id}
					className="mb-1 block font-medium text-gray-700 text-sm"
				>
					{label}
				</label>
			)}
			<div className="relative">
				<div className="absolute inset-0 rounded-md bg-gradient-primary" />
				<select
					id={id}
					className={`relative z-10 block w-full appearance-none rounded-md border-0 bg-transparent py-2 pr-10 pl-3 text-white shadow-md focus:outline-none focus:ring-2 focus:ring-white/30 ${className}`}
					{...props}
				>
					{options.map((option) => (
						<option
							key={option.value}
							value={option.value}
							className="bg-primary-dark"
						>
							{option.label}
						</option>
					))}
				</select>
				<div className="pointer-events-none absolute inset-y-0 right-0 z-10 flex items-center px-2 text-white">
					<svg className="h-5 w-5 fill-current" viewBox="0 0 20 20">
						<path
							fillRule="evenodd"
							d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
							clipRule="evenodd"
						/>
					</svg>
				</div>
			</div>
		</div>
	);
};
