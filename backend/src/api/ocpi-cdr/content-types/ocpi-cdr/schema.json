{"kind": "collectionType", "collectionName": "ocpi_cdrs", "info": {"singularName": "ocpi-cdr", "pluralName": "ocpi-cdrs", "displayName": "OCPI CDR"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"cdrId": {"type": "string", "unique": true, "required": true}, "sessionId": {"type": "string", "description": "Session ID"}, "totalCost": {"type": "decimal", "required": true}, "currency": {"type": "string", "required": true}, "totalTime": {"type": "decimal", "required": true, "description": "Total time in hours"}, "timestamp": {"type": "datetime", "required": true}, "payment_session": {"type": "relation", "relation": "oneToOne", "target": "api::payment-session.payment-session", "mappedBy": "ocpi_cdr"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "ocpi_cdrs"}, "rawData": {"type": "json", "description": "Der gesamte CDR-Payload als JSON-String"}, "countryCode": {"type": "string", "description": "Country code of the CPO"}, "partyId": {"type": "string", "description": "Party ID of the CPO"}, "startDateTime": {"type": "datetime", "description": "Start time of the charging session"}, "endDateTime": {"type": "datetime", "description": "End time of the charging session"}, "authMethod": {"type": "string", "description": "Method used for authentication (e.g. COMMAND, AUTH_REQUEST)"}, "authorizationReference": {"type": "string", "description": "Reference to the authorization that started this charging session"}, "lastUpdated": {"type": "datetime", "description": "Timestamp when the CDR was last updated"}, "cdrLocation": {"type": "component", "component": "ocpi.cdr-location", "description": "Location where the charging session took place"}, "cdrToken": {"type": "component", "component": "ocpi.cdr-token", "description": "<PERSON><PERSON> used to start the charging session"}, "tariffs": {"type": "json", "description": "Tariffs used for this charging session"}, "chargingPeriods": {"type": "json", "description": "Charging periods during this charging session"}, "totalFixedCost": {"type": "decimal", "description": "Fixed costs for this charging session"}, "totalEnergyCost": {"type": "decimal", "description": "Energy costs for this charging session"}, "totalTimeCost": {"type": "decimal", "description": "Time costs for this charging session"}, "totalParkingCost": {"type": "decimal", "description": "Parking costs for this charging session"}, "totalEnergy": {"type": "decimal", "description": "Total energy delivered in kWh"}}}