/**
 * Hilfsfunktionen für die Mandantenzuordnung
 */

/**
 * Findet den Mandanten anhand einer Terminal-ID
 * 
 * @param strapi - Strapi-Instanz
 * @param terminalId - Die Terminal-ID
 * @returns Die Mandanten-ID oder null, wenn kein Mandant gefunden wurde
 */
export async function findMandantByTerminalId(strapi, terminalId: string): Promise<number | null> {
  try {
    // Suche nach dem Terminal in der Datenbank
    const terminals = await strapi.entityService.findMany('api::terminal.terminal', {
      filters: {
        SerialNumber: terminalId
      },
      populate: ['mandant']
    });

    // Wenn ein Terminal gefunden wurde und es einen Mandanten hat, gib die Mandanten-ID zurück
    if (terminals && terminals.length > 0 && terminals[0].mandant) {
      return terminals[0].mandant.documentId;
    }

    return null;
  } catch (error) {
    console.error(`Fehler beim Suchen des Mandanten für Terminal ${terminalId}:`, error);
    return null;
  }
}

/**
 * Findet den Root-Mandanten
 * 
 * @param strapi - Strapi-Instanz
 * @returns Die Mandanten-ID des Root-Mandanten oder null, wenn kein Root-Mandant gefunden wurde
 */
export async function findRootMandant(strapi): Promise<number | null> {
  try {
    // Suche nach dem Root-Mandanten in der Datenbank
    // Annahme: Der Root-Mandant hat die ID 1 oder einen speziellen Namen/Kennzeichen
    const rootMandant = await strapi.entityService.findOne('api::mandant.mandant', 1);

    if (rootMandant) {
      return rootMandant.documentId;
    }

    // Alternativ: Suche nach dem ersten Mandanten
    const mandants = await strapi.entityService.findMany('api::mandant.mandant', {
      sort: { documentId: 'asc' },
      limit: 1
    });

    if (mandants && mandants.length > 0) {
      return mandants[0].documentId;
    }

    return null;
  } catch (error) {
    console.error('Fehler beim Suchen des Root-Mandanten:', error);
    return null;
  }
}
