import type React from "react";

interface CustomSelectProps
	extends React.SelectHTMLAttributes<HTMLSelectElement> {
	options: { value: string; label: string }[];
	label?: string;
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
	options,
	label,
	id,
	className = "",
	...props
}) => {
	return (
		<div className="w-full">
			{label && (
				<label
					htmlFor={id}
					className="mb-1 block font-medium text-gray-700 text-sm"
				>
					{label}
				</label>
			)}
			<div className="relative">
				<select
					id={id}
					className={`block w-full appearance-none rounded-md border-0 bg-primary-dark py-2 pr-10 pl-3 text-white shadow-md focus:outline-none focus:ring-2 focus:ring-primary-light ${className}`}
					{...props}
				>
					{options.map((option) => (
						<option key={option.value} value={option.value}>
							{option.label}
						</option>
					))}
				</select>
				<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
					<svg className="h-5 w-5 fill-current" viewBox="0 0 20 20">
						<path
							fillRule="evenodd"
							d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
							clipRule="evenodd"
						/>
					</svg>
				</div>
			</div>
		</div>
	);
};

// Beispiel für die Verwendung:
/*
<CustomSelect
  id="example"
  name="example"
  label="Beispiel Select"
  options={[
    { value: "1", label: "Option 1" },
    { value: "2", label: "Option 2" },
    { value: "3", label: "Option 3" },
  ]}
  onChange={(e) => console.log(e.target.value)}
/>
*/
