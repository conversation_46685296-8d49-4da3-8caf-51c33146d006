const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON><PERSON>tertes Token-Minimierungs-Skript
 * Erzeugt eine stark komprimierte Darstellung des Codes mit minimaler Token-Anzahl.
 * Verarbeitet sowohl das Backend als auch das Frontend (Next.js-Projekt).
 */

// Standard Patterns, die wir erkennen und zusammenfassen
const STANDARD_PATTERNS = {
    CORE_CONTROLLER: /import\s*{\s*factories\s*}\s*from\s*['"]@strapi\/strapi['"]\s*[;\n].*export\s+default\s+factories\.createCoreController/s,
    CORE_SERVICE: /import\s*{\s*factories\s*}\s*from\s*['"]@strapi\/strapi['"]\s*[;\n].*export\s+default\s+factories\.createCoreService/s,
    CORE_ROUTER: /import\s*{\s*factories\s*}\s*from\s*['"]@strapi\/strapi['"]\s*[;\n].*export\s+default\s+factories\.createCoreRouter/s,
};

// Dateien, die wir komplett ignorieren wollen
const IGNORED_FILES = [
    'package-lock.json', '.DS_Store', '.strapi-updater.json',
    '.gitkeep', '.gitignore', '.env', '.env.example', 'robots.txt',
    'create-hello-world-plugin.sh'
];

// Standard ignorierte Pfade (für Backend u. Ä.)
const DEFAULT_IGNORED_PATHS = [
    'node_modules', 'dist', '.tmp', '.strapi', '.git',
    'test', 'tests', '__tests__', 'coverage', 'build', 'public/build',
    'dist-*', '.cache', 'vendor', 'uploads', 'public/uploads',
    'types/generated'
];

// Erweiterungen, die wir ignorieren wollen
const IGNORED_EXTENSIONS = [
    '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.webp', '.bmp', '.tiff',
    '.lock', '.md', '.log', '.map', '.min.js', '.d.ts', '.swp', '.bak', '.zip', '.tar.gz',
    '.ttf', '.woff', '.woff2', '.eot', '.pdf', '.exe', '.bin'
];

/**
 * Sucht alle Dateien in einem Verzeichnis, wobei optional eigene ignorierte Pfade übergeben werden können.
 */
async function findFiles(dir, customIgnoredPaths) {
    const ignoredPaths = customIgnoredPaths || DEFAULT_IGNORED_PATHS;
    let results = [];

    async function walk(directory) {
        try {
            const items = await fs.promises.readdir(directory, { withFileTypes: true });
            for (const item of items) {
                const itemPath = path.join(directory, item.name);
                // Bei Verzeichnissen prüfen wir, ob sie ignoriert werden sollen
                if (item.isDirectory()) {
                    if (ignoredPaths.some(ignorePath =>
                        itemPath.includes(`/${ignorePath}/`) ||
                        itemPath.endsWith(`/${ignorePath}`) ||
                        item.name === ignorePath)) {
                        continue;
                    }
                    await walk(itemPath);
                    continue;
                }
                // Dateien anhand von Namen ignorieren
                if (IGNORED_FILES.includes(item.name)) continue;
                // Dateien anhand der Erweiterung ignorieren
                const ext = path.extname(item.name).toLowerCase();
                if (IGNORED_EXTENSIONS.includes(ext)) continue;
                try {
                    const stats = await fs.promises.stat(itemPath);
                    results.push({
                        path: itemPath,
                        size: stats.size,
                        extension: ext
                    });
                } catch (error) {
                    console.error(`Fehler beim Lesen der Datei ${itemPath}:`, error.message);
                }
            }
        } catch (error) {
            console.error(`Fehler beim Lesen des Verzeichnisses ${directory}:`, error.message);
        }
    }
    await walk(dir);
    return results;
}

/**
 * Analysiert den Dateityp und Inhalt.
 */
function analyzeFile(filePath, content) {
    const fileName = path.basename(filePath).toLowerCase();
    const ext = path.extname(filePath).toLowerCase();

    let category = {
        type: 'unknown',
        importance: 'low',
        isStandard: false,
        standardType: null
    };

    // Prüfen auf Standardmuster (für Backend)
    if (STANDARD_PATTERNS.CORE_CONTROLLER.test(content)) {
        category.isStandard = true;
        category.standardType = 'controller';
        category.importance = 'low';
    } else if (STANDARD_PATTERNS.CORE_SERVICE.test(content)) {
        category.isStandard = true;
        category.standardType = 'service';
        category.importance = 'low';
    } else if (STANDARD_PATTERNS.CORE_ROUTER.test(content)) {
        category.isStandard = true;
        category.standardType = 'router';
        category.importance = 'low';
    }

    // Weitere Klassifizierungen (Backend-spezifisch)
    if (filePath.includes('/api/') && fileName.endsWith('schema.json')) {
        category.type = 'schema';
        category.importance = 'high';
    } else if (filePath.includes('/components/') && ext === '.json') {
        category.type = 'component';
        category.importance = 'medium';
    } else if (filePath.includes('/config/')) {
        category.type = 'config';
        category.importance = 'medium';
    } else if (filePath.includes('/controllers/') && !category.isStandard) {
        category.type = 'custom_controller';
        category.importance = 'high';
    } else if (filePath.includes('/services/') && !category.isStandard) {
        category.type = 'custom_service';
        category.importance = 'high';
    } else if (filePath.includes('/routes/') && !category.isStandard) {
        category.type = 'custom_route';
        category.importance = 'high';
    } else if (ext === '.json' && fileName === 'package.json') {
        category.type = 'package';
        category.importance = 'medium';
    } else if (fileName === 'index.ts' || fileName === 'index.js') {
        category.type = 'index';
        category.importance = 'medium';
    }

    return category;
}

/**
 * Komprimiert JSON-Schemadateien.
 */
function compressSchema(content) {
    try {
        const schema = JSON.parse(content);
        const simplified = {
            kind: schema.kind,
            collectionName: schema.collectionName,
            info: {
                singularName: schema.info?.singularName,
                pluralName: schema.info?.pluralName,
                displayName: schema.info?.displayName
            }
        };

        if (schema.attributes) {
            simplified.attributes = {};
            for (const [key, value] of Object.entries(schema.attributes)) {
                if (['createdAt', 'updatedAt', 'createdBy', 'updatedBy', 'publishedAt', 'locale', 'localizations'].includes(key)) {
                    continue;
                }
                if (value.type) {
                    simplified.attributes[key] = { type: value.type };
                    if (value.type === 'relation') {
                        simplified.attributes[key].relation = value.relation;
                        simplified.attributes[key].target = value.target;
                    }
                    if (value.type === 'enumeration') {
                        simplified.attributes[key].enum = value.enum;
                    }
                    if (value.type === 'component') {
                        simplified.attributes[key].component = value.component;
                        simplified.attributes[key].repeatable = value.repeatable;
                    }
                    if (value.required) {
                        simplified.attributes[key].required = true;
                    }
                }
            }
        }
        return JSON.stringify(simplified);
    } catch (e) {
        console.error('Fehler beim Komprimieren des Schemas:', e);
        return content;
    }
}

/**
 * Komprimiert Konfigurationsdateien.
 */
function compressConfig(content, filePath) {
    const fileName = path.basename(filePath);
    if (fileName.endsWith('.ts') || fileName.endsWith('.js')) {
        let compressed = content
            .replace(/\/\/.*$/gm, '')
            .replace(/\/\*[\s\S]*?\*\//g, '')
            .replace(/\s{2,}/g, ' ')
            .replace(/\n\s*\n/g, '\n')
            .trim();
        return compressed;
    }
    if (fileName.endsWith('.json')) {
        try {
            return JSON.stringify(JSON.parse(content));
        } catch (e) {
            return content;
        }
    }
    return content;
}

/**
 * Sammelt Standard-Dateien.
 */
function collectStandardFiles(files) {
    const standardFiles = {
        controllers: [],
        services: [],
        routes: []
    };
    files.forEach(file => {
        if (file.category?.isStandard) {
            switch (file.category.standardType) {
                case 'controller':
                    standardFiles.controllers.push(file.path);
                    break;
                case 'service':
                    standardFiles.services.push(file.path);
                    break;
                case 'router':
                    standardFiles.routes.push(file.path);
                    break;
            }
        }
    });
    return standardFiles;
}

/**
 * Extrahiert Datenmodell-Beziehungen aus den Schemadateien.
 */
function extractEntityRelationships(files) {
    const entities = {};
    const relationships = [];
    files.forEach(file => {
        if (file.category?.type === 'schema' && file.content) {
            try {
                const schema = JSON.parse(file.content);
                const entityName = schema.info?.displayName || path.basename(path.dirname(file.path));
                entities[entityName] = {
                    path: file.path,
                    attributes: schema.attributes || {}
                };
            } catch (e) {
                // Ignoriere Parsing-Fehler
            }
        }
    });
    Object.entries(entities).forEach(([entityName, entity]) => {
        Object.entries(entity.attributes).forEach(([attrName, attr]) => {
            if (attr.type === 'relation' && attr.target) {
                const targetParts = attr.target.split('::');
                if (targetParts.length > 1) {
                    const targetEntity = targetParts[1].split('.')[0];
                    let relation = `${entityName} -> ${targetEntity} (${attr.relation})`;
                    if (attrName) {
                        relation += ` via ${attrName}`;
                    }
                    relationships.push(relation);
                }
            }
        });
    });
    return relationships;
}

/**
 * Gruppiert ähnliche Komponenten.
 */
function groupComponents(files) {
    const components = {};
    files.forEach(file => {
        if (file.category?.type === 'component' && file.content) {
            try {
                const component = JSON.parse(file.content);
                const componentName = component.info?.displayName || path.basename(file.path, '.json');
                const componentGroup = path.basename(path.dirname(file.path));
                if (!components[componentGroup]) {
                    components[componentGroup] = [];
                }
                components[componentGroup].push({
                    name: componentName,
                    attributes: Object.keys(component.attributes || {})
                });
            } catch (e) {
                // Ignoriere Parsing-Fehler
            }
        }
    });
    return components;
}

async function main() {
    try {
        const startTime = Date.now();

        // ---------------------------
        // Verarbeitung des Backend-Projekts
        // ---------------------------
        console.log('Starte Token-Minimierung für Backend...');
        const allFilesBackend = await findFiles('backend');
        console.log(`Backend: Gefunden ${allFilesBackend.length} Dateien`);

        const processedFilesBackend = [];
        for (const file of allFilesBackend) {
            try {
                const content = await fs.promises.readFile(file.path, 'utf8');
                const category = analyzeFile(file.path, content);
                let processedContent = content;
                if (category.type === 'schema') {
                    processedContent = compressSchema(content);
                } else if (category.type === 'config') {
                    processedContent = compressConfig(content, file.path);
                } else if (category.isStandard) {
                    processedContent = null;
                } else if (category.type === 'component') {
                    processedContent = compressSchema(content);
                }
                processedFilesBackend.push({
                    ...file,
                    category,
                    content: processedContent
                });
            } catch (err) {
                console.error(`Fehler beim Verarbeiten von ${file.path}:`, err);
            }
        }
        const standardFiles = collectStandardFiles(processedFilesBackend);
        const relationships = extractEntityRelationships(processedFilesBackend);
        const components = groupComponents(processedFilesBackend);

        let outputBackend = '# BACKEND-STRUKTUR (KOMPRIMIERT)\n\n';
        outputBackend += '## PROJEKTÜBERSICHT\n';
        outputBackend += `Gesamtanzahl Dateien: ${allFilesBackend.length}\n`;
        outputBackend += `Analysierte Dateien: ${processedFilesBackend.length}\n\n`;
        outputBackend += '## STANDARD IMPLEMENTIERUNGEN\n';
        outputBackend += `Controller (${standardFiles.controllers.length}): ${standardFiles.controllers.map(f => path.basename(f)).join(', ')}\n`;
        outputBackend += `Services (${standardFiles.services.length}): ${standardFiles.services.map(f => path.basename(f)).join(', ')}\n`;
        outputBackend += `Routes (${standardFiles.routes.length}): ${standardFiles.routes.map(f => path.basename(f)).join(', ')}\n\n`;
        outputBackend += '## ENTITY-BEZIEHUNGEN\n';
        outputBackend += relationships.join('\n') + "\n\n";
        outputBackend += '## KOMPONENTEN\n';
        for (const [group, items] of Object.entries(components)) {
            outputBackend += `### ${group}\n`;
            items.forEach(item => {
                outputBackend += `- ${item.name}: ${item.attributes.join(', ')}\n`;
            });
            outputBackend += '\n';
        }
        outputBackend += '## BENUTZERDEFINIERTE IMPLEMENTIERUNGEN\n';
        for (const file of processedFilesBackend) {
            if ((file.category.type === 'custom_controller' ||
                    file.category.type === 'custom_service' ||
                    file.category.type === 'custom_route') &&
                file.content) {
                outputBackend += `### ${file.path}\n`;
                outputBackend += file.content + "\n\n";
            }
        }
        outputBackend += '## DATENMODELLE (KOMPRIMIERT)\n';
        for (const file of processedFilesBackend) {
            if (file.category.type === 'schema' && file.content) {
                outputBackend += `### ${path.basename(path.dirname(file.path))}\n`;
                outputBackend += file.content + "\n\n";
            }
        }
        outputBackend += '## KONFIGURATIONEN\n';
        for (const file of processedFilesBackend) {
            if (file.category.type === 'config' && file.content) {
                outputBackend += `### ${file.path}\n`;
                outputBackend += file.content + "\n\n";
            }
        }
        const packageFileBackend = processedFilesBackend.find(f => path.basename(f.path) === 'package.json');
        if (packageFileBackend && packageFileBackend.content) {
            outputBackend += '## PACKAGE.JSON\n';
            try {
                const pkg = JSON.parse(packageFileBackend.content);
                outputBackend += `Name: ${pkg.name}\n`;
                outputBackend += `Version: ${pkg.version}\n`;
                outputBackend += `Dependencies: ${Object.keys(pkg.dependencies || {}).join(', ')}\n`;
                outputBackend += `DevDependencies: ${Object.keys(pkg.devDependencies || {}).join(', ')}\n`;
            } catch (e) {
                outputBackend += packageFileBackend.content;
            }
            outputBackend += "\n\n";
        }
        await fs.promises.writeFile('backend_hyper_minimized.txt', outputBackend);
        console.log('Backend Token-Minimierung abgeschlossen.');

        // ---------------------------
        // Verarbeitung des Frontend-Projekts (Next.js)
        // ---------------------------
        console.log('Starte Token-Minimierung für Frontend (Next.js)...');
        // Für Frontend sollen alle Dateien einbezogen werden, außer node_modules und .next
        const allFilesFrontend = await findFiles('frontend', ['node_modules', '.next']);
        console.log(`Frontend: Gefunden ${allFilesFrontend.length} Dateien`);

        const processedFilesFrontend = [];
        for (const file of allFilesFrontend) {
            try {
                const content = await fs.promises.readFile(file.path, 'utf8');
                let category = analyzeFile(file.path, content);

                // Next.js-spezifische Klassifizierung anhand des Pfades
                if (file.path.includes('/pages/api/')) {
                    category.type = 'next_api';
                    category.importance = 'high';
                } else if (file.path.includes('/pages/')) {
                    category.type = 'next_page';
                    category.importance = 'high';
                } else if (file.path.includes('/components/')) {
                    category.type = 'next_component';
                    category.importance = 'medium';
                } else if (path.basename(file.path) === 'next.config.js') {
                    category.type = 'next_config';
                    category.importance = 'medium';
                } else if (file.path.includes('/pages/_app') || file.path.includes('/pages/_document')) {
                    category.type = 'next_custom';
                    category.importance = 'high';
                } else {
                    // Alle anderen Dateien
                    category.type = 'next_other';
                    category.importance = 'low';
                }

                let processedContent = content;
                if (category.type === 'next_config') {
                    processedContent = compressConfig(content, file.path);
                } else {
                    processedContent = content
                        .replace(/\/\/.*$/gm, '')
                        .replace(/\/\*[\s\S]*?\*\//g, '')
                        .replace(/\s{2,}/g, ' ')
                        .replace(/\n\s*\n/g, '\n')
                        .trim();
                }

                processedFilesFrontend.push({
                    ...file,
                    category,
                    content: processedContent
                });
            } catch (err) {
                console.error(`Fehler beim Verarbeiten von ${file.path}:`, err);
            }
        }

        // Gruppiere Next.js-spezifische Dateien
        const nextConfigs = processedFilesFrontend.filter(f => f.category.type === 'next_config');
        const nextPages = processedFilesFrontend.filter(f => f.category.type === 'next_page');
        const nextApis = processedFilesFrontend.filter(f => f.category.type === 'next_api');
        const nextComponents = processedFilesFrontend.filter(f => f.category.type === 'next_component');
        const nextCustom = processedFilesFrontend.filter(f => f.category.type === 'next_custom');
        const nextOthers = processedFilesFrontend.filter(f => f.category.type === 'next_other');

        let outputFrontend = '# FRONTEND-STRUKTUR (NEXTJS, KOMPROMIERT)\n\n';
        outputFrontend += '## PROJEKTÜBERSICHT (Frontend)\n';
        outputFrontend += `Gesamtanzahl Dateien: ${allFilesFrontend.length}\n`;
        outputFrontend += `Analysierte Dateien: ${processedFilesFrontend.length}\n\n`;

        if (nextConfigs.length > 0) {
            outputFrontend += '## NEXTJS KONFIGURATION\n';
            nextConfigs.forEach(file => {
                outputFrontend += `### ${file.path}\n`;
                outputFrontend += file.content + "\n\n";
            });
        }

        if (nextPages.length > 0) {
            outputFrontend += '## SEITEN (PAGES)\n';
            nextPages.forEach(file => {
                outputFrontend += `### ${file.path}\n`;
                outputFrontend += file.content + "\n\n";
            });
        }

        if (nextApis.length > 0) {
            outputFrontend += '## API ROUTEN (pages/api)\n';
            nextApis.forEach(file => {
                outputFrontend += `### ${file.path}\n`;
                outputFrontend += file.content + "\n\n";
            });
        }

        if (nextComponents.length > 0) {
            outputFrontend += '## KOMPONENTEN\n';
            nextComponents.forEach(file => {
                outputFrontend += `### ${file.path}\n`;
                outputFrontend += file.content + "\n\n";
            });
        }

        if (nextCustom.length > 0) {
            outputFrontend += '## BENUTZERDEFINIERTE NEXTJS IMPLEMENTIERUNGEN\n';
            nextCustom.forEach(file => {
                outputFrontend += `### ${file.path}\n`;
                outputFrontend += file.content + "\n\n";
            });
        }

        if (nextOthers.length > 0) {
            outputFrontend += '## SONSTIGE DATEIEN\n';
            nextOthers.forEach(file => {
                outputFrontend += `### ${file.path}\n`;
                outputFrontend += file.content + "\n\n";
            });
        }

        const packageFileFrontend = processedFilesFrontend.find(f => path.basename(f.path) === 'package.json');
        if (packageFileFrontend && packageFileFrontend.content) {
            outputFrontend += '## PACKAGE.JSON\n';
            try {
                const pkg = JSON.parse(packageFileFrontend.content);
                outputFrontend += `Name: ${pkg.name}\n`;
                outputFrontend += `Version: ${pkg.version}\n`;
                outputFrontend += `Dependencies: ${Object.keys(pkg.dependencies || {}).join(', ')}\n`;
                outputFrontend += `DevDependencies: ${Object.keys(pkg.devDependencies || {}).join(', ')}\n`;
            } catch (e) {
                outputFrontend += packageFileFrontend.content;
            }
            outputFrontend += "\n\n";
        }
        await fs.promises.writeFile('frontend_hyper_minimized.txt', outputFrontend);
        console.log('NextJS Token-Minimierung abgeschlossen für Frontend!');

        const endTime = Date.now();
        const seconds = ((endTime - startTime) / 1000).toFixed(2);
        console.log(`Token-Minimierung abgeschlossen in ${seconds} Sekunden!`);
    } catch (error) {
        console.error('Fehler bei der Ausführung:', error);
    }
}

main();
