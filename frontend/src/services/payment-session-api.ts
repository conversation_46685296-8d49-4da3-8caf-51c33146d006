import { apiClient } from "./api";

/**
 * API-Service für Payment-Sessions
 */
export const paymentSessionDetailApi = {
	/**
	 * Ruft die Details einer Payment-Session ab
	 * @param id Die ID der Payment-Session
	 * @returns Die Details der Payment-Session
	 */
	getById: async (id: string) => {
		try {
			const response = await apiClient.get(`/api/payment-sessions/${id}`, {
				params: {
					populate: {
						terminal: {
							fields: ["serialNumber", "terminalName"],
						},
						mandant: {
							fields: ["name"],
						},
						ocpi_evse: {
							fields: ["evseId", "labelForTerminal"],
							populate: {
								location: {
									fields: ["name", "address", "city", "postalCode", "country"],
								},
							},
						},
						ocpi_session: {
							fields: [
								"sessionId",
								"startTime",
								"endTime",
								"kwh",
								"ocpiStatus",
							],
						},
						ocpi_cdr: {
							fields: [
								"cdrId",
								"totalCost",
								"currency",
								"totalEnergy",
								"totalTime",
								"startDateTime",
								"endDateTime",
							],
						},
						invoice: {
							fields: ["invoice_number", "invoice_status"],
						},
					},
				},
			});
			return response.data;
		} catch (error) {
			console.error(`Error fetching payment session with ID ${id}:`, error);
			throw error;
		}
	},

	/**
	 * Führt eine Capture-Operation für eine Payment-Session durch
	 * @param id Die ID der Payment-Session
	 * @param amount Der zu belastende Betrag in Cent
	 * @returns Das Ergebnis der Capture-Operation
	 */
	capturePayment: async (id: string, amount: number) => {
		try {
			const response = await apiClient.post(
				`/api/payment-sessions/${id}/capture`,
				{
					amount,
				},
			);
			return response.data;
		} catch (error) {
			console.error(
				`Error capturing payment for session with ID ${id}:`,
				error,
			);
			throw error;
		}
	},
};
