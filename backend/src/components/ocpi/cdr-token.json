{"collectionName": "components_ocpi_cdr_tokens", "info": {"displayName": "CDR Token", "description": "OCPI CDR Token Component"}, "options": {}, "attributes": {"uid": {"type": "string", "description": "Token UID"}, "type": {"type": "string", "description": "Token type (e.g. AD_HOC_USER, RFID)"}, "contractId": {"type": "string", "description": "Contract ID"}, "countryCode": {"type": "string", "description": "Country code"}, "partyId": {"type": "string", "description": "Party ID"}, "issuer": {"type": "string", "description": "Token issuer"}, "visualNumber": {"type": "string", "description": "Visual number on the token"}, "lastUpdated": {"type": "datetime", "description": "Last updated timestamp"}}}