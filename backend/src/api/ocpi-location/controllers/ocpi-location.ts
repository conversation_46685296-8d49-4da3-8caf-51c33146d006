'use strict';

import { updateTerminalScreenByLocationStatus } from "../../terminal/services/terminal-status";

/**
 * Interface für eingehende Location-Daten (OCPI-Format)
 */
interface OCPIConnector {
    id: string;
    standard: string;
    format: string;
    power_type: string;
    max_voltage: number;
    max_amperage: number;
    max_electric_power: number;
    tariff_ids: string[];
    last_updated: string;
}

interface OCPIEVSE {
    uid: string;
    evse_id: string;
    status: string;
    capabilities: string[];
    connectors: OCPIConnector[];
    coordinates?: {
        latitude: string;
        longitude: string;
    };
    physical_reference?: string;
    parking_restrictions?: string[];
    last_updated: string;
}

interface OCPIOpeningTimes {
    twentyfourseven: boolean;
    regular_hours?: {
        weekday: number;
        period_begin: string;
        period_end: string;
    }[];
    exceptional_openings?: {
        period_begin: string;
        period_end: string;
    }[];
    exceptional_closings?: {
        period_begin: string;
        period_end: string;
    }[];
}

interface OCPILocationData {
    id: string;
    country_code: string;
    party_id: string;
    publish: boolean;
    publish_allowed_to?: any[];
    name: string;
    address: string;
    city: string;
    postal_code: string;
    country: string;
    coordinates: {
        latitude: string;
        longitude: string;
    };
    evses?: OCPIEVSE[];
    directions?: any[];
    facilities?: string[];
    time_zone?: string;
    opening_times?: OCPIOpeningTimes;
    charging_when_closed?: boolean;
    last_updated: string;
    status?: string;
}

/**
 * Interface für das Update-Objekt (interne Datenbank-Format)
 */
interface LocationUpdateData {
    name?: string;
    address?: string;
    city?: string;
    postalCode?: string;
    country?: string;
    countryCode?: string;
    partyId?: string;
    publish?: boolean;
    publishAllowedTo?: any[];
    coordinates?: {
        latitude: string;
        longitude: string;
    };
    directions?: any[];
    facilities?: string[];
    timeZone?: string;
    openingTimes?: {
        twentyfourseven: boolean;
        regularHours?: any[];
        exceptionalOpenings?: any[];
        exceptionalClosings?: any[];
    };
    chargingWhenClosed?: boolean;
    lastUpdated: Date;
}

/**
 * Interface für das neue Location-Objekt (interne Datenbank-Format)
 */
interface NewLocationData {
    ocpiId: string;
    countryCode: string;
    partyId: string;
    publish: boolean;
    publishAllowedTo: any[];
    name: string;
    address: string;
    city: string;
    postalCode: string;
    country: string;
    lastUpdated: Date;
    ocpiConnection: any;
    coordinates: {
        latitude: string;
        longitude: string;
    };
    directions?: any[];
    facilities?: string[];
    timeZone?: string;
    openingTimes?: {
        twentyfourseven: boolean;
        regularHours?: any[];
        exceptionalOpenings?: any[];
        exceptionalClosings?: any[];
    };
    chargingWhenClosed?: boolean;
}

/**
 * Interface für das Location-Update-Objekt (für PUT-Requests)
 */
interface LocationToUpdate {
    name: any;
    address: any;
    city: any;
    postalCode: any;
    country: any;
    countryCode: any;
    partyId: any;
    publish: any;
    publishAllowedTo: any;
    lastUpdated: Date;
    coordinates: {
        latitude: any;
        longitude: any;
    };
    directions?: any[];
    facilities?: string[];
    timeZone?: string;
    openingTimes?: {
        twentyfourseven: boolean;
        regularHours?: any[];
        exceptionalOpenings?: any[];
        exceptionalClosings?: any[];
    };
    chargingWhenClosed?: boolean;
}

/**
 * Interface für das Location-Create-Objekt (für PUT-Requests)
 */
interface LocationToCreate {
    ocpiId: any;
    name: any;
    address: any;
    city: any;
    postalCode: any;
    country: any;
    countryCode: any;
    partyId: any;
    publish: any;
    publishAllowedTo: any;
    lastUpdated: Date;
    coordinates: {
        latitude: any;
        longitude: any;
    };
    ocpiConnection: any;
    directions?: any[];
    facilities?: string[];
    timeZone?: string;
    openingTimes?: {
        twentyfourseven: boolean;
        regularHours?: any[];
        exceptionalOpenings?: any[];
        exceptionalClosings?: any[];
    };
    chargingWhenClosed?: boolean;
}

/**
 * OCPI Location Controller für OCPI 2.2.1
 * Implementiert GET, PUT und PATCH Endpunkte für Locations gemäß OCPI 2.2.1 Spezifikation
 */
module.exports = {
    /**
     * GET /ocpi/2.2.1/locations
     * Abrufen von Locations aus dem System
     */
    async getLocations(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            if (!validationResult.valid) {
                return ctx.send(validationResult, 401);
            }

            const connection = validationResult.connection;

            // Locations basierend auf der Verbindung abrufen
            // Hier müsste die tatsächliche Logik implementiert werden, um Locations abzurufen
            // Dies ist nur ein Beispiel
            const locations = await strapi.documents('api::ocpi-location.ocpi-location').findMany({
                where: {
                    mandant: {
                        id: connection.mandants[0]?.id
                    }
                },
                populate: ['evses.connectors']
            });

            await logger.info(`Locations wurden erfolgreich abgerufen`, {
                endpoint: 'locations',
                action: 'GET',
                mandantId: connection.mandants[0]?.id,
                connectionId: connection.id
            });

            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                data: locations || [],
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            strapi.log.error('Fehler beim Abrufen von Locations:', error);

            return ctx.send({
                status_code: 3000,
                status_message: `Server error: ${error.message}`,
                timestamp: new Date().toISOString()
            }, 500);
        }
    },

    /**
     * PUT /ocpi/2.2.1/locations
     * Aktualisieren oder Erstellen von Locations
     */
    async putLocation(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');
            const logger = strapi.service('api::ocpi-log.ocpi-logger');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            if (!validationResult.valid) {
                return ctx.send(validationResult, 401);
            }

            const connection = validationResult.connection;
            const locationData = ctx.request.body;

            if (!locationData || !locationData.id) {
                return ctx.send({
                    status_code: 2001,
                    status_message: "Invalid or missing location data",
                    timestamp: new Date().toISOString()
                }, 400);
            }

            const existingLocation = await strapi.documents('api::ocpi-location.ocpi-location').findFirst({
                filters: {
                    ocpiId: locationData.id,
                }
            });

            let result;
            if (existingLocation) {
                // Location-Daten für das Update vorbereiten
                const locationToUpdate: LocationToUpdate = {
                    name: locationData.name,
                    address: locationData.address,
                    city: locationData.city,
                    postalCode: locationData.postal_code,
                    country: locationData.country,
                    countryCode: locationData.country_code,
                    partyId: locationData.party_id,
                    publish: locationData.publish !== undefined ? locationData.publish : true,
                    publishAllowedTo: locationData.publish_allowed_to || [],
                    lastUpdated: new Date(),
                    // Standardwerte für erforderliche Felder
                    coordinates: {
                        latitude: locationData.coordinates?.latitude || '0.000000',
                        longitude: locationData.coordinates?.longitude || '0.000000'
                    }
                };

                // Optionale Felder hinzufügen, wenn vorhanden
                if (locationData.directions) locationToUpdate.directions = locationData.directions;
                if (locationData.facilities) locationToUpdate.facilities = locationData.facilities;
                if (locationData.time_zone) locationToUpdate.timeZone = locationData.time_zone;
                if (locationData.charging_when_closed !== undefined) locationToUpdate.chargingWhenClosed = locationData.charging_when_closed;

                // Opening Times hinzufügen, wenn vorhanden
                if (locationData.opening_times) {
                    locationToUpdate.openingTimes = {
                        twentyfourseven: locationData.opening_times.twentyfourseven,
                        regularHours: locationData.opening_times.regular_hours || [],
                        exceptionalOpenings: locationData.opening_times.exceptional_openings || [],
                        exceptionalClosings: locationData.opening_times.exceptional_closings || []
                    };
                }

                // Location aktualisieren
                result = await strapi.documents('api::ocpi-location.ocpi-location').update({
                    documentId: existingLocation.documentId,
                    data: locationToUpdate
                });

                await logger.info(`Location ${locationData.id} wurde aktualisiert`, {
                    endpoint: 'locations',
                    action: 'PUT',
                    mandantId: connection.mandants[0]?.id,
                    connectionId: connection.id,
                    locationId: locationData.id
                });
            } else {
                // Location-Daten für die Erstellung vorbereiten
                const locationToCreate: LocationToCreate = {
                    ocpiId: locationData.id,
                    name: locationData.name,
                    address: locationData.address,
                    city: locationData.city,
                    postalCode: locationData.postal_code,
                    country: locationData.country,
                    countryCode: locationData.country_code,
                    partyId: locationData.party_id,
                    publish: locationData.publish !== undefined ? locationData.publish : true,
                    publishAllowedTo: locationData.publish_allowed_to || [],
                    lastUpdated: new Date(),
                    // Standardwerte für erforderliche Felder
                    coordinates: {
                        latitude: locationData.coordinates?.latitude || '0.000000',
                        longitude: locationData.coordinates?.longitude || '0.000000'
                    },
                    // Verbindung zur OCPI-Connection herstellen
                    ocpiConnection: connection.id
                };

                // Optionale Felder hinzufügen, wenn vorhanden
                if (locationData.directions) locationToCreate.directions = locationData.directions;
                if (locationData.facilities) locationToCreate.facilities = locationData.facilities;
                if (locationData.time_zone) locationToCreate.timeZone = locationData.time_zone;
                if (locationData.charging_when_closed !== undefined) locationToCreate.chargingWhenClosed = locationData.charging_when_closed;

                // Opening Times hinzufügen, wenn vorhanden
                if (locationData.opening_times) {
                    locationToCreate.openingTimes = {
                        twentyfourseven: locationData.opening_times.twentyfourseven,
                        regularHours: locationData.opening_times.regular_hours || [],
                        exceptionalOpenings: locationData.opening_times.exceptional_openings || [],
                        exceptionalClosings: locationData.opening_times.exceptional_closings || []
                    };
                }

                // Neue Location erstellen
                result = await strapi.documents('api::ocpi-location.ocpi-location').create({
                    data: locationToCreate
                });

                await logger.info(`Neue Location ${locationData.id} wurde erstellt`, {
                    endpoint: 'locations',
                    action: 'PUT',
                    mandantId: connection.mandants[0]?.id,
                    connectionId: connection.id,
                    locationId: locationData.id
                });
            }

            // EVSEs verarbeiten
            if (locationData.evses && Array.isArray(locationData.evses)) {
                try {
                    // Verwende die processEvses-Methode, um die EVSEs zu verarbeiten
                    await this.processEvses(locationData.evses, result, connection, logger);

                    await logger.info(`EVSEs für Location ${locationData.id} wurden verarbeitet`, {
                        endpoint: 'locations',
                        action: 'PUT_EVSES',
                        mandantId: connection.mandant?.id,
                        connectionId: connection.id,
                        locationId: locationData.id
                    });
                } catch (evseError) {
                    await logger.error(`Fehler beim Verarbeiten der EVSEs: ${evseError.message}`, {
                        endpoint: 'locations',
                        action: 'PUT_EVSES',
                        mandantId: connection.mandant?.id,
                        connectionId: connection.id,
                        locationId: locationData.id,
                        error: {
                            message: evseError.message,
                            stack: evseError.stack
                        }
                    });
                    // Wir setzen den Prozess fort, auch wenn die EVSE-Verarbeitung fehlschlägt
                }
            }

            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            strapi.log.error('Fehler beim Aktualisieren der Location:', error);

            return ctx.send({
                status_code: 3000,
                status_message: `Server error: ${error.message}`,
                timestamp: new Date().toISOString()
            }, 500);
        }
    },


    /**
     * Controller-Methode zum teilweisen Aktualisieren einer Location, EVSE oder eines Connectors
     * basierend auf der URL: {locations_endpoint_url}/{country_code}/{party_id}/{location_id}[/{evse_uid}][/{connector_id}]
     */
    async patchLocation(ctx) {

        const logger = strapi.service('api::ocpi-log.ocpi-logger');
        let connectionId = null;
        let locationId = ctx.request.params.location || null;
        let mandantId = null;
        const evseUid = ctx.request.params.evse || null;
        const connectorId = ctx.request.params.connector || null;
        const country = ctx.request.params.country || null;
        const party = ctx.request.params.party || null;

        // Bestimme den Update-Typ basierend auf den URL-Parametern
        let updateType = 'location';
        if (evseUid) {
            updateType = connectorId ? 'connector' : 'evse';
        }

        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            if (!validationResult.valid) {
                return ctx.send(validationResult, 401);
            }

            const connection = validationResult.connection;
            connectionId = connection.id;
            mandantId = connection.mandants[0]?.documentId;

            // Request-Body validieren
            const requestData = ctx.request.body;

            if (!requestData || !requestData.last_updated) {
                await logger.warn(`Ungültige oder fehlende Daten im PATCH-Request (${updateType})`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    requestBody: ctx.request.body
                });

                return ctx.send({
                    status_code: 2001,
                    status_message: "Invalid or missing data. PATCH request must contain last_updated field.",
                    timestamp: new Date().toISOString()
                }, 400);
            }

            // Prüfen, ob Location existiert
            let existingLocation = await strapi.documents('api::ocpi-location.ocpi-location').findFirst({
                filters: {
                    ocpiId: locationId,
                },
                populate: {
                    evses: true
                }
            });

            if (!existingLocation) {
                await logger.warn(`Location mit ID ${locationId} wurde nicht gefunden. PATCH kann keine neuen Locations erstellen.`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId
                });

                return ctx.send({
                    status_code: 2003,
                    status_message: `Location with ID ${locationId} not found. PATCH cannot create new locations.`,
                    timestamp: new Date().toISOString()
                }, 404);
            }

            // Je nach Update-Typ unterschiedlich verarbeiten
            if (updateType === 'connector') {
                // Connector-Update
                await logger.info(`PATCH-Request für Connector ${connectorId} von EVSE ${evseUid} in Location ${locationId}`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId,
                    evseUid,
                    connectorId
                });

                // Prüfen, ob die EVSE existiert
                const existingEvse = await strapi.documents('api::ocpi-evse.ocpi-evse').findFirst({
                    filters: {
                        uid: evseUid,
                        location: {
                            documentId: existingLocation.documentId
                        }
                    }
                });

                if (!existingEvse) {
                    await logger.warn(`EVSE ${evseUid} wurde nicht gefunden in Location ${locationId}`, {
                        endpoint: 'locations',
                        action: 'PATCH',
                        updateType,
                        mandantId,
                        connectionId,
                        locationId,
                        evseUid
                    });

                    return ctx.send({
                        status_code: 2003,
                        status_message: `EVSE with UID ${evseUid} not found in Location ${locationId}`,
                        timestamp: new Date().toISOString()
                    }, 404);
                }

                // Connector in der EVSE finden und aktualisieren
                const connectors = existingEvse.connectors as unknown as OCPIConnector[];

                // Manuell nach dem Connector mit der passenden ID suchen
                let connectorIndex = -1;
                for (let i = 0; i < connectors.length; i++) {
                    if (connectors[i].id === connectorId) {
                        connectorIndex = i;
                        break;
                    }
                }

                if (connectorIndex === -1) {
                    await logger.warn(`Connector ${connectorId} wurde nicht gefunden in EVSE ${evseUid}`, {
                        endpoint: 'locations',
                        action: 'PATCH',
                        updateType,
                        mandantId,
                        connectionId,
                        locationId,
                        evseUid,
                        connectorId
                    });

                    return ctx.send({
                        status_code: 2003,
                        status_message: `Connector with ID ${connectorId} not found in EVSE ${evseUid}`,
                        timestamp: new Date().toISOString()
                    }, 404);
                }

                // Connector aktualisieren (nur die übergebenen Felder)
                const connector: any = connectors[connectorIndex];
                const updatedConnector: any = { ...connector };

                // Nur die übergebenen Felder aktualisieren
                if (requestData.standard !== undefined) updatedConnector.standard = requestData.standard;
                if (requestData.format !== undefined) updatedConnector.format = requestData.format;
                if (requestData.power_type !== undefined) updatedConnector.powerType = requestData.power_type;
                if (requestData.max_voltage !== undefined) updatedConnector.maxVoltage = requestData.max_voltage;
                if (requestData.max_amperage !== undefined) updatedConnector.maxAmperage = requestData.max_amperage;
                if (requestData.max_electric_power !== undefined) updatedConnector.maxElectricPower = requestData.max_electric_power;
                if (requestData.tariff_ids !== undefined) updatedConnector.tariffIds = requestData.tariff_ids;
                updatedConnector.lastUpdated = requestData.last_updated;

                // Connector im Array ersetzen
                connectors[connectorIndex] = updatedConnector;

                // EVSE mit aktualisiertem Connector speichern
                await strapi.documents('api::ocpi-evse.ocpi-evse').update({
                    documentId: existingEvse.documentId,
                    data: {
                        connectors: JSON.stringify(connectors),
                        lastUpdated: new Date(requestData.last_updated)
                    }
                });

                // Auch die lastUpdated der Location aktualisieren
                await strapi.documents('api::ocpi-location.ocpi-location').update({
                    documentId: existingLocation.documentId,
                    data: {
                        lastUpdated: new Date(requestData.last_updated)
                    }
                });

                await logger.info(`Connector ${connectorId} in EVSE ${evseUid} wurde aktualisiert`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId,
                    evseUid,
                    connectorId,
                    updatedFields: Object.keys(requestData).filter(key => key !== 'last_updated')
                });

                return ctx.send({
                    status_code: 1000,
                    status_message: "Success",
                    timestamp: new Date().toISOString()
                });
            } else if (updateType === 'evse') {
                // EVSE-Update
                await logger.info(`PATCH-Request für EVSE ${evseUid} in Location ${locationId}`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId,
                    evseUid
                });

                // Prüfen, ob die EVSE existiert
                const existingEvse = await strapi.documents('api::ocpi-evse.ocpi-evse').findFirst({
                    filters: {
                        uid: evseUid,
                        location: {
                            documentId: existingLocation.documentId
                        }
                    }
                });

                if (!existingEvse) {
                    await logger.warn(`EVSE ${evseUid} wurde nicht gefunden in Location ${locationId}`, {
                        endpoint: 'locations',
                        action: 'PATCH',
                        updateType,
                        mandantId,
                        connectionId,
                        locationId,
                        evseUid
                    });

                    return ctx.send({
                        status_code: 2003,
                        status_message: `EVSE with UID ${evseUid} not found in Location ${locationId}`,
                        timestamp: new Date().toISOString()
                    }, 404);
                }

                // EVSE aktualisieren (nur die übergebenen Felder)
                const evseUpdateData: any = {
                    lastUpdated: new Date(requestData.last_updated)
                };

                // Nur die übergebenen Felder aktualisieren
                if (requestData.status !== undefined) evseUpdateData.ocpiStatus = requestData.status;
                if (requestData.evse_id !== undefined) evseUpdateData.evseId = requestData.evse_id;
                if (requestData.capabilities !== undefined) evseUpdateData.capabilities = requestData.capabilities;
                if (requestData.physical_reference !== undefined) {
                    evseUpdateData.physicalReference = requestData.physical_reference;
                    evseUpdateData.labelForTerminal = (requestData.physical_reference || existingEvse.evseId || evseUid).slice(-12);
                }
                if (requestData.parking_restrictions !== undefined) evseUpdateData.parkingRestrictions = requestData.parking_restrictions;

                // Koordinaten aktualisieren, wenn vorhanden
                if (requestData.coordinates && requestData.coordinates.latitude && requestData.coordinates.longitude) {
                    evseUpdateData.coordinates = {
                        latitude: requestData.coordinates.latitude,
                        longitude: requestData.coordinates.longitude
                    };
                }

                // Connectors aktualisieren, wenn vorhanden
                if (requestData.connectors) {
                    evseUpdateData.connectors = requestData.connectors.map(connector => ({
                        id: connector.id,
                        standard: connector.standard,
                        format: connector.format,
                        powerType: connector.power_type,
                        maxVoltage: connector.max_voltage,
                        maxAmperage: connector.max_amperage,
                        maxElectricPower: connector.max_electric_power,
                        tariffIds: connector.tariff_ids || [],
                        lastUpdated: connector.last_updated
                    }));
                }

                // EVSE aktualisieren
                // Prüfen, ob sich der Status geändert hat
                const statusChanged = requestData.status !== undefined && existingEvse.ocpiStatus !== requestData.status;

                await strapi.documents('api::ocpi-evse.ocpi-evse').update({
                    documentId: existingEvse.documentId,
                    data: evseUpdateData
                });

                // Auch die lastUpdated der Location aktualisieren
                await strapi.documents('api::ocpi-location.ocpi-location').update({
                    documentId: existingLocation.documentId,
                    data: {
                        lastUpdated: new Date(requestData.last_updated)
                    }
                });

                // Wenn sich der Status geändert hat, aktualisiere den Terminal-Screen
                if (statusChanged) {
                    try {
                        await updateTerminalScreenByLocationStatus(locationId);
                        await logger.info(`Terminal-Screen für Location ${locationId} aktualisiert aufgrund von EVSE-Statusänderung (Terminals im MAINTENANCE-Zustand werden nicht aktualisiert)`, {
                            endpoint: 'locations',
                            action: 'UPDATE_TERMINAL_SCREEN',
                            mandantId,
                            connectionId,
                            locationId,
                            evseUid,
                            oldStatus: existingEvse.ocpiStatus,
                            newStatus: requestData.status
                        });
                    } catch (screenError) {
                        await logger.error(`Fehler beim Aktualisieren des Terminal-Screens für Location ${locationId}: ${screenError.message}`, {
                            endpoint: 'locations',
                            action: 'UPDATE_TERMINAL_SCREEN',
                            mandantId,
                            connectionId,
                            locationId,
                            evseUid,
                            error: {
                                message: screenError.message,
                                stack: screenError.stack
                            }
                        });
                    }
                }

                await logger.info(`EVSE ${evseUid} in Location ${locationId} wurde aktualisiert`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId,
                    evseUid,
                    updatedFields: Object.keys(evseUpdateData).filter(key => key !== 'lastUpdated')
                });

                return ctx.send({
                    status_code: 1000,
                    status_message: "Success",
                    timestamp: new Date().toISOString()
                });
            } else {
                // Location-Update
                await logger.info(`PATCH-Request für Location ${locationId}`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId
                });

                // Location aktualisieren (nur geänderte Felder)
                const updateData: LocationUpdateData = {
                    lastUpdated: new Date(requestData.last_updated)
                };

                // Optionale Felder prüfen und nur überschreiben, wenn sie im Request vorhanden sind
                if (requestData.name !== undefined) updateData.name = requestData.name;
                if (requestData.address !== undefined) updateData.address = requestData.address;
                if (requestData.city !== undefined) updateData.city = requestData.city;
                if (requestData.postal_code !== undefined) updateData.postalCode = requestData.postal_code;
                if (requestData.country !== undefined) updateData.country = requestData.country;
                if (requestData.country_code !== undefined) updateData.countryCode = requestData.country_code;
                if (requestData.party_id !== undefined) updateData.partyId = requestData.party_id;
                if (requestData.publish !== undefined) updateData.publish = requestData.publish;
                if (requestData.publish_allowed_to !== undefined) updateData.publishAllowedTo = requestData.publish_allowed_to;
                if (requestData.directions !== undefined) updateData.directions = requestData.directions;
                if (requestData.facilities !== undefined) updateData.facilities = requestData.facilities;
                if (requestData.time_zone !== undefined) updateData.timeZone = requestData.time_zone;
                if (requestData.charging_when_closed !== undefined) updateData.chargingWhenClosed = requestData.charging_when_closed;

                // Opening Times aktualisieren, wenn vorhanden
                if (requestData.opening_times) {
                    updateData.openingTimes = {
                        twentyfourseven: requestData.opening_times.twentyfourseven,
                        regularHours: requestData.opening_times.regular_hours || [],
                        exceptionalOpenings: requestData.opening_times.exceptional_openings || [],
                        exceptionalClosings: requestData.opening_times.exceptional_closings || []
                    };
                }

                // Sichere Verarbeitung der Koordinaten
                if (requestData.coordinates !== undefined) {
                    // Prüfen, ob die Koordinaten vollständig sind
                    if (
                        typeof requestData.coordinates === 'object' &&
                        requestData.coordinates !== null &&
                        'latitude' in requestData.coordinates &&
                        'longitude' in requestData.coordinates
                    ) {
                        // Konvertieren und validieren der Koordinaten
                        const latitude = typeof requestData.coordinates.latitude === 'number'
                            ? requestData.coordinates.latitude
                            : parseFloat(String(requestData.coordinates.latitude));

                        const longitude = typeof requestData.coordinates.longitude === 'number'
                            ? requestData.coordinates.longitude
                            : parseFloat(String(requestData.coordinates.longitude));

                        // Prüfen, ob die Koordinaten gültige Zahlen sind
                        if (!isNaN(latitude) && !isNaN(longitude)) {
                            updateData.coordinates = {
                                latitude: latitude.toString(),
                                longitude: longitude.toString()
                            };
                        } else {
                            await logger.warn(`Ungültige Koordinaten für Location ${locationId}`, {
                                endpoint: 'locations',
                                action: 'PATCH',
                                updateType,
                                mandantId,
                                connectionId,
                                locationId,
                                coordinates: requestData.coordinates
                            });
                        }
                    } else {
                        await logger.warn(`Unvollständige Koordinaten für Location ${locationId}`, {
                            endpoint: 'locations',
                            action: 'PATCH',
                            updateType,
                            mandantId,
                            connectionId,
                            locationId,
                            coordinates: requestData.coordinates
                        });
                    }
                }

                // Location aktualisieren
                // Konvertiere updateData in das Format, das Strapi erwartet
                const strapiUpdateData = {
                    ...updateData,
                    // Wenn coordinates vorhanden ist, konvertiere es in das richtige Format
                    ...(updateData.coordinates ? {
                        coordinates: {
                            latitude: updateData.coordinates.latitude,
                            longitude: updateData.coordinates.longitude
                        }
                    } : {})
                };

                await strapi.documents('api::ocpi-location.ocpi-location').update({
                    documentId: existingLocation.documentId,
                    data: strapiUpdateData
                });

                await logger.info(`Location ${locationId} wurde teilweise aktualisiert`, {
                    endpoint: 'locations',
                    action: 'PATCH',
                    updateType,
                    mandantId,
                    connectionId,
                    locationId,
                    updatedFields: Object.keys(updateData).filter(key => key !== 'lastUpdated')
                });

                // EVSEs verarbeiten, wenn vorhanden
                if (requestData.evses && Array.isArray(requestData.evses)) {
                    const statusChanged = await this.processEvses(requestData.evses, existingLocation, connection, logger, true);

                    // Wenn sich der Status eines EVSEs geändert hat, aktualisiere den Terminal-Screen
                    if (statusChanged) {
                        try {
                            await updateTerminalScreenByLocationStatus(locationId);
                            await logger.info(`Terminal-Screen für Location ${locationId} aktualisiert aufgrund von EVSE-Statusänderung (Terminals im MAINTENANCE-Zustand werden nicht aktualisiert)`, {
                                endpoint: 'locations',
                                action: 'UPDATE_TERMINAL_SCREEN',
                                mandantId,
                                connectionId,
                                locationId
                            });
                        } catch (screenError) {
                            await logger.error(`Fehler beim Aktualisieren des Terminal-Screens für Location ${locationId}: ${screenError.message}`, {
                                endpoint: 'locations',
                                action: 'UPDATE_TERMINAL_SCREEN',
                                mandantId,
                                connectionId,
                                locationId,
                                error: {
                                    message: screenError.message,
                                    stack: screenError.stack
                                }
                            });
                        }
                    }
                }

                return ctx.send({
                    status_code: 1000,
                    status_message: "Success",
                    timestamp: new Date().toISOString()
                });
            }
        } catch (error) {
            // Detailliertes Error-Logging
            await logger.error(`Fehler beim teilweisen Aktualisieren (${updateType}): ${error.message}`, {
                endpoint: 'locations',
                action: 'PATCH',
                updateType,
                mandantId,
                connectionId,
                locationId,
                evseUid,
                connectorId,
                error: {
                    message: error.message,
                    stack: error.stack
                }
            });

            return ctx.send({
                status_code: 3000,
                status_message: `Server error: ${error.message}`,
                timestamp: new Date().toISOString()
            }, 500);
        }
    },

    /**
     * Hilfsmethode zum Verarbeiten von EVSEs
     * @param evses Array von EVSE-Objekten
     * @param location Die übergeordnete Location
     * @param connection Die OCPI-Verbindung
     * @param logger Der Logger-Service
     * @param isPatchRequest Gibt an, ob es sich um einen PATCH-Request handelt
     * @param specificEvseUid Spezifische EVSE-UID für PATCH-Requests (optional)
     * @returns {boolean} True, wenn sich der Status eines EVSEs geändert hat, sonst false
     */
    async processEvses(evses, location, connection, logger, isPatchRequest = false, specificEvseUid = null) {
        // Implementierung für EVSEs und Connectors
        // Alle EVSEs werden im ocpi-evse Model gespeichert

        const mandantId = connection.mandant?.id;
        let statusChanged = false;

        // Bestehende EVSEs für diese Location abrufen
        const existingEvses = await strapi.documents('api::ocpi-evse.ocpi-evse').findMany({
            filters: {
                location: {
                    documentId: location.documentId
                }
            }
        });

        // Map erstellen für schnellen Zugriff auf bestehende EVSEs nach uid
        const existingEvseMap = new Map();
        existingEvses.forEach(evse => {
            existingEvseMap.set(evse.uid, evse);
        });

        // Alle EVSEs aus dem Request verarbeiten
        for (const evseData of evses) {
            try {
                // Wenn eine spezifische EVSE-UID angegeben wurde und diese nicht mit der aktuellen EVSE übereinstimmt, überspringen
                if (specificEvseUid && evseData.uid !== specificEvseUid) {
                    continue;
                }

                // Prüfen, ob EVSE bereits existiert
                const existingEvse = existingEvseMap.get(evseData.uid);

                if (!existingEvse) {
                    // Bei PATCH-Requests keine neuen EVSEs erstellen, wenn sie nicht existieren
                    if (isPatchRequest) {
                        await logger.warn(`EVSE ${evseData.uid} existiert nicht und kann nicht mit PATCH erstellt werden`, {
                            endpoint: 'locations',
                            action: 'PATCH_EVSE',
                            mandantId,
                            connectionId: connection.id,
                            locationId: location.ocpiId,
                            evseUid: evseData.uid
                        });
                        continue;
                    }

                    // Bei PUT-Requests neue EVSE erstellen
                    const evseToCreate = {
                        uid: evseData.uid,
                        evseId: evseData.evse_id,
                        ocpiStatus: evseData.status,
                        capabilities: evseData.capabilities,
                        physicalReference: evseData.physical_reference,
                        parkingRestrictions: evseData.parking_restrictions,
                        lastUpdated: new Date(evseData.last_updated),
                        location: location.id,
                        // Hinzufügen des erforderlichen Feldes labelForTerminal
                        labelForTerminal: (evseData.physical_reference || evseData.evse_id || evseData.uid).slice(-12),
                        coordinates: {
                            latitude: '0.000000',
                            longitude: '0.000000'
                        },
                        // Connectors als JSON speichern
                        connectors: evseData.connectors ? evseData.connectors.map(connector => ({
                            id: connector.id,
                            standard: connector.standard,
                            format: connector.format,
                            powerType: connector.power_type,
                            maxVoltage: connector.max_voltage,
                            maxAmperage: connector.max_amperage,
                            maxElectricPower: connector.max_electric_power,
                            tariffIds: connector.tariff_ids || [],
                            lastUpdated: connector.last_updated
                        })) : []
                    };

                    // Koordinaten hinzufügen, wenn vorhanden
                    if (evseData.coordinates && evseData.coordinates.latitude && evseData.coordinates.longitude) {
                        evseToCreate.coordinates = {
                            latitude: evseData.coordinates.latitude,
                            longitude: evseData.coordinates.longitude
                        };
                    }

                    // Neues EVSE erstellen
                    await strapi.documents('api::ocpi-evse.ocpi-evse').create({
                        data: evseToCreate
                    });

                    await logger.info(`EVSE ${evseData.uid} erstellt`, {
                        endpoint: 'locations',
                        action: 'CREATE_EVSE',
                        mandantId,
                        connectionId: connection.id,
                        locationId: location.ocpiId,
                        evseUid: evseData.uid
                    });
                } else {
                    // EVSE aktualisieren
                    // Bei PATCH-Requests nur die übergebenen Attribute aktualisieren
                    if (isPatchRequest) {
                        const evseUpdateData: any = {
                            lastUpdated: new Date(evseData.last_updated)
                        };

                        // Nur die übergebenen Attribute aktualisieren
                        if (evseData.status !== undefined) {
                            // Prüfen, ob sich der Status geändert hat
                            if (existingEvse.ocpiStatus !== evseData.status) {
                                statusChanged = true;
                            }
                            evseUpdateData.ocpiStatus = evseData.status;
                        }
                        if (evseData.evse_id !== undefined) evseUpdateData.evseId = evseData.evse_id;
                        if (evseData.capabilities !== undefined) evseUpdateData.capabilities = evseData.capabilities;
                        if (evseData.physical_reference !== undefined) {
                            evseUpdateData.physicalReference = evseData.physical_reference;
                            evseUpdateData.labelForTerminal = (evseData.physical_reference || evseData.evse_id || evseData.uid).slice(0, 12);
                        }
                        if (evseData.parking_restrictions !== undefined) evseUpdateData.parkingRestrictions = evseData.parking_restrictions;

                        // Koordinaten aktualisieren, wenn vorhanden
                        if (evseData.coordinates && evseData.coordinates.latitude && evseData.coordinates.longitude) {
                            evseUpdateData.coordinates = {
                                latitude: evseData.coordinates.latitude,
                                longitude: evseData.coordinates.longitude
                            };
                        }

                        // Connectors aktualisieren, wenn vorhanden
                        if (evseData.connectors) {
                            evseUpdateData.connectors = evseData.connectors.map(connector => ({
                                id: connector.id,
                                standard: connector.standard,
                                format: connector.format,
                                powerType: connector.power_type,
                                maxVoltage: connector.max_voltage,
                                maxAmperage: connector.max_amperage,
                                maxElectricPower: connector.max_electric_power,
                                tariffIds: connector.tariff_ids || [],
                                lastUpdated: connector.last_updated
                            }));
                        }

                        // EVSE aktualisieren
                        await strapi.documents('api::ocpi-evse.ocpi-evse').update({
                            documentId: existingEvse.documentId,
                            data: evseUpdateData
                        });

                        await logger.info(`EVSE ${evseData.uid} teilweise aktualisiert`, {
                            endpoint: 'locations',
                            action: 'PATCH_EVSE',
                            mandantId,
                            connectionId: connection.id,
                            locationId: location.ocpiId,
                            evseUid: evseData.uid,
                            updatedFields: Object.keys(evseUpdateData).filter(key => key !== 'lastUpdated')
                        });
                    } else {
                        // Bei PUT-Requests alle Attribute aktualisieren
                        const evseToUpdate = {
                            uid: evseData.uid,
                            evseId: evseData.evse_id,
                            ocpiStatus: evseData.status,
                            capabilities: evseData.capabilities,
                            physicalReference: evseData.physical_reference,
                            parkingRestrictions: evseData.parking_restrictions,
                            lastUpdated: new Date(evseData.last_updated),
                            location: location.id,
                            // Hinzufügen des erforderlichen Feldes labelForTerminal
                            labelForTerminal: (evseData.physical_reference || evseData.evse_id || evseData.uid).slice(-12),
                            coordinates: {
                                latitude: '0.000000',
                                longitude: '0.000000'
                            },
                            // Connectors als JSON speichern
                            connectors: evseData.connectors ? evseData.connectors.map(connector => ({
                                id: connector.id,
                                standard: connector.standard,
                                format: connector.format,
                                powerType: connector.power_type,
                                maxVoltage: connector.max_voltage,
                                maxAmperage: connector.max_amperage,
                                maxElectricPower: connector.max_electric_power,
                                tariffIds: connector.tariff_ids || [],
                                lastUpdated: connector.last_updated
                            })) : []
                        };

                        // Koordinaten hinzufügen, wenn vorhanden
                        if (evseData.coordinates && evseData.coordinates.latitude && evseData.coordinates.longitude) {
                            evseToUpdate.coordinates = {
                                latitude: evseData.coordinates.latitude,
                                longitude: evseData.coordinates.longitude
                            };
                        }

                        // Prüfen, ob sich der Status geändert hat
                        if (existingEvse.ocpiStatus !== evseData.status) {
                            statusChanged = true;
                        }

                        // EVSE aktualisieren
                        await strapi.documents('api::ocpi-evse.ocpi-evse').update({
                            documentId: existingEvse.documentId,
                            data: evseToUpdate
                        });

                        await logger.info(`EVSE ${evseData.uid} aktualisiert`, {
                            endpoint: 'locations',
                            action: 'UPDATE_EVSE',
                            mandantId,
                            connectionId: connection.id,
                            locationId: location.ocpiId,
                            evseUid: evseData.uid
                        });
                    }
                }
            } catch (evseError) {
                await logger.error(`Fehler beim Verarbeiten von EVSE ${evseData.uid}: ${evseError.message}`, {
                    endpoint: 'locations',
                    action: 'PROCESS_EVSE',
                    mandantId,
                    connectionId: connection.id,
                    locationId: location.ocpiId,
                    evseUid: evseData.uid,
                    error: {
                        message: evseError.message,
                        stack: evseError.stack
                    }
                });
                // Fahre mit dem nächsten EVSE fort
            }
        }

        // Bei PATCH-Requests keine EVSEs löschen
        if (!isPatchRequest) {
            // Prüfen, ob EVSEs gelöscht wurden
            const currentEvseUids = new Set(evses.map(evse => evse.uid));
            for (const [uid, evse] of existingEvseMap.entries()) {
                if (!currentEvseUids.has(uid)) {
                    // EVSE wurde gelöscht
                    try {
                        // First find the document by filters
                        const document = await strapi.documents('api::ocpi-evse.ocpi-evse').findFirst({
                            filters: {
                                id: evse.id
                            }
                        });

                        // Then delete it using its documentId
                        if (document) {
                            await strapi.documents('api::ocpi-evse.ocpi-evse').delete({
                                documentId: document.documentId
                            });
                        }

                        await logger.info(`EVSE ${uid} gelöscht`, {
                            endpoint: 'locations',
                            action: 'DELETE_EVSE',
                            mandantId,
                            connectionId: connection.id,
                            locationId: location.ocpiId,
                            evseUid: uid
                        });
                    } catch (deleteError) {
                        await logger.error(`Fehler beim Löschen von EVSE ${uid}: ${deleteError.message}`, {
                            endpoint: 'locations',
                            action: 'DELETE_EVSE',
                            mandantId,
                            connectionId: connection.id,
                            locationId: location.ocpiId,
                            evseUid: uid,
                            error: {
                                message: deleteError.message,
                                stack: deleteError.stack
                            }
                        });
                    }
                }
            }
        }

        return statusChanged;
    }

};
