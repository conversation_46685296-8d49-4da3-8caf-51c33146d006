"use client";

import { format } from "date-fns";
import { de } from "date-fns/locale";
import type React from "react";
import { useCallback, useEffect, useState } from "react";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	FiColumns,
	FiFilter,
	FiPause,
	FiPlay,
	FiRefreshCw,
	FiX,
} from "react-icons/fi";
import { useLocalStorage } from "react-use";
import { useMandant } from "~/components/MandantContext";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Spinner } from "~/components/ui/spinner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useDebounce } from "~/hooks/useDebounce";
import {
	type OcpiCdrData,
	type OcpiSessionData,
	ocpiCdrApi,
	ocpiSessionApi,
} from "~/services/api";

const DEFAULT_CDR_COLUMNS = {
	cdrId: true,
	sessionId: true,
	totalCost: true,
	currency: true,
	timestamp: true,
	startDateTime: true,
	endDateTime: true,
	authMethod: true,
	mandant: true,
	totalEnergy: true,
	totalTime: true,
	totalFixedCost: false,
	totalEnergyCost: false,
	totalTimeCost: false,
	totalParkingCost: false,
};

const DEFAULT_SESSION_COLUMNS = {
	sessionId: true,
	startTime: true,
	endTime: true,
	kwh: true,
	totalCost: true,
	currency: true,
	ocpiStatus: true,
	locationId: true,
	evseUid: true,
	connectorId: true,
	authMethod: true,
	mandant: true,
};

const POLLING_INTERVAL = 30000; // 30 Sekunden

export default function SessionsPage() {
	const [activeTab, setActiveTab] = useState("cdrs");
	const [cdrs, setCdrs] = useState<OcpiCdrData[]>([]);
	const [sessions, setSessions] = useState<OcpiSessionData[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [isLive, setIsLive] = useState(false);
	const [showFilters, setShowFilters] = useState(false);
	const [showColumnSelector, setShowColumnSelector] = useState(false);

	// Filter-Zustände
	const [cdrFilters, setCdrFilters] = useState({
		search: "",
		startDate: "",
		endDate: "",
	});
	const [sessionFilters, setSessionFilters] = useState({
		search: "",
		startDate: "",
		endDate: "",
		status: "",
	});

	// Spalten-Einstellungen aus localStorage
	const [cdrColumns, setCdrColumns] = useLocalStorage(
		"ocpi-cdr-columns",
		DEFAULT_CDR_COLUMNS,
	);
	const [sessionColumns, setSessionColumns] = useLocalStorage(
		"ocpi-session-columns",
		DEFAULT_SESSION_COLUMNS,
	);

	// Mandant-Kontext
	const { activeMandant } = useMandant();

	// Debounced Suche
	const debouncedCdrSearch = useDebounce(cdrFilters.search, 500);
	const debouncedSessionSearch = useDebounce(sessionFilters.search, 500);

	// Daten laden mit Paginierung
	// CDRs laden
	const fetchCdrs = useCallback(
		async (resetPage = false) => {
			try {
				setIsLoading(true);
				setError(null);

				const currentPage = resetPage ? 1 : page;
				if (resetPage) {
					setPage(1);
					setCdrs([]);
				}

				// Filterbedingungen aufbauen
				const params: Record<string, any> = {
					sort: "timestamp:desc",
					"pagination[page]": currentPage,
					"pagination[pageSize]": 20,
					populate: "*",
				};

				// Suchfilter
				if (debouncedCdrSearch) {
					params["filters[$or][0][cdrId][$containsi]"] = debouncedCdrSearch;
					params["filters[$or][1][sessionId][$containsi]"] = debouncedCdrSearch;
					params["filters[$or][2][authorizationReference][$containsi]"] =
						debouncedCdrSearch;
				}

				// Datumsfilter
				if (cdrFilters.startDate) {
					params["filters[timestamp][$gte]"] = cdrFilters.startDate;
				}

				if (cdrFilters.endDate) {
					params["filters[timestamp][$lte]"] = cdrFilters.endDate;
				}

				// Mandantfilter
				if (activeMandant && activeMandant.documentId !== "root") {
					params["filters[mandant][documentId][$eq]"] =
						activeMandant.documentId;
				}

				const response = await ocpiCdrApi.getAll(params);

				if (resetPage) {
					setCdrs(response.data);
				} else {
					setCdrs((prev) => [...prev, ...response.data]);
				}

				// Prüfen, ob noch mehr Daten verfügbar sind
				setHasMore(
					response.meta.pagination.page < response.meta.pagination.pageCount,
				);

				if (currentPage === 1 && response.data.length === 0) {
					setHasMore(false);
				}
			} catch (err: any) {
				console.error("Fehler beim Laden der CDRs:", err);
				setError(`CDRs konnten nicht geladen werden. ${err.message || ""}`);
			} finally {
				setIsLoading(false);
			}
		},
		[page],
	); // Entferne Abhängigkeiten, die in useEffect bereits behandelt werden

	const fetchSessions = useCallback(
		async (resetPage = false) => {
			try {
				setIsLoading(true);
				setError(null);

				const currentPage = resetPage ? 1 : page;
				if (resetPage) {
					setPage(1);
					setSessions([]);
				}

				// Filterbedingungen aufbauen
				const params: Record<string, any> = {
					sort: "startTime:desc",
					"pagination[page]": currentPage,
					"pagination[pageSize]": 20,
					populate: "*",
				};

				// Suchfilter
				if (debouncedSessionSearch) {
					params["filters[$or][0][sessionId][$containsi]"] =
						debouncedSessionSearch;
					params["filters[$or][1][authorizationReference][$containsi]"] =
						debouncedSessionSearch;
					params["filters[$or][2][evseUid][$containsi]"] =
						debouncedSessionSearch;
				}

				// Datumsfilter
				if (sessionFilters.startDate) {
					params["filters[startTime][$gte]"] = sessionFilters.startDate;
				}

				if (sessionFilters.endDate) {
					params["filters[startTime][$lte]"] = sessionFilters.endDate;
				}

				// Status-Filter
				if (sessionFilters.status) {
					params["filters[ocpiStatus][$eq]"] = sessionFilters.status;
				}

				// Mandantfilter
				if (activeMandant && activeMandant.documentId !== "root") {
					params["filters[mandant][documentId][$eq]"] =
						activeMandant.documentId;
				}

				const response = await ocpiSessionApi.getAll(params);

				if (resetPage) {
					setSessions(response.data);
				} else {
					setSessions((prev) => [...prev, ...response.data]);
				}

				// Prüfen, ob noch mehr Daten verfügbar sind
				setHasMore(
					response.meta.pagination.page < response.meta.pagination.pageCount,
				);

				if (currentPage === 1 && response.data.length === 0) {
					setHasMore(false);
				}
			} catch (err: any) {
				console.error("Fehler beim Laden der Sessions:", err);
				setError(`Sessions konnten nicht geladen werden. ${err.message || ""}`);
			} finally {
				setIsLoading(false);
			}
		},
		[page],
	); // Entferne Abhängigkeiten, die in useEffect bereits behandelt werden

	// Effekt für das Laden der Daten bei Änderung des Tabs oder der Filter
	useEffect(() => {
		if (activeTab === "cdrs") {
			fetchCdrs(true);
		} else {
			fetchSessions(true);
		}
	}, [
		activeTab,
		fetchCdrs,
		fetchSessions,
		debouncedCdrSearch,
		debouncedSessionSearch,
		cdrFilters.startDate,
		cdrFilters.endDate,
		sessionFilters.startDate,
		sessionFilters.endDate,
		sessionFilters.status,
		activeMandant,
	]);

	// Effekt für Live-Polling
	useEffect(() => {
		let interval: NodeJS.Timeout | null = null;

		if (isLive) {
			interval = setInterval(() => {
				if (activeTab === "cdrs") {
					fetchCdrs(true);
				} else {
					fetchSessions(true);
				}
			}, POLLING_INTERVAL);
		}

		return () => {
			if (interval) clearInterval(interval);
		};
	}, [isLive, activeTab, fetchCdrs, fetchSessions]);

	// Infinite Scroll-Handler
	const handleScroll = useCallback(
		(e: React.UIEvent<HTMLDivElement>) => {
			const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

			if (scrollHeight - scrollTop <= clientHeight * 1.5) {
				if (!isLoading && hasMore) {
					setPage((prev) => prev + 1);
					if (activeTab === "cdrs") {
						fetchCdrs();
					} else {
						fetchSessions();
					}
				}
			}
		},
		[isLoading, hasMore, activeTab, fetchCdrs, fetchSessions],
	);

	// Filter zurücksetzen
	const resetFilters = () => {
		if (activeTab === "cdrs") {
			setCdrFilters({
				search: "",
				startDate: "",
				endDate: "",
			});
		} else {
			setSessionFilters({
				search: "",
				startDate: "",
				endDate: "",
				status: "",
			});
		}
	};

	// Spalten zurücksetzen
	const resetColumns = () => {
		if (activeTab === "cdrs") {
			setCdrColumns(DEFAULT_CDR_COLUMNS);
		} else {
			setSessionColumns(DEFAULT_SESSION_COLUMNS);
		}
	};

	// Format für Datum und Zeit
	const formatDateTime = (dateString: string | undefined) => {
		if (!dateString) return "-";
		return format(new Date(dateString), "dd.MM.yyyy HH:mm:ss", { locale: de });
	};

	// Format für Geldbeträge
	const formatCurrency = (amount: number | undefined, currency = "EUR") => {
		if (amount === undefined) return "-";
		return `${amount.toFixed(2)} ${currency}`;
	};

	return (
		<div className="space-y-4">
			<h1 className="mb-4 font-bold text-3xl">Ladesessions</h1>

			<div className="mb-2 flex justify-between">
				<Tabs
					defaultValue="cdrs"
					value={activeTab}
					onValueChange={setActiveTab}
					className="w-full"
				>
					<div className="mb-4 flex items-center justify-between">
						<TabsList>
							<TabsTrigger value="cdrs">CDRs</TabsTrigger>
							<TabsTrigger value="sessions">Sessions</TabsTrigger>
						</TabsList>

						<div className="flex space-x-2">
							<Button
								onClick={() => {
									activeTab === "cdrs" ? fetchCdrs(true) : fetchSessions(true);
								}}
								variant="outline"
								className="flex items-center"
								disabled={isLoading}
							>
								<FiRefreshCw
									className={`mr-2 ${isLoading ? "animate-spin" : ""}`}
								/>
								Aktualisieren
							</Button>

							<Button
								onClick={() => setShowFilters(!showFilters)}
								variant={showFilters ? "default" : "outline"}
								className="flex items-center"
							>
								<FiFilter className="mr-2" />
								Filter
							</Button>

							<Button
								onClick={() => setShowColumnSelector(!showColumnSelector)}
								variant={showColumnSelector ? "default" : "outline"}
								className="flex items-center"
							>
								<FiColumns className="mr-2" />
								Spalten
							</Button>

							<Button
								onClick={() => setIsLive(!isLive)}
								variant={isLive ? "default" : "outline"}
								className="flex items-center"
							>
								{isLive ? (
									<>
										<FiPause className="mr-2" />
										Live (aktiv)
									</>
								) : (
									<>
										<FiPlay className="mr-2" />
										Live
									</>
								)}
							</Button>
						</div>
					</div>

					{/* Filter-Bereich */}
					{showFilters && (
						<div className="mb-4 rounded-md border bg-gray-50 p-4">
							<div className="mb-2 flex items-center justify-between">
								<h3 className="font-semibold">Filter</h3>
								<Button onClick={resetFilters} variant="outline" size="sm">
									Filter zurücksetzen
								</Button>
							</div>

							{activeTab === "cdrs" && (
								<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
									<div>
										<Input
											placeholder="Suche nach CDR-ID, Session-ID oder Referenz"
											value={cdrFilters.search}
											onChange={(e) =>
												setCdrFilters({ ...cdrFilters, search: e.target.value })
											}
										/>
									</div>
									<div>
										<Input
											type="datetime-local"
											placeholder="Start-Datum"
											value={cdrFilters.startDate}
											onChange={(e) =>
												setCdrFilters({
													...cdrFilters,
													startDate: e.target.value,
												})
											}
										/>
									</div>
									<div>
										<Input
											type="datetime-local"
											placeholder="End-Datum"
											value={cdrFilters.endDate}
											onChange={(e) =>
												setCdrFilters({
													...cdrFilters,
													endDate: e.target.value,
												})
											}
										/>
									</div>
								</div>
							)}

							{activeTab === "sessions" && (
								<div className="grid grid-cols-1 gap-4 md:grid-cols-4">
									<div>
										<Input
											placeholder="Suche nach Session-ID, Referenz oder EVSE"
											value={sessionFilters.search}
											onChange={(e) =>
												setSessionFilters({
													...sessionFilters,
													search: e.target.value,
												})
											}
										/>
									</div>
									<div>
										<Input
											type="datetime-local"
											placeholder="Start-Datum"
											value={sessionFilters.startDate}
											onChange={(e) =>
												setSessionFilters({
													...sessionFilters,
													startDate: e.target.value,
												})
											}
										/>
									</div>
									<div>
										<Input
											type="datetime-local"
											placeholder="End-Datum"
											value={sessionFilters.endDate}
											onChange={(e) =>
												setSessionFilters({
													...sessionFilters,
													endDate: e.target.value,
												})
											}
										/>
									</div>
									<div>
										<select
											className="w-full rounded-md border p-2"
											value={sessionFilters.status}
											onChange={(e) =>
												setSessionFilters({
													...sessionFilters,
													status: e.target.value,
												})
											}
										>
											<option value="">Alle Status</option>
											<option value="ACTIVE">ACTIVE</option>
											<option value="COMPLETED">COMPLETED</option>
											<option value="INVALID">INVALID</option>
											<option value="PENDING">PENDING</option>
											<option value="RESERVATION">RESERVATION</option>
										</select>
									</div>
								</div>
							)}
						</div>
					)}

					{/* Spalten-Auswahl */}
					{showColumnSelector && (
						<div className="mb-4 rounded-md border bg-gray-50 p-4">
							<div className="mb-2 flex items-center justify-between">
								<h3 className="font-semibold">Spalten auswählen</h3>
								<Button onClick={resetColumns} variant="outline" size="sm">
									Standardspalten
								</Button>
							</div>

							{activeTab === "cdrs" && (
								<div className="grid grid-cols-2 gap-2 md:grid-cols-4 lg:grid-cols-6">
									{Object.keys(cdrColumns || DEFAULT_CDR_COLUMNS).map(
										(column) => (
											<div key={column} className="flex items-center space-x-2">
												<input
													type="checkbox"
													id={`cdr-column-${column}`}
													checked={
														cdrColumns?.[
															column as keyof typeof DEFAULT_CDR_COLUMNS
														] ?? true
													}
													onChange={() => {
														setCdrColumns((prev) => ({
															...(prev || DEFAULT_CDR_COLUMNS),
															[column]: !(
																prev?.[
																	column as keyof typeof DEFAULT_CDR_COLUMNS
																] ?? true
															),
														}));
													}}
												/>
												<label
													htmlFor={`cdr-column-${column}`}
													className="ml-2"
												>
													{column}
												</label>
											</div>
										),
									)}
								</div>
							)}

							{activeTab === "sessions" && (
								<div className="grid grid-cols-2 gap-2 md:grid-cols-4 lg:grid-cols-6">
									{Object.keys(sessionColumns || DEFAULT_SESSION_COLUMNS).map(
										(column) => (
											<div key={column} className="flex items-center space-x-2">
												<input
													type="checkbox"
													id={`session-column-${column}`}
													checked={
														sessionColumns?.[
															column as keyof typeof DEFAULT_SESSION_COLUMNS
														] ?? true
													}
													onChange={() => {
														setSessionColumns((prev) => ({
															...(prev || DEFAULT_SESSION_COLUMNS),
															[column]: !(
																prev?.[
																	column as keyof typeof DEFAULT_SESSION_COLUMNS
																] ?? true
															),
														}));
													}}
												/>
												<label
													htmlFor={`session-column-${column}`}
													className="ml-2"
												>
													{column}
												</label>
											</div>
										),
									)}
								</div>
							)}
						</div>
					)}

					{error && (
						<div className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
							{error}
						</div>
					)}

					<div
						className="overflow-auto"
						style={{ maxHeight: "70vh" }}
						onScroll={handleScroll}
					>
						<TabsContent value="cdrs" className="mt-0">
							<div className="overflow-x-auto rounded-lg border border-gray-200">
								<table className="min-w-full divide-y divide-gray-200">
									<thead className="sticky top-0 bg-gray-50">
										<tr>
											{cdrColumns?.cdrId && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													CDR ID
												</th>
											)}
											{cdrColumns?.sessionId && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Session ID
												</th>
											)}
											{cdrColumns?.timestamp && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Zeitpunkt
												</th>
											)}
											{cdrColumns?.startDateTime && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Start
												</th>
											)}
											{cdrColumns?.endDateTime && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Ende
												</th>
											)}
											{cdrColumns?.totalCost && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Gesamtkosten
												</th>
											)}
											{cdrColumns?.currency && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Währung
												</th>
											)}
											{cdrColumns?.totalEnergy && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Energie (kWh)
												</th>
											)}
											{cdrColumns?.totalTime && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Zeit (h)
												</th>
											)}
											{cdrColumns?.totalFixedCost && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Fixkosten
												</th>
											)}
											{cdrColumns?.totalEnergyCost && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Energiekosten
												</th>
											)}
											{cdrColumns?.totalTimeCost && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Zeitkosten
												</th>
											)}
											{cdrColumns?.totalParkingCost && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Parkkosten
												</th>
											)}
											{cdrColumns?.authMethod && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Auth-Methode
												</th>
											)}
											{cdrColumns?.mandant && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Mandant
												</th>
											)}
										</tr>
									</thead>
									<tbody className="divide-y divide-gray-200 bg-white">
										{cdrs.length === 0 ? (
											<tr>
												<td
													colSpan={
														Object.values(cdrColumns || {}).filter(Boolean)
															.length
													}
													className="px-6 py-4 text-center text-gray-500 text-sm"
												>
													{isLoading ? (
														<div className="flex justify-center py-8">
															<Spinner size="lg" />
														</div>
													) : (
														"Keine CDRs gefunden."
													)}
												</td>
											</tr>
										) : (
											cdrs.map((cdr) => (
												<tr key={cdr.documentId} className="hover:bg-gray-50">
													{cdrColumns?.cdrId && (
														<td className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 text-sm">
															{cdr.cdrId}
														</td>
													)}
													{cdrColumns?.sessionId && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{cdr.sessionId || "-"}
														</td>
													)}
													{cdrColumns?.timestamp && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatDateTime(cdr.timestamp)}
														</td>
													)}
													{cdrColumns?.startDateTime && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatDateTime(cdr.startDateTime)}
														</td>
													)}
													{cdrColumns?.endDateTime && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatDateTime(cdr.endDateTime)}
														</td>
													)}
													{cdrColumns?.totalCost && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatCurrency(cdr.totalCost, cdr.currency)}
														</td>
													)}
													{cdrColumns?.currency && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{cdr.currency}
														</td>
													)}
													{cdrColumns?.totalEnergy && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{cdr.totalEnergy?.toFixed(2) || "-"}
														</td>
													)}
													{cdrColumns?.totalTime && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{cdr.totalTime?.toFixed(2) || "-"}
														</td>
													)}
													{cdrColumns?.totalFixedCost && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatCurrency(cdr.totalFixedCost, cdr.currency)}
														</td>
													)}
													{cdrColumns?.totalEnergyCost && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatCurrency(
																cdr.totalEnergyCost,
																cdr.currency,
															)}
														</td>
													)}
													{cdrColumns?.totalTimeCost && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatCurrency(cdr.totalTimeCost, cdr.currency)}
														</td>
													)}
													{cdrColumns?.totalParkingCost && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatCurrency(
																cdr.totalParkingCost,
																cdr.currency,
															)}
														</td>
													)}
													{cdrColumns?.authMethod && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{cdr.authMethod || "-"}
														</td>
													)}
													{cdrColumns?.mandant && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{cdr.mandant?.data?.name || "-"}
														</td>
													)}
												</tr>
											))
										)}
									</tbody>
								</table>
							</div>
						</TabsContent>

						<TabsContent value="sessions" className="mt-0">
							<div className="overflow-x-auto rounded-lg border border-gray-200">
								<table className="min-w-full divide-y divide-gray-200">
									<thead className="sticky top-0 bg-gray-50">
										<tr>
											{sessionColumns?.sessionId && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Session ID
												</th>
											)}
											{sessionColumns?.startTime && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Start
												</th>
											)}
											{sessionColumns?.endTime && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Ende
												</th>
											)}
											{sessionColumns?.ocpiStatus && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Status
												</th>
											)}
											{sessionColumns?.kwh && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Energie (kWh)
												</th>
											)}
											{sessionColumns?.totalCost && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Gesamtkosten
												</th>
											)}
											{sessionColumns?.currency && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Währung
												</th>
											)}
											{sessionColumns?.locationId && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Location ID
												</th>
											)}
											{sessionColumns?.evseUid && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													EVSE UID
												</th>
											)}
											{sessionColumns?.connectorId && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Connector ID
												</th>
											)}
											{sessionColumns?.authMethod && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Auth-Methode
												</th>
											)}
											{sessionColumns?.mandant && (
												<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
													Mandant
												</th>
											)}
										</tr>
									</thead>
									<tbody className="divide-y divide-gray-200 bg-white">
										{sessions.length === 0 ? (
											<tr>
												<td
													colSpan={
														Object.values(sessionColumns || {}).filter(Boolean)
															.length
													}
													className="px-6 py-4 text-center text-gray-500 text-sm"
												>
													{isLoading ? (
														<div className="flex justify-center py-8">
															<Spinner size="lg" />
														</div>
													) : (
														"Keine Sessions gefunden."
													)}
												</td>
											</tr>
										) : (
											sessions.map((session) => (
												<tr
													key={session.documentId}
													className="hover:bg-gray-50"
												>
													{sessionColumns?.sessionId && (
														<td className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 text-sm">
															{session.sessionId}
														</td>
													)}
													{sessionColumns?.startTime && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatDateTime(session.startTime)}
														</td>
													)}
													{sessionColumns?.endTime && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatDateTime(session.endTime)}
														</td>
													)}
													{sessionColumns?.ocpiStatus && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															<span
																className={`rounded-full px-2 py-1 text-xs ${
																	session.ocpiStatus === "ACTIVE"
																		? "bg-green-100 text-green-800"
																		: session.ocpiStatus === "COMPLETED"
																			? "bg-blue-100 text-blue-800"
																			: session.ocpiStatus === "INVALID"
																				? "bg-red-100 text-red-800"
																				: session.ocpiStatus === "PENDING"
																					? "bg-yellow-100 text-yellow-800"
																					: "bg-gray-100 text-gray-800"
																}`}
															>
																{session.ocpiStatus}
															</span>
														</td>
													)}
													{sessionColumns?.kwh && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.kwh?.toFixed(2) || "-"}
														</td>
													)}
													{sessionColumns?.totalCost && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{formatCurrency(
																session.totalCost,
																session.currency,
															)}
														</td>
													)}
													{sessionColumns?.currency && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.currency}
														</td>
													)}
													{sessionColumns?.locationId && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.locationId || "-"}
														</td>
													)}
													{sessionColumns?.evseUid && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.evseUid || "-"}
														</td>
													)}
													{sessionColumns?.connectorId && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.connectorId || "-"}
														</td>
													)}
													{sessionColumns?.authMethod && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.authMethod || "-"}
														</td>
													)}
													{sessionColumns?.mandant && (
														<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
															{session.mandant?.data?.name || "-"}
														</td>
													)}
												</tr>
											))
										)}
									</tbody>
								</table>
							</div>
						</TabsContent>
					</div>

					{isLoading && hasMore && (
						<div className="flex justify-center py-4">
							<Spinner size="md" />
						</div>
					)}
				</Tabs>
			</div>
		</div>
	);
}
