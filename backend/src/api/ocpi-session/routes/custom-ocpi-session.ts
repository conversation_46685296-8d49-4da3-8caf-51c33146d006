/**
 * OCPI 2.2.1 Sessions-Modul Router
 */

// Keine Imports benötigt

// OCPI 2.2.1 Sessions-Modul Routen
const ocpiSessionsRoutes = {
    routes: [

        // OCPI 2.2.1 Sessions-Modul Routen
        {
            method: 'GET',
            path: '/ocpi/2.2.1/sessions',
            handler: 'custom-ocpi-session.getSessions',
            config: {
                auth: false, // Kein Auth-Check, wird im Controller gemacht
            },
        },
        {
            method: 'GET',
            path: '/ocpi/2.2.1/sessions/:sessionId',
            handler: 'custom-ocpi-session.getSessions',
            config: {
                auth: false, // Kein Auth-Check, wird im Controller gemacht
            },
        },
        {
            method: 'PUT',
            path: '/ocpi/2.2.1/sessions/:countryCode/:partyId/:sessionId',
            handler: 'custom-ocpi-session.updateSession',
            config: {
                auth: false, // Kein Auth-Check, wird im Controller gemacht
            },
        },
        {
            method: 'PATCH',
            path: '/ocpi/2.2.1/sessions/:countryCode/:partyId/:sessionId',
            handler: 'custom-ocpi-session.patchSession',
            config: {
                auth: false, // Kein Auth-Check, wird im Controller gemacht
            },
        },
    ],
};

export default ocpiSessionsRoutes;
