"use client";

import {
	<PERSON><PERSON><PERSON>ging,
	<PERSON>,
	Power,
	QrC<PERSON>,
	<PERSON>f<PERSON><PERSON><PERSON>,
	Trash,
	Wifi,
	WifiOff,
} from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";
import { apiClient } from "~/services/api";

interface Terminal {
	id: string;
	attributes: {
		serialNumber: string;
		terminalName: string;
		online: boolean;
		state: string;
		lastUpdate: string;
	};
	relationships: {
		mandant: {
			data: {
				id: string;
			};
		};
		location: {
			data: {
				id: string;
			};
		};
		evses: {
			data: Array<{
				id: string;
			}>;
		};
	};
}

interface PaymentSession {
	id: string;
	documentId: string;
	state: string;
	createdAt: string;
}

export default function TerminalDetailPage() {
	const { id } = useParams();
	const router = useRouter();
	const [terminal, setTerminal] = useState<Terminal | null>(null);
	const [activeSessions, setActiveSessions] = useState<PaymentSession[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchTerminal = async () => {
			try {
				setLoading(true);
				const response = await apiClient.get(`/api/terminals/${id}?populate=*`);
				setTerminal(response.data.data);

				// Aktive Sessions für das Terminal abrufen
				const sessionsResponse = await apiClient.get(
					`/api/payment-sessions?filters[terminal][id][$eq]=${id}&filters[state][$in][0]=started&filters[state][$in][1]=authorized&sort=createdAt:desc`,
				);

				if (sessionsResponse.data.data) {
					setActiveSessions(sessionsResponse.data.data);
				}
			} catch (err) {
				console.error("Error fetching terminal:", err);
				setError("Fehler beim Laden der Terminal-Daten.");
			} finally {
				setLoading(false);
			}
		};

		if (id) {
			fetchTerminal();
		}
	}, [id]);

	const handleInitTerminal = async () => {
		try {
			setLoading(true);
			await apiClient.post(
				`/api/payter/init?terminalId=${terminal?.attributes.serialNumber}`,
			);
			toast.success("Terminal erfolgreich initialisiert");
		} catch (err) {
			console.error("Error initializing terminal:", err);
			toast.error("Fehler beim Initialisieren des Terminals");
		} finally {
			setLoading(false);
		}
	};

	const handleEditTerminal = () => {
		// Hier könnte ein Modal zum Bearbeiten des Terminals geöffnet werden
		toast.info("Funktion noch nicht implementiert");
	};

	const handleDeleteTerminal = async () => {
		if (
			!window.confirm(
				"Sind Sie sicher, dass Sie dieses Terminal löschen möchten?",
			)
		) {
			return;
		}

		try {
			setLoading(true);
			await apiClient.delete(`/api/terminals/${id}`);
			toast.success("Terminal erfolgreich gelöscht");
			router.push("/terminals");
		} catch (err) {
			console.error("Error deleting terminal:", err);
			toast.error("Fehler beim Löschen des Terminals");
			setLoading(false);
		}
	};

	const renderStatus = (status: string, online: boolean) => {
		let statusClass = "bg-gray-100 text-gray-800";
		let statusText = status;

		if (online) {
			if (status === "IDLE") {
				statusClass = "bg-green-100 text-green-800";
			} else if (status === "BUSY") {
				statusClass = "bg-blue-100 text-blue-800";
			} else if (status === "ERROR") {
				statusClass = "bg-red-100 text-red-800";
			}
		} else {
			statusClass = "bg-red-100 text-red-800";
			statusText = "Offline";
		}

		return (
			<span
				className={`inline-flex items-center rounded-full px-3 py-1 font-medium text-sm ${statusClass}`}
			>
				{online ? (
					<Wifi className="mr-1 h-4 w-4" />
				) : (
					<WifiOff className="mr-1 h-4 w-4" />
				)}
				{statusText}
			</span>
		);
	};

	if (loading) {
		return (
			<div className="container py-6">
				<Skeleton className="mb-6 h-8 w-1/3" />
				<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
					<Skeleton className="h-64" />
					<Skeleton className="h-64" />
				</div>
			</div>
		);
	}

	if (error || !terminal) {
		return (
			<div className="container py-6">
				<Card>
					<CardHeader>
						<CardTitle>Fehler</CardTitle>
					</CardHeader>
					<CardContent>
						<p>{error || "Terminal nicht gefunden"}</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container py-6">
			<div className="mb-6 flex items-center justify-between">
				<h1 className="font-bold text-2xl">
					Terminal: {terminal.attributes.terminalName}
				</h1>
				<div className="flex space-x-2">
					<Button
						variant="outline"
						onClick={() => router.push(`/terminals/${id}/qrcode`)}
					>
						<QrCode className="mr-2 h-4 w-4" />
						QR-Code
					</Button>
					<Button variant="outline" onClick={handleInitTerminal}>
						<Power className="mr-2 h-4 w-4" />
						Initialisieren
					</Button>
					<Button variant="outline" onClick={() => window.location.reload()}>
						<RefreshCw className="mr-2 h-4 w-4" />
						Aktualisieren
					</Button>
					<Button variant="outline" onClick={handleEditTerminal}>
						<Edit className="mr-2 h-4 w-4" />
						Bearbeiten
					</Button>
					<Button variant="destructive" onClick={handleDeleteTerminal}>
						<Trash className="mr-2 h-4 w-4" />
						Löschen
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
				<Card>
					<CardHeader>
						<CardTitle>Terminal-Informationen</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="flex justify-between">
								<span className="font-medium">Status:</span>
								{renderStatus(
									terminal.attributes.state,
									terminal.attributes.online,
								)}
							</div>
							<div className="flex justify-between">
								<span className="font-medium">Seriennummer:</span>
								<span>{terminal.attributes.serialNumber}</span>
							</div>
							<div className="flex justify-between">
								<span className="font-medium">Letzte Aktualisierung:</span>
								<span>
									{terminal.attributes.lastUpdate
										? new Date(terminal.attributes.lastUpdate).toLocaleString(
												"de-DE",
											)
										: "Keine Daten"}
								</span>
							</div>
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Aktive Ladevorgänge</CardTitle>
					</CardHeader>
					<CardContent>
						{activeSessions.length > 0 ? (
							<div className="space-y-4">
								{activeSessions.map((session) => (
									<div
										key={session.id}
										className="flex items-center justify-between rounded-md border p-4"
									>
										<div className="flex items-center">
											<BatteryCharging className="mr-2 h-5 w-5 text-green-500" />
											<div>
												<p className="font-medium">
													Session ID: {session.documentId.substring(0, 8)}...
												</p>
												<p className="text-gray-500 text-sm">
													Gestartet:{" "}
													{new Date(session.createdAt).toLocaleString("de-DE")}
												</p>
											</div>
										</div>
										<Button
											variant="outline"
											size="sm"
											onClick={() =>
												router.push(`/invoice/${session.documentId}`)
											}
										>
											<QrCode className="mr-2 h-4 w-4" />
											QR-Code
										</Button>
									</div>
								))}
							</div>
						) : (
							<p className="py-8 text-center text-gray-500">
								Keine aktiven Ladevorgänge
							</p>
						)}
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
