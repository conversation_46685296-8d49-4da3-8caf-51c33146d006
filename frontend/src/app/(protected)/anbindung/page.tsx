"use client";

import React, { useState, useEffect } from "react";

import { FiPlus, FiRefreshCw } from "react-icons/fi";
import OcpiConnectionForm from "~/components/ocpi/OcpiConnectionForm";
import OcpiConnectionTable from "~/components/ocpi/OcpiConnectionTable";
import type { OcpiConnection } from "~/components/ocpi/OcpiConnectionTable";
import { Button } from "~/components/ui/button";
import { Spinner } from "~/components/ui/spinner";
import { apiClient } from "~/services/api";

export default function AnbindungPage() {
	const [connections, setConnections] = useState<OcpiConnection[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showForm, setShowForm] = useState(false);

	const loadConnections = async () => {
		setIsLoading(true);
		setError(null);

		try {
			const response = await apiClient.get("/api/ocpi-connections", {
				params: {
					"sort[0]": "name:asc",
					"pagination[pageSize]": 100,
					populate: "*", // Stelle sicher, dass alle Relationen geladen werden
				},
			});

			if (response.data?.data) {
				// Transformiere die Daten in das richtige Format
				const formattedConnections = response.data.data.map((conn: any) => ({
					documentId: conn.documentId,
					name: conn.name,
					ConnectionUrl: conn.connectionUrl,
					connectionStatus: conn.connectionStatus,
					lastConnection: conn.lastConnection,
					OCPIVersion: conn.ocpiVersion,
					Type: conn.type,
					role: conn.role,
					mandantId: conn.mandant?.data?.id,
					operatorId: conn.operatorId,
					partyId: conn.partyId,
					countryCode: conn.countryCode,
					companyName: conn.companyName,
				}));
				setConnections(formattedConnections);
			} else {
				setConnections([]);
			}
		} catch (err: any) {
			console.error("Fehler beim Laden der OCPI-Verbindungen:", err);
			setError(
				`Fehler beim Laden der OCPI-Verbindungen: ${err.message || "Unbekannter Fehler"}`,
			);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		loadConnections();
	}, []);

	const handleFormSuccess = () => {
		setShowForm(false);
		loadConnections();
	};

	return (
		<div className="container mx-auto py-8">
			<div className="mb-6 flex items-center justify-between">
				<h1 className="font-bold text-2xl">OCPI-Anbindungen</h1>
				<div className="flex space-x-3">
					<Button
						onClick={loadConnections}
						variant="outline"
						className="flex items-center"
						disabled={isLoading}
					>
						<FiRefreshCw
							className={`mr-2 ${isLoading ? "animate-spin" : ""}`}
						/>
						Aktualisieren
					</Button>
					<Button
						onClick={() => setShowForm(true)}
						variant="outline"
						className="flex items-center"
						disabled={isLoading || showForm}
					>
						<FiPlus className="mr-2" />
						Neue Verbindung
					</Button>
				</div>
			</div>

			{error && (
				<div className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
					{error}
				</div>
			)}

			{showForm && (
				<div className="mb-6">
					<OcpiConnectionForm
						onSuccess={handleFormSuccess}
						onCancel={() => setShowForm(false)}
					/>
				</div>
			)}

			{isLoading ? (
				<div className="flex items-center justify-center py-12">
					<Spinner size="lg" />
				</div>
			) : (
				<OcpiConnectionTable
					connections={connections}
					onRefresh={loadConnections}
				/>
			)}
		</div>
	);
}
