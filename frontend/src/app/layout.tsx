// frontend/src/app/layout.tsx
import "~/styles/globals.css";
import "react-toastify/dist/ReactToastify.css";
import type { Metadata } from "next";
import { Montserrat, Source_Sans_3 } from "next/font/google";
import { ToastContainer } from "react-toastify";
import { AuthProvider } from "~/components/AuthContext";

export const metadata: Metadata = {
	title: "EulektroTerminalVerwaltung",
	description: "Verwaltungssystem für Elektro-Ladestationen",
	icons: [{ rel: "icon", url: "/favicon.ico" }],
};

const sourceSans = Source_Sans_3({
	subsets: ["latin"],
	variable: "--font-source-sans",
});

const montserrat = Montserrat({
	subsets: ["latin"],
	variable: "--font-montserrat",
});

export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="de" className={`${sourceSans.variable} ${montserrat.variable}`}>
			<body>
				<AuthProvider>{children}</AuthProvider>
				<ToastContainer
					position="top-right"
					autoClose={5000}
					hideProgressBar={false}
					newestOnTop
					closeOnClick
					rtl={false}
					pauseOnFocusLoss
					draggable
					pauseOnHover
					theme="light"
				/>
			</body>
		</html>
	);
}
