import { randomBytes } from 'crypto';

export default {
    async beforeCreate(event) {
        const { data } = event.params;
        // Falls noch kein InitialSecret gesetzt wurde, generiere einen neuen
        if (!data.InitialSecret) {
            data.InitialSecret = randomBytes(16).toString('hex');
        }
        if (!data.ReceivingSecret) {
            data.ReceivingSecret = randomBytes(16).toString('hex');
        }
    },
};
