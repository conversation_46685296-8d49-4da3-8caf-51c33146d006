/**
 * terminal-message-log controller
 */

import { factories } from '@strapi/strapi';

// Typendefinitionen für den Benutzer mit Mandant
interface Mandant {
  documentId: number;
  [key: string]: any;
}

interface User {
  id: number;
  username: string;
  email: string;
  mandant?: Mandant;
  [key: string]: any;
}

interface StrapiState {
  user?: User;
  [key: string]: any;
}

// Typendefinitionen für die Query-Parameter
interface FilterValue {
  $eq?: any;
  $ne?: any;
  $lt?: any;
  $lte?: any;
  $gt?: any;
  $gte?: any;
  $in?: any[];
  $notIn?: any[];
  $contains?: string;
  $notContains?: string;
  $containsi?: string;
  $notContainsi?: string;
  $null?: boolean;
  $notNull?: boolean;
  $between?: [any, any];
  [key: string]: any;
}

interface QueryFilters {
  [key: string]: any | FilterValue;
  mandant?: any | FilterValue;
  'mandant.documentId'?: number | FilterValue;
}

interface QueryPopulate {
  [key: string]: any;
  mandant?: any;
}

// Spezielle Typen für Strapi 5 EntityService
type StrapiSort = string[] | string | Record<string, 'asc' | 'desc'> | any;

interface StrapiQuery {
  [key: string]: any;
  filters?: QueryFilters;
  populate?: QueryPopulate;
  pagination?: {
    page?: number;
    pageSize?: number;
    withCount?: boolean;
  };
  sort?: StrapiSort;
}

// Hilfsfunktion zum Erstellen eines Log-Eintrags
async function createLogEntry(strapi, data: {
  terminalId: string;
  direction: string;
  payload?: any;
  messageType?: string;
  paymentSessionId?: string | null;
  mandantId?: number | null;
  responsePayload?: any;
}) {
  try {
    let { terminalId, direction, payload = null, messageType = 'info', paymentSessionId = null, mandantId = null, responsePayload = null } = data;

    // Stelle sicher, dass direction einen gültigen Wert hat
    const validDirections = ['ServerToTerminal', 'TerminalToServer', 'WebToServer', 'ServerToWeb', 'Unknown'];
    direction = validDirections.includes(direction) ? direction : 'Unknown';

    // Erstelle das Log-Eintrag-Objekt
    const logEntry: Record<string, any> = {
      terminalId,
      direction,
      messageType,
      payload: payload ? JSON.stringify(payload) : null,
      paymentSessionId,
      responsePayload: responsePayload ? JSON.stringify(responsePayload) : null
    };

    // Füge Mandant nur hinzu, wenn mandantId vorhanden ist
    if (mandantId) {
      logEntry.mandant = mandantId;
    }

    const result = await strapi.entityService.create('api::terminal-message-log.terminal-message-log', {
      data: logEntry,
    });

    return result;
  } catch (error) {
    console.error('Fehler beim Erstellen des Terminal-Message-Logs:', error);
    return null;
  }
}

export default factories.createCoreController('api::terminal-message-log.terminal-message-log', ({ strapi }) => ({


  // Endpunkt zum Loggen von Server-zu-Terminal-Nachrichten
  async logToTerminal(ctx) {
    try {
      const { terminalId, payload, mandantId, paymentSessionId, messageType = 'info', responsePayload = null } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID is required');
      }

      const result = await createLogEntry(strapi, {
        terminalId,
        direction: 'ServerToTerminal',
        payload,
        messageType,
        paymentSessionId,
        mandantId,
        responsePayload,
      });

      // Sicherer Zugriff auf die ID
      const logId = result && typeof result === 'object' && 'documentId' in result ? result.documentId : null;
      return { success: true, id: logId };
    } catch (error) {
      console.error('Error logging terminal message:', error);
      return ctx.badRequest('Failed to log terminal message', { error: error.message });
    }
  },

  // Endpunkt zum Loggen von Terminal-zu-Server-Nachrichten
  async logFromTerminal(ctx) {
    try {
      const { terminalId, payload, mandantId, paymentSessionId, messageType = 'info' } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID is required');
      }

      const result = await createLogEntry(strapi, {
        terminalId,
        direction: 'TerminalToServer',
        payload,
        messageType,
        paymentSessionId,
        mandantId
      });

      // Sicherer Zugriff auf die ID
      const logId = result && typeof result === 'object' && 'documentId' in result ? result.documentId : null;
      return { success: true, id: logId };
    } catch (error) {
      console.error('Error logging terminal message:', error);
      return ctx.badRequest('Failed to log terminal message', { error: error.message });
    }
  },

  // Endpunkt zum Loggen von Fehlern
  async logError(ctx) {
    try {
      const { terminalId, payload, mandantId, paymentSessionId } = ctx.request.body;

      if (!terminalId) {
        return ctx.badRequest('Terminal ID is required');
      }

      const result = await createLogEntry(strapi, {
        terminalId,
        direction: 'TerminalToServer',
        payload,
        messageType: 'error',
        paymentSessionId,
        mandantId
      });

      // Sicherer Zugriff auf die ID
      const logId = result && typeof result === 'object' && 'documentId' in result ? result.documentId : null;
      return { success: true, id: logId };
    } catch (error) {
      console.error('Error logging terminal error:', error);
      return ctx.badRequest('Failed to log terminal error', { error: error.message });
    }
  },

  // Endpoint zum Zählen aller Terminal-Nachrichten
  async count(ctx) {
    try {
      // Sicherstellen, dass ctx.query ein Objekt ist mit korrektem Typ
      const query = (ctx.query || {}) as StrapiQuery;

      // Filters initialisieren, falls nicht vorhanden
      if (!query.filters) {
        query.filters = {} as QueryFilters;
      }

      // Wenn ein spezifischer Mandanten-Filter gesetzt ist, verwenden wir diesen
      const filters = query.filters as QueryFilters;
      const hasMandantFilter = filters && (
        (typeof filters.mandant !== 'undefined') ||
        (typeof filters['mandant.documentId'] !== 'undefined')
      );

      // Mandantenfilter basierend auf dem angemeldeten Benutzer
      const state = ctx.state as StrapiState;
      if (!hasMandantFilter && state?.user?.mandant) {
        const userMandantId = state.user.mandant.documentId;
        filters['mandant.documentId'] = userMandantId;
        console.log(`Terminal-Message-Log count: Filtering for mandant ${userMandantId}`);
      }

      // Debug-Ausgabe
      console.log('Terminal-Message-Log Count Query:', JSON.stringify(query.filters, null, 2));

      // Anzahl ermitteln
      const count = await strapi.entityService.count(
        'api::terminal-message-log.terminal-message-log',
        { filters: query.filters }
      );

      return { count };
    } catch (error) {
      console.error('Error counting terminal message logs:', error);
      return ctx.badRequest('Failed to count terminal message logs', { error: error.message });
    }
  },

  // Endpoint zum Abrufen aller eindeutigen Terminal-IDs
  async getTerminalIds(ctx) {
    try {
      // Query für die Ermittlung eindeutiger Terminal-IDs
      // Da groupBy nicht unterstützt wird, müssen wir alle Einträge abrufen und dann manuell gruppieren
      const result = await strapi.db.query('api::terminal-message-log.terminal-message-log').findMany({
        select: ['terminalId'],
        orderBy: { terminalId: 'asc' }
      });

      // Manuelles Gruppieren der Terminal-IDs
      const uniqueTerminalIds = new Set();

      // Extraktion der eindeutigen Terminal-IDs aus dem Ergebnis
      result.forEach(item => {
        if (item.terminalId) {
          uniqueTerminalIds.add(item.terminalId);
        }
      });

      // Konvertiere das Set in ein Array
      const terminalIds = Array.from(uniqueTerminalIds) as string[];

      return terminalIds;
    } catch (error) {
      console.error('Error retrieving terminal IDs:', error);
      return ctx.badRequest('Failed to retrieve terminal IDs', { error: error.message });
    }
  },
  // Überschreiben der find-Methode mit entityService
  async find(ctx) {
    try {
      // Sicherstellen, dass ctx.query ein Objekt ist mit korrektem Typ
      const query = (ctx.query || {}) as StrapiQuery;

      // Defaults für Paginierung setzen, falls nicht vorhanden
      if (!query.pagination) {
        query.pagination = {
          page: 1,
          pageSize: 25,
          withCount: true
        };
      } else {
        // Sicherstellen, dass page und pageSize vorhanden sind
        query.pagination.page = query.pagination.page || 1;
        query.pagination.pageSize = query.pagination.pageSize || 25;
        query.pagination.withCount = query.pagination.withCount !== false;
      }

      // Filters initialisieren, falls nicht vorhanden
      if (!query.filters) {
        query.filters = {} as QueryFilters;
      }

      // Population der Mandanten aktivieren
      if (!query.populate) {
        query.populate = { mandant: true } as QueryPopulate;
      } else if (typeof query.populate === 'object') {
        (query.populate as QueryPopulate).mandant = true;
      }

      // Wenn ein spezifischer Mandanten-Filter gesetzt ist, verwenden wir diesen
      const filters = query.filters as QueryFilters;
      const hasMandantFilter = filters && (
        (typeof filters.mandant !== 'undefined') ||
        (typeof filters['mandant.documentId'] !== 'undefined')
      );

      // Wenn kein spezifischer Mandanten-Filter gesetzt ist und der Benutzer authentifiziert ist
      const state = ctx.state as StrapiState;
      if (!hasMandantFilter && state?.user?.mandant) {
        const userMandantId = state.user.mandant.documentId;

        // Füge Mandanten-Filter hinzu, um nur Logs für den Mandanten des Benutzers anzuzeigen
        filters['mandant.documentId'] = userMandantId;

        console.log(`Terminal-Message-Log Controller: Filtering logs for mandant ${userMandantId}`);
      }

      // Standard-Sortierung hinzufügen, falls nicht vorhanden
      if (!query.sort) {
        query.sort = { createdAt: 'desc' };
      }

      // Debug-Ausgabe
      console.log('Terminal-Message-Log Query:', JSON.stringify(query, null, 2));

      // EntityService-Abfrage vorbereiten
      const findOptions = {
        filters: query.filters,
        populate: query.populate,
        sort: query.sort as any, // Type casting für Strapi 5 Kompatibilität
        page: query.pagination.page,
        pageSize: query.pagination.pageSize
      };

      // EntityService-Abfrage durchführen
      const result = await strapi.entityService.findPage('api::terminal-message-log.terminal-message-log', findOptions);
      const { results, pagination } = result;

      // Die Ergebnisse in das vom Frontend erwartete Format umwandeln
      const formattedResults = results.map(item => {
        // TypeScript-sicheres Casting
        const typedItem = item as any;
        const mandant = typedItem.mandant;

        return {
          id: typedItem.documentId,
          attributes: {
            ...typedItem,
            // Mandant speziell formatieren, falls vorhanden
            mandant: mandant
              ? {
                  data: {
                    id: mandant.documentId,
                    attributes: {
                      ...mandant,
                      documentId: mandant.documentId
                    }
                  }
                }
              : null
          }
        };
      });

      // Gesamtanzahl der Datensätze abrufen, falls nicht über findPage verfügbar
      const total = pagination?.total || await strapi.entityService.count(
        'api::terminal-message-log.terminal-message-log',
        { filters: query.filters }
      );

      // Paginierungsinformation berechnen
      const page = query.pagination.page;
      const pageSize = query.pagination.pageSize;
      const pageCount = Math.ceil(total / pageSize);

      // Das Antwortformat an das vom Frontend erwartete Format anpassen
      return {
        data: formattedResults,
        meta: {
          pagination: {
            page,
            pageSize,
            pageCount,
            total
          }
        }
      };
    } catch (error) {
      console.error('Error in terminal-message-log find method:', error);
      return ctx.badRequest('Failed to retrieve terminal message logs', { error: error.message });
    }
  },

  // Überschreiben der findOne-Methode mit entityService
  async findOne(ctx) {
    try {
      const { id } = ctx.params;

      if (!id) {
        return ctx.badRequest('ID is required');
      }

      // Query-Parameter vorbereiten
      const query = (ctx.query || {}) as StrapiQuery;

      // Population der Mandanten aktivieren
      if (!query.populate) {
        query.populate = { mandant: true } as QueryPopulate;
      } else if (typeof query.populate === 'object') {
        (query.populate as QueryPopulate).mandant = true;
      }

      // Debug-Ausgabe
      console.log('Terminal-Message-Log findOne Query:', JSON.stringify(query, null, 2));

      // Datensatz abrufen
      const item = await strapi.entityService.findOne(
        'api::terminal-message-log.terminal-message-log',
        id,
        { populate: query.populate }
      );

      if (!item) {
        return ctx.notFound('Terminal message log not found');
      }

      // Typisiertes Casting für das Item mit Mandant
      const typedItem = item as any;
      const mandant = typedItem.mandant;

      // Prüfen, ob der Benutzer auf diesen Log zugreifen darf
      const state = ctx.state as StrapiState;
      if (mandant?.documentId && state?.user?.mandant) {
        const userMandantId = state.user.mandant.documentId;
        const logMandantId = mandant.documentId;

        if (logMandantId !== userMandantId) {
          return ctx.forbidden('You do not have access to this log entry');
        }
      }

      // Das Ergebnis in das vom Frontend erwartete Format umwandeln
      const formattedResult = {
        id: typedItem.documentId,
        attributes: {
          ...typedItem,
          // Mandant speziell formatieren, falls vorhanden
          mandant: mandant
            ? {
                data: {
                  id: mandant.documentId,
                  attributes: {
                    ...mandant,
                    documentId: mandant.documentId
                  }
                }
              }
            : null
        }
      };

      // Das Antwortformat an das vom Frontend erwartete Format anpassen
      return {
        data: formattedResult,
        meta: {}
      };
    } catch (error) {
      console.error('Error in terminal-message-log findOne method:', error);
      return ctx.badRequest('Failed to retrieve terminal message log', { error: error.message });
    }
  }
}));
