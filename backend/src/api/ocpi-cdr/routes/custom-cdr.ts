export default  {
    routes: [
        // OCPI 2.2.1 CDRs-Modul Routen
        {
            method: 'GET',
            path: '/ocpi/2.2.1/cdrs',
            handler: 'custom-ocpi-cdr.getCdrs',
            config: {
                auth: false, // Kein <PERSON>th-<PERSON>, wird im Controller gemacht
            },
        },
        {
            method: 'GET',
            path: '/ocpi/2.2.1/cdrs/:cdrId',
            handler: 'custom-ocpi-cdr.getCdrs',
            config: {
                auth: false, // Kein Auth-Check, wird im Controller gemacht
            },
        },
        {
            method: 'POST',
            path: '/ocpi/2.2.1/cdrs',
            handler: 'custom-ocpi-cdr.createCdr',
            config: {
                auth: false, // Kein Auth-Check, wird im Controller gemacht^
            },
        },
    ]
}
