{"kind": "collectionType", "collectionName": "invoices", "info": {"singularName": "invoice", "pluralName": "invoices", "displayName": "Invoice", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"kindOfInvoice": {"type": "enumeration", "enum": ["INVOICE", "CREDIT", "STORNO", "CREDIT_STORNO"], "default": "INVOICE", "required": true}, "invoiceId": {"type": "integer"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant"}, "ocpi_cdr": {"type": "relation", "relation": "oneToOne", "target": "api::ocpi-cdr.ocpi-cdr"}, "ocpi_session": {"type": "relation", "relation": "oneToOne", "target": "api::ocpi-session.ocpi-session"}, "payment_session": {"type": "relation", "relation": "oneToOne", "target": "api::payment-session.payment-session", "mappedBy": "invoice"}, "sum_net": {"type": "float", "required": true, "comment": "Sum of all net amounts"}, "sum_gross": {"type": "float", "required": true, "comment": "sum_net inkl. VAT"}, "vat_percentage": {"type": "float", "min": 0, "max": 100, "default": 19, "required": true}, "vat_amount": {"type": "float", "required": true, "comment": "amount added to sum_net to get sum_gross"}, "metadata": {"type": "json", "default": {}}, "invoice_number": {"type": "string"}, "period_start_utc": {"type": "datetime", "required": true}, "period_end_utc": {"type": "datetime", "required": true}, "invoice_status": {"type": "enumeration", "enum": ["DRAFT", "INMUTABLE_WRITTEN", "PAID"], "default": "DRAFT", "required": true}, "invoice_positions": {"type": "component", "repeatable": true, "component": "invoice-position.invoice-position"}, "file": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["files"]}, "file_path": {"type": "string", "unique": true}}}