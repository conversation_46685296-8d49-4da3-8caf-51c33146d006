import type React from "react";
import { useState } from "react";
import { toast } from "react-toastify";
import { useMandant } from "~/components/MandantContext";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { cn } from "~/lib/utils";
import { apiClient } from "~/services/api";

interface OcpiConnectionFormProps {
	onSuccess: () => void;
	onCancel: () => void;
}

const OcpiConnectionForm: React.FC<OcpiConnectionFormProps> = ({
	onSuccess,
	onCancel,
}) => {
	// Mandant aus dem Kontext holen
	const { activeMandant } = useMandant();

	const [name, setName] = useState("");
	const [url, setUrl] = useState("");
	const [initialSecret, setInitialSecret] = useState("");
	const [type, setType] = useState<"Prod" | "Test" | "Dev">("Prod");
	const [partyId, setPartyId] = useState("EUL"); // Default: EUL
	const [countryCode, setCountryCode] = useState("DE"); // Default: DE
	const [companyName, setCompanyName] = useState("Eulektro GmbH"); // Default: Eulektro GmbH
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsLoading(true);
		setError(null);

		try {
			// Validierung
			if (!name.trim()) {
				throw new Error("Name ist erforderlich");
			}

			if (!url.trim()) {
				throw new Error("URL ist erforderlich");
			}

			if (!initialSecret.trim()) {
				throw new Error("Initial Secret ist erforderlich");
			}

			if (!partyId.trim()) {
				throw new Error("Party ID ist erforderlich");
			}

			if (!countryCode.trim()) {
				throw new Error("Ländercode ist erforderlich");
			}

			if (!companyName.trim()) {
				throw new Error("Firmenname ist erforderlich");
			}

			// Prüfen, ob ein aktiver Mandant vorhanden ist
			if (!activeMandant) {
				throw new Error("Kein aktiver Mandant ausgewählt");
			}

			// API-Aufruf zum Erstellen der OCPI-Verbindung
			const response = await apiClient.post("/api/ocpi-connections", {
				data: {
					name: name,
					connectionUrl: url,
					initialSecret: initialSecret,
					connectionStatus: "inactive",
					type: type, // Ausgewählter Typ: Prod, Test oder Dev
					role: "sender", // Standard-Rolle: sender oder receiver
					mandants: {
						connect: [{ documentId: activeMandant.documentId }],
					}, // Mandanten-ID hinzufügen
					partyId: partyId,
					countryCode: countryCode,
					companyName: companyName,
				},
			});

			if (response.status === 201) {
				toast.success("OCPI-Verbindung erfolgreich erstellt");
				onSuccess();
			} else {
				throw new Error("Fehler beim Erstellen der OCPI-Verbindung");
			}
		} catch (err: any) {
			const errorMessage =
				err.message || "Ein unbekannter Fehler ist aufgetreten";
			setError(errorMessage);
			toast.error(errorMessage);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="rounded-lg bg-white p-6 shadow-md">
			<h2 className="mb-4 font-semibold text-xl">Neue OCPI-Verbindung</h2>

			{error && (
				<div className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
					{error}
				</div>
			)}

			<form onSubmit={handleSubmit}>
				<div className="mb-4">
					<Label htmlFor="name">Name</Label>
					<Input
						id="name"
						value={name}
						onChange={(e) => setName(e.target.value)}
						placeholder="Name der OCPI-Verbindung"
						required
					/>
				</div>

				<div className="mb-4">
					<Label htmlFor="url">URL</Label>
					<Input
						id="url"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
						placeholder="https://example.com/ocpi"
						required
					/>
				</div>

				<div className="mb-4">
					<Label htmlFor="initialSecret">Initial Secret</Label>
					<Input
						id="initialSecret"
						value={initialSecret}
						onChange={(e) => setInitialSecret(e.target.value)}
						placeholder="Initial Secret"
						required
					/>
				</div>

				<div className="mb-4">
					<Label htmlFor="partyId">Party ID</Label>
					<Input
						id="partyId"
						value={partyId}
						onChange={(e) => setPartyId(e.target.value)}
						placeholder="EUL"
						required
					/>
				</div>

				<div className="mb-4">
					<Label htmlFor="countryCode">Ländercode</Label>
					<Input
						id="countryCode"
						value={countryCode}
						onChange={(e) => setCountryCode(e.target.value.toUpperCase())}
						placeholder="DE"
						maxLength={2}
						required
					/>
				</div>

				<div className="mb-4">
					<Label htmlFor="companyName">Firmenname</Label>
					<Input
						id="companyName"
						value={companyName}
						onChange={(e) => setCompanyName(e.target.value)}
						placeholder="Eulektro GmbH"
						required
					/>
				</div>

				<div className="mb-6">
					<Label htmlFor="type">Verbindungstyp</Label>
					<div className="mt-2 flex space-x-4">
						{(["Prod", "Test", "Dev"] as const).map((typeOption) => (
							<label
								key={typeOption}
								className="flex cursor-pointer items-center space-x-2"
							>
								<input
									type="radio"
									name="type"
									value={typeOption}
									checked={type === typeOption}
									onChange={() => setType(typeOption)}
									className="h-4 w-4 cursor-pointer text-[rgb(33,105,124)] focus:ring-[rgb(33,105,124)]"
								/>
								<span
									className={cn(
										"text-sm",
										type === typeOption
											? "font-medium text-[rgb(33,105,124)]"
											: "text-gray-700",
									)}
								>
									{typeOption}
								</span>
							</label>
						))}
					</div>
				</div>

				<div className="flex justify-end space-x-3">
					<Button
						type="button"
						onClick={onCancel}
						variant="outline"
						disabled={isLoading}
					>
						Abbrechen
					</Button>
					<Button type="submit" disabled={isLoading}>
						{isLoading ? "Wird gespeichert..." : "Speichern"}
					</Button>
				</div>
			</form>
		</div>
	);
};

export default OcpiConnectionForm;
