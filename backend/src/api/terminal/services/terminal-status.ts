/**
 * Terminal Status Service
 * Verwaltet den Status der Terminals basierend auf dem Status der zugehörigen EVSEs
 */

import { displayScreen } from "../../payter/controllers/terminal/uiActions";

/**
 * Prüft den Status aller EVSEs an einer Location und aktualisiert den Terminal-Screen entsprechend
 * @param locationId Die ID der Location (ocpiId)
 */
export async function updateTerminalScreenByLocationStatus(locationId: string): Promise<void> {
  try {
    // Finde die Location anhand der ocpiId
    const location = await strapi.documents('api::ocpi-location.ocpi-location').findFirst({
      filters: {
        ocpiId: locationId
      },
      populate: {
        terminals: {
          populate: {
            payter_connection: true
          }
        },
        evses: true
      }
    });

    if (!location) {
      console.error(`Location mit ocpiId ${locationId} nicht gefunden`);
      return;
    }

    // Prüfe, ob die Location Terminals hat
    if (!location.terminals || location.terminals.length === 0) {
      console.log(`Location ${locationId} hat keine Terminals`);
      return;
    }

    // Prüfe den Status aller EVSEs
    const evses = location.evses || [];
    const hasAvailableOrChargingEvse = evses.some(evse =>
      evse.ocpiStatus === 'AVAILABLE' || evse.ocpiStatus === 'CHARGING'
    );

    // Aktualisiere den Screen für jedes Terminal
    for (const terminal of location.terminals) {
      try {
        // Prüfe, ob das Terminal im MAINTENANCE-Zustand ist
        if (terminal.terminalState === 'MAINTENANCE') {
          console.log(`Terminal ${terminal.serialNumber} ist im MAINTENANCE-Zustand und wird nicht automatisch aktualisiert`);
          continue; // Überspringe dieses Terminal
        }

        // Erstelle den Kontext für die displayScreen-Funktion
        const ctx = {
          state: {
            terminal: terminal
          }
        };

        // Hole den API-Client für das Terminal
        const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

        if (!apiClient || !callback.ui) {
          console.error(`API-Client für Terminal ${terminal.serialNumber} konnte nicht initialisiert werden`);
          continue;
        }

        // Zeige den entsprechenden Screen an
        const screenId = hasAvailableOrChargingEvse ? 'screen-init' : 'screen-out-of-order';
        await displayScreen(ctx, apiClient, callback.ui, screenId);

        console.log(`Screen ${screenId} auf Terminal ${terminal.serialNumber} angezeigt`);

        // Bestimme den neuen Status
        const newState = hasAvailableOrChargingEvse ? 'ACTIVE' : 'OUT_OF_ORDER';

        // Logge die Statusänderung
        console.log(`Terminal ${terminal.serialNumber}: Status wird von ${terminal.terminalState || 'unbekannt'} auf ${newState} geändert`);

        // Aktualisiere den Terminal-Status in der Datenbank
        await strapi.documents('api::terminal.terminal').update({
          documentId: terminal.documentId,
          data: {
            terminalState: newState,
            lastUpdate: new Date()
          }
        });
      } catch (error) {
        console.error(`Fehler beim Aktualisieren des Screens für Terminal ${terminal.serialNumber}:`, error);
      }
    }
  } catch (error) {
    console.error(`Fehler beim Aktualisieren des Terminal-Screens für Location ${locationId}:`, error);
  }
}

/**
 * Setzt den Terminal-Status auf OUT_OF_ORDER und zeigt den entsprechenden Screen an
 * @param terminalId Die Seriennummer des Terminals
 */
export async function setOutOfOrderScreen(terminalId: string): Promise<void> {
  try {
    // Finde das Terminal anhand der Seriennummer
    const terminal = await strapi.documents('api::terminal.terminal').findFirst({
      filters: {
        serialNumber: terminalId
      },
      populate: {
        payter_connection: true,
        mandant: true
      }
    });

    if (!terminal) {
      console.error(`Terminal mit Seriennummer ${terminalId} nicht gefunden`);
      return;
    }

    // Erstelle den Kontext für die displayScreen-Funktion
    const ctx = {
      state: {
        terminal: terminal
      }
    };

    // Hole den API-Client für das Terminal
    const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

    if (!apiClient || !callback.ui) {
      console.error(`API-Client für Terminal ${terminalId} konnte nicht initialisiert werden`);
      return;
    }

    // Zeige den Out-of-Order-Screen an
    await displayScreen(ctx, apiClient, callback.ui, 'screen-out-of-order');

    console.log(`Screen screen-out-of-order auf Terminal ${terminalId} angezeigt`);

    // Aktualisiere den Terminal-Status in der Datenbank
    await strapi.documents('api::terminal.terminal').update({
      documentId: terminal.documentId,
      data: {
        terminalState: 'OUT_OF_ORDER',
        lastUpdate: new Date()
      }
    });

    console.log(`Terminal ${terminalId}: Status auf OUT_OF_ORDER gesetzt`);
  } catch (error) {
    console.error(`Fehler beim Setzen des Out-of-Order-Screens für Terminal ${terminalId}:`, error);
  }
}

export default {
  updateTerminalScreenByLocationStatus,
  setOutOfOrderScreen
};
