{"collectionName": "components_ocpi_geo_locations", "info": {"displayName": "GeoLocation", "description": "OCPI 2.2.1 GeoLocation object"}, "options": {}, "attributes": {"latitude": {"type": "string", "required": true, "comment": "Latitude of the point in decimal degree. Example: 50.770774. Decimal separator: \".\" Regex: -?[0-9]{1,2}\\.[0-9]{5,7}"}, "longitude": {"type": "string", "required": true, "comment": "Longitude of the point in decimal degree. Example: -126.104965. Decimal separator: \".\" Regex: -?[0-9]{1,3}\\.[0-9]{5,7}"}}}