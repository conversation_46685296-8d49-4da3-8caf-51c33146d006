{"collectionName": "components_tariff_hourly_rates", "info": {"displayName": "Hourly Rate", "icon": "clock", "description": "Stündliche Preisdaten"}, "options": {}, "attributes": {"hourFrom": {"type": "integer", "required": true, "min": 0, "max": 23}, "pricePerKwh": {"type": "decimal", "required": true}, "sessionFee": {"type": "decimal", "required": true}, "priceType": {"type": "enumeration", "enum": ["EPEX", "Festpreis"], "required": true}, "epex_base": {"type": "decimal"}, "hourTo": {"type": "integer", "max": 23}}}