/**
 * payter-connection controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::payter-connection.payter-connection', ({ strapi }) => ({
  // Erweitere die find Methode, um sicherzustellen, dass nur authentifizierte Benutzer zugreifen können
  async find(ctx) {
    // Pr<PERSON><PERSON>, ob ein gültiger API-Token oder eine Benutzer-Session vorliegt
    if (!ctx.state.user && !ctx.state.auth?.credentials) {
      return ctx.unauthorized('Unauthorized access. Valid API token or authentication required.');
    }
    
    // Standard find-Methode ausführen und Ergebnis zurückgeben
    return await super.find(ctx);
  },
  
  // Erweitere die findOne Methode mit Authentifizierung
  async findOne(ctx) {
    if (!ctx.state.user && !ctx.state.auth?.credentials) {
      return ctx.unauthorized('Unauthorized access. Valid API token or authentication required.');
    }
    
    return await super.findOne(ctx);
  },
  
  // Erweitere die create Methode mit Authentifizierung
  async create(ctx) {
    if (!ctx.state.user && !ctx.state.auth?.credentials) {
      return ctx.unauthorized('Unauthorized access. Valid API token or authentication required.');
    }
    
    return await super.create(ctx);
  },
  
  // Erweitere die update Methode mit Authentifizierung
  async update(ctx) {
    if (!ctx.state.user && !ctx.state.auth?.credentials) {
      return ctx.unauthorized('Unauthorized access. Valid API token or authentication required.');
    }
    
    return await super.update(ctx);
  },
  
  // Erweitere die delete Methode mit Authentifizierung
  async delete(ctx) {
    if (!ctx.state.user && !ctx.state.auth?.credentials) {
      return ctx.unauthorized('Unauthorized access. Valid API token or authentication required.');
    }
    
    return await super.delete(ctx);
  }
}));
