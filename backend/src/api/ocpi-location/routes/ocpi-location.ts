'use strict';

/**
 * OCPI Location Routes für OCPI 2.2.1
 * Definiert die Endpunkte für GET, PUT und PATCH von Locations
 */
module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/ocpi/2.2.1/locations',
      handler: 'ocpi-location.getLocations',
      config: {
        auth: false
      }
    },
    {
      method: 'PUT',
      path: '/ocpi/2.2.1/locations/:country/:party/:location/:evse?',
      handler: 'ocpi-location.putLocation',
      config: {
        auth: false
      }
    },
    {
      method: 'PATCH',
      path: '/ocpi/2.2.1/locations/:country/:party/:location/:evse?/:connector?',
      handler: 'ocpi-location.patchLocation',
      config: {
        auth: false
      }
    },
  ],
};
