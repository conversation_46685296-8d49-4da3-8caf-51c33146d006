/**
 * Enum für die Richtung der Terminal-Nachrichten
 */
export enum MessageDirection {
  ServerToTerminal = 'ServerToTerminal',
  TerminalToServer = 'TerminalToServer',
  WebToServer = 'WebToServer',
  Unknown = 'Unknown'
}

/**
 * Prüft, ob ein Wert ein gültiger MessageDirection-Enum-Wert ist
 * 
 * @param value Der zu prüfende Wert
 * @returns true, wenn der Wert ein gültiger MessageDirection-Enum-Wert ist, sonst false
 */
export function isValidMessageDirection(value: string): value is MessageDirection {
  return Object.values(MessageDirection).includes(value as MessageDirection);
}

/**
 * Konvertiert einen String in einen MessageDirection-Enum-Wert
 * Wenn der String kein gültiger Enum-Wert ist, wird MessageDirection.Unknown zurückgegeben
 * 
 * @param value Der zu konvertierende String
 * @returns Der entsprechende MessageDirection-Enum-Wert
 */
export function toMessageDirection(value: string): MessageDirection {
  if (isValidMessageDirection(value)) {
    return value as MessageDirection;
  }
  return MessageDirection.Unknown;
}
