{"kind": "collectionType", "collectionName": "mandants", "info": {"singularName": "mandant", "pluralName": "mandants", "displayName": "Mandant", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "children"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::mandant.mandant", "mappedBy": "parent"}, "users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "mandants"}, "ocpi_connections": {"type": "relation", "relation": "manyToMany", "target": "api::ocpi-connection.ocpi-connection", "inversedBy": "mandants"}, "payter_connection": {"type": "relation", "relation": "manyToOne", "target": "api::payter-connection.payter-connection", "inversedBy": "mandants"}, "AcPrice": {"type": "component", "repeatable": false, "component": "price.price"}, "DcPrice": {"type": "component", "repeatable": false, "component": "price.price"}, "ocpi_logs": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-log.ocpi-log"}, "ocpi_commands": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-command.ocpi-command"}, "tariff": {"type": "relation", "relation": "manyToOne", "target": "api::tariff.tariff", "inversedBy": "mandants"}, "terminals": {"type": "relation", "relation": "oneToMany", "target": "api::terminal.terminal", "mappedBy": "mandant"}, "invoice_templates": {"type": "relation", "relation": "oneToMany", "target": "api::invoice-template.invoice-template", "mappedBy": "mandant"}, "invoices": {"type": "relation", "relation": "oneToMany", "target": "api::invoice.invoice"}, "terminal_message_logs": {"type": "relation", "relation": "oneToMany", "target": "api::terminal-message-log.terminal-message-log", "mappedBy": "mandant"}, "payment_sessions": {"type": "relation", "relation": "oneToMany", "target": "api::payment-session.payment-session", "mappedBy": "mandant"}, "ocpi_locations": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-location.ocpi-location", "inverseBy": "mandant"}, "ocpi_cdrs": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-cdr.ocpi-cdr", "inverseBy": "mandant"}, "ocpi_sessions": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-session.ocpi-session", "inverseBy": "mandant"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "ciColor": {"type": "string"}, "ciButtonLabelColor": {"type": "string"}, "ciBgMenuColor": {"type": "string"}}}