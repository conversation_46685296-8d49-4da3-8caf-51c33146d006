// backend/src/api/ocpi-version/controllers/ocpi-version-2-2-1.ts
'use strict';

/**
 * OCPI Version 2.2.1 Controller für eMSP Modulendpunkte mit Payter-Terminal Integration
 */
module.exports = {
    async getModules(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'any');

            if (!validationResult.valid) {
                return ctx.send(createErrorResponse(2000, "Unauthorized"));
            }

            // Modulendpunkte zurückgeben
            return ctx.send(createModulesResponse());
        } catch (error) {
            strapi.log.error('Error in OCPI getModules:', error);
            return ctx.send(createErrorResponse(3000, "Server error"));
        }
    }
};

/**
 * Erstellt eine standardisierte OCPI-Fehlerantwort
 */
function createErrorResponse(statusCode, message) {
    return {
        status_code: statusCode,
        status_message: message,
        timestamp: new Date().toISOString()
    };
}

/**
 * Erstellt die OCPI-Modulantwort mit allen verfügbaren Endpunkten für eMSP
 */
function createModulesResponse() {
    const publicUrl = strapi.config.get('server.publicURL', 'localhost:1337');
    const baseUrl = `${publicUrl}/api/ocpi/2.2.1`;

    // Definiere die verfügbaren Endpunkte für eMSP mit korrekten Rollen
    const endpoints = [
        { identifier: "credentials", role: "BOTH", url: `${baseUrl}/credentials` },
        { identifier: "locations", role: "RECEIVER", url: `${baseUrl}/locations` },
        { identifier: "sessions", role: "BOTH", url: `${baseUrl}/sessions` },
        { identifier: "cdrs", role: "RECEIVER", url: `${baseUrl}/cdrs` },
        { identifier: "tokens", role: "SENDER", url: `${baseUrl}/tokens` },
        { identifier: "commands", role: "SENDER", url: `${baseUrl}/commands` },
        { identifier: "tariffs", role: "RECEIVER", url: `${baseUrl}/tariffs` }
    ];

    return {
        status_code: 1000,
        status_message: "Success",
        data:
            {
                version: "2.2.1",
                endpoints: endpoints
            }
        ,
        timestamp: new Date().toISOString()
    };
}
