/**
 * OCPI Response Router
 *
 * Definiert die Routen für die OCPI 2.2.1 Command-Response-Endpunkte:
 * - startSession
 * - stopSession
 */

import { factories } from '@strapi/strapi';

const routes = [
  {
    method: 'POST',
    path: '/ocpi/commands/response/startSession/:authorizationReference',
    handler: 'ocpi-response.startSession',
    config: {
      auth: false
    }
  },
  {
    method: 'POST',
    path: '/ocpi/2.2.1/commands/response/startSession/:authorizationReference',
    handler: 'ocpi-response.startSession',
    config: {
      auth: false
    }
  }
];

export default routes;
