{"kind": "collectionType", "collectionName": "tariffs", "info": {"singularName": "tariff", "pluralName": "tariffs", "displayName": "Tariff", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"Name": {"type": "string", "required": true}, "ValidFrom": {"type": "datetime", "required": true}, "ValidTo": {"type": "datetime", "required": true}, "dailySchedules": {"type": "component", "repeatable": true, "component": "tariff.daily-schedule"}, "blockFeeSchedules": {"type": "component", "repeatable": true, "component": "tariff.block-fee-schedule"}, "mandants": {"type": "relation", "relation": "oneToMany", "target": "api::mandant.mandant", "mappedBy": "tariff"}, "terminals": {"type": "relation", "relation": "oneToMany", "target": "api::terminal.terminal"}, "ocpi_evses": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-evse.ocpi-evse"}, "chargerType": {"type": "enumeration", "enum": ["AC", "DC"]}, "ocpi_locations": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-location.ocpi-location"}}}