const request = require('supertest');
const axios = require('axios');
jest.mock('axios');

// Testdaten für den PATCH-Request
const testLocationData = {
  "id": "DEEUlSE0669X",
  "country_code": "DE",
  "party_id": "EUL",
  "publish": true,
  "publish_allowed_to": [],
  "name": "Doventorsteinweg2",
  "address": "Doventorsteinweg 7",
  "city": "Bremen",
  "postal_code": "28195",
  "country": "DEU",
  "coordinates": {
    "latitude": "0.000000",
    "longitude": "0.000000"
  },
  "evses": [
    {
      "uid": "990cb15b-2420-474c-93cb-9b8d328fa68X",
      "evse_id": "DE*EUL*ESIMULATOR099*X",
      "status": "AVAILABLE",
      "capabilities": [
        "REMOTE_START_STOP_CAPABLE",
        "UNLOCK_CAPABLE"
      ],
      "connectors": [
        {
          "id": "1",
          "standard": "CHADEMO",
          "format": "SOCKET",
          "power_type": "AC_1_PHASE",
          "max_voltage": 0,
          "max_amperage": 0,
          "max_electric_power": 0,
          "tariff_ids": [
            "DEEULTDEFAULT"
          ],
          "last_updated": "2025-04-11T13:06:08.961Z"
        }
      ],
      "coordinates": {
        "latitude": "0.000000",
        "longitude": "0.000000"
      },
      "physical_reference": "DEEUlSE0669-1",
      "parking_restrictions": [],
      "last_updated": "2025-04-11T13:06:08.961Z"
    },
    {
      "uid": "ac14d582-d874-4f99-91cf-75765d7d8cbx",
      "evse_id": "DE*EUL*ESIMULATOR099*2X",
      "status": "AVAILABLE",
      "capabilities": [
        "REMOTE_START_STOP_CAPABLE",
        "UNLOCK_CAPABLE"
      ],
      "connectors": [
        {
          "id": "2",
          "standard": "CHADEMO",
          "format": "SOCKET",
          "power_type": "AC_1_PHASE",
          "max_voltage": 0,
          "max_amperage": 0,
          "max_electric_power": 0,
          "tariff_ids": [
            "DEEULTDEFAULT"
          ],
          "last_updated": "2025-04-11T13:06:08.961Z"
        }
      ],
      "coordinates": {
        "latitude": "0.000000",
        "longitude": "0.000000"
      },
      "physical_reference": "DEEUlSE0669-2",
      "parking_restrictions": [],
      "last_updated": "2025-04-11T13:06:08.961Z"
    }
  ],
  "directions": [],
  "facilities": [],
  "time_zone": "Europe/Berlin",
  "opening_times": {
    "twentyfourseven": true
  },
  "charging_when_closed": true,
  "last_updated": "2025-04-11T13:06:08.961Z"
};

// Mock für den OCPI-Location-Controller
const mockLocationController = {
  patchLocation: jest.fn().mockImplementation((ctx) => {
    // Prüfen, ob der Token gültig ist
    const authHeader = ctx.request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Token ')) {
      ctx.status(401);
      return ctx.send({
        status_code: 2000,
        status_message: 'Unauthorized',
        timestamp: new Date().toISOString()
      });
    }

    const token = authHeader.substring(6);
    if (token !== '10f414dbc0ebc0316be09bcfc3b79529') {
      ctx.status(401);
      return ctx.send({
        status_code: 2000,
        status_message: 'Unauthorized',
        timestamp: new Date().toISOString()
      });
    }

    // Erfolgsantwort zurückgeben
    ctx.status(200);
    return ctx.send({
      status_code: 1000,
      status_message: 'Success',
      timestamp: new Date().toISOString()
    });
  })
};

// Mock für Express
const express = require('express');
const app = express();
app.use(express.json());

// PATCH-Endpunkt für Locations
app.patch('/api/ocpi/2.2.1/locations/:countryCode/:partyId/:locationId', (req, res) => {
  const ctx = {
    params: req.params,
    request: {
      body: req.body,
      headers: req.headers
    },
    status: (code) => {
      res.status(code);
      return res;
    },
    send: (data) => {
      res.json(data);
      return res;
    }
  };

  return mockLocationController.patchLocation(ctx);
});

describe('OCPI Location API', () => {
  let server;

  beforeAll((done) => {
    server = app.listen(3000, done);
  });

  afterAll((done) => {
    if (server) {
      server.close(done);
    } else {
      done();
    }
  });

  it('sollte eine erfolgreiche Antwort zurückgeben, wenn der Token gültig ist', async () => {
    const response = await request('http://localhost:3000')
      .patch('/api/ocpi/2.2.1/locations/DE/EUL/DEEUlSE0669')
      .set('Authorization', 'Token 10f414dbc0ebc0316be09bcfc3b79529')
      .set('Content-Type', 'application/json')
      .send(testLocationData);

    expect(response.status).toBe(200);
    expect(response.body.status_code).toBe(1000);
    expect(response.body.status_message).toBe('Success');

    // Überprüfen, ob der Controller aufgerufen wurde
    expect(mockLocationController.patchLocation).toHaveBeenCalled();
  });

  it('sollte einen Fehler zurückgeben, wenn der Token ungültig ist', async () => {
    const response = await request('http://localhost:3000')
      .patch('/api/ocpi/2.2.1/locations/DE/EUL/DEEUlSE0669')
      .set('Authorization', 'Token ungueltig')
      .set('Content-Type', 'application/json')
      .send(testLocationData);

    expect(response.status).toBe(401);
    expect(response.body.status_code).toBe(2000);
    expect(response.body.status_message).toBe('Unauthorized');

    // Überprüfen, ob der Controller aufgerufen wurde
    expect(mockLocationController.patchLocation).toHaveBeenCalled();
  });
});
