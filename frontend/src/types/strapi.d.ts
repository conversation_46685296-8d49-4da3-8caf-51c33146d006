/**
 * Strapi Typen
 */

// Benutzer-Anmeldung
export interface StrapiLoginResponse {
	jwt: string;
	user: {
		id: number;
		username: string;
		email: string;
		provider: string;
		confirmed: boolean;
		blocked: boolean;
		createdAt: string;
		updatedAt: string;
		role?: {
			id: number;
			name: string;
			description: string;
			type: string;
		};
	};
}

// Generischer Strapi-Datentyp für Entitäten
export interface StrapiEntity<T> {
	id: number;
	attributes: T & {
		createdAt: string;
		updatedAt: string;
		publishedAt?: string;
	};
}

// Generischer Strapi-Antworttyp für Listen
export interface StrapiResponse<T> {
	data: StrapiEntity<T>[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

// Generischer Strapi-Antworttyp für einzelne Entitäten
export interface StrapiSingleResponse<T> {
	data: StrapiEntity<T>;
	meta: {
		// Nützliche Metadaten für einzelne Entitäten
	};
}

// Relationale Daten in Strapi
export interface StrapiRelation<T> {
	data: StrapiEntity<T> | null;
}

export interface StrapiRelationMany<T> {
	data: StrapiEntity<T>[];
}
