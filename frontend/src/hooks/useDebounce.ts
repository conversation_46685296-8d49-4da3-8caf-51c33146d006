import { useEffect, useState } from "react";

/**
 * Ein Hook für das Debouncing von Werten
 *
 * Verzögert Aktualisierungen eines Werts, um übermäßige Anfragen
 * bei schnellen Änderungen (z.B. bei Eingaben in Suchfeldern) zu vermeiden
 *
 * @param value Der Wert, der debounced werden soll
 * @param delay Die Verzögerungszeit in Millisekunden
 * @returns Der debounced Wert
 */
export function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value);

	useEffect(() => {
		// Aktualisiere den debounced-Wert nach der Verzögerung
		const timer = setTimeout(() => {
			setDebouncedValue(value);
		}, delay);

		// Bereinige den Timer, wenn sich der Wert ändert oder die Komponente unmountiert wird
		return () => {
			clearTimeout(timer);
		};
	}, [value, delay]);

	return debouncedValue;
}
