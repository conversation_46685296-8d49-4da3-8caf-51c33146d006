{"kind": "collectionType", "collectionName": "ocpi_locations", "info": {"singularName": "ocpi-location", "pluralName": "ocpi-locations", "displayName": "OCPI Location", "description": "OCPI 2.2.1 Location-Objekt"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"ocpiId": {"type": "string", "required": true, "comment": "Unique identifier of the location within the CPOs platform (and suboperator platforms)."}, "countryCode": {"type": "string", "required": true, "maxLength": 2, "comment": "ISO-3166 alpha-2 country code of the CPO that owns this location."}, "partyId": {"type": "string", "required": true, "maxLength": 3, "comment": "ID of the CPO that owns this location (following the ISO-15118 standard)."}, "publish": {"type": "boolean", "default": true, "comment": "Indicates whether this location may be published to end users by the eMSP."}, "publishAllowedTo": {"type": "json", "comment": "List of parties this location may be published to."}, "name": {"type": "string", "comment": "Display name of the location."}, "address": {"type": "string", "required": true, "comment": "Street/block name and house number if available."}, "city": {"type": "string", "required": true, "comment": "City or town."}, "postalCode": {"type": "string", "required": true, "comment": "Postal code of the location."}, "country": {"type": "string", "required": true, "maxLength": 3, "comment": "ISO 3166-1 alpha-3 code for the country of this location."}, "coordinates": {"type": "component", "repeatable": false, "component": "ocpi.geo-location", "required": false}, "relatedLocations": {"type": "component", "repeatable": true, "component": "ocpi.additional-geo-location"}, "directions": {"type": "component", "repeatable": true, "component": "ocpi.display-text"}, "operator": {"type": "component", "repeatable": false, "component": "ocpi.business-details"}, "suboperator": {"type": "component", "repeatable": false, "component": "ocpi.business-details"}, "owner": {"type": "component", "repeatable": false, "component": "ocpi.business-details"}, "facilities": {"type": "json", "comment": "Optional list of facilities this location contains."}, "timeZone": {"type": "string", "comment": "Time zone of the location, in IANA time zone format."}, "openingTimes": {"type": "component", "repeatable": false, "component": "ocpi.hours"}, "chargingWhenClosed": {"type": "boolean", "comment": "Indicates if the EVSEs are still charging when the location is closed."}, "images": {"type": "component", "repeatable": true, "component": "ocpi.image"}, "energyMix": {"type": "component", "repeatable": false, "component": "ocpi.energy-mix"}, "lastUpdated": {"type": "datetime", "required": true, "comment": "Timestamp when this Location was last updated."}, "ocpiConnection": {"type": "relation", "relation": "manyToOne", "target": "api::ocpi-connection.ocpi-connection"}, "evses": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-evse.ocpi-evse", "mappedBy": "location"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "ocpi_locations"}, "terminals": {"type": "relation", "relation": "oneToMany", "target": "api::terminal.terminal", "mappedBy": "location"}}}