/**
 * Utility-Funktionen für OCPI-Responses
 */

/**
 * Erstellt eine OCPI-Erfolgsantwort
 * 
 * @param data - Optionale Daten für die Antwort
 * @returns OCPI-Erfolgsantwort
 */
export function createSuccessResponse(data?: any) {
  const response: any = {
    status_code: 1000,
    status_message: 'Success',
    timestamp: new Date().toISOString()
  };
  
  // Füge data nur hinzu, wenn es vorhanden ist
  if (data !== undefined) {
    response.data = data;
  }
  
  return response;
}

/**
 * Erstellt eine OCPI-Fehlerantwort
 * 
 * @param statusCode - OCPI-Statuscode
 * @param statusMessage - Fehlermeldung
 * @returns OCPI-Fehlerantwort
 */
export function createErrorResponse(statusCode: number, statusMessage: string) {
  return {
    status_code: statusCode,
    status_message: statusMessage,
    timestamp: new Date().toISOString()
  };
}

/**
 * OCPI-Statuscodes
 */
export enum OCPIStatusCode {
  SUCCESS = 1000,
  CLIENT_ERROR = 2000,
  SERVER_ERROR = 3000,
  HUB_ERROR = 4000,
  NOT_IMPLEMENTED = 2001,
  INVALID_PARAMETERS = 2002,
  NOT_FOUND = 2003,
  TIMEOUT = 2004
}
