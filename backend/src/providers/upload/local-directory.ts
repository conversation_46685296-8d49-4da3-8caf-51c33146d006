// /providers/local-directory.ts
import * as fs from 'fs';
import * as path from 'path';

interface ProviderOptions {
  rootDir: string;
  baseUrl: string;
}

interface UploadResult {
  url: string;
  path: string;
}

export default {
  init(config: ProviderOptions) {
    const rootDir = path.resolve(config.rootDir);
    
    // <PERSON>elle sicher, dass das Verzeichnis existiert
    if (!fs.existsSync(rootDir)) {
      fs.mkdirSync(rootDir, { recursive: true });
    }

    return {
      upload(file: {buffer: Buffer, name: string}): Promise<UploadResult> {
        return new Promise<UploadResult>((resolve, reject) => {
          // Generiere einen sicheren Dateinamen
          const fileName = `${Date.now()}-${file.name}`;
          const filePath = path.join(rootDir, fileName);

          try {
            // Schreibe die Datei
            fs.writeFileSync(filePath, file.buffer);

            // Gib die URL zurück
            const fileUrl = `${config.baseUrl}/${fileName}`;
            resolve({
              url: fileUrl,
              path: filePath
            });
          } catch (error) {
            reject(error);
          }
        });
      },

      uploadStream(file: any): Promise<UploadResult> {
        return new Promise<UploadResult>((resolve, reject) => {
          const fileName = `${Date.now()}-${file.name}`;
          const filePath = path.join(rootDir, fileName);
          const writeStream = fs.createWriteStream(filePath);

          file.stream.pipe(writeStream);

          writeStream.on('finish', () => {
            const fileUrl = `${config.baseUrl}/${fileName}`;
            resolve({
              url: fileUrl,
              path: filePath
            });
          });

          writeStream.on('error', reject);
        });
      },

      delete(file: {path: string}): Promise<void> {
        return new Promise<void>((resolve, reject) => {
          try {
            fs.unlinkSync(file.path);
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      }
    };
  }
};