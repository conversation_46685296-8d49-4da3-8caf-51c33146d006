import {InvoicePos} from "./invoice-manager";

/**
 * Erstellt InvoiceItems basierend auf Preisinformationen und Session/CDR-Daten
 * @param price Tarifdetails (sessionFee, pricePerKwh, perMinute, gracePeriod, maxBlockFee)
 * @param data Daten aus CDR oder Session
 * @returns Array von InvoicePos-Objekten für die weitere Verwendung
 */
export function createInvoiceItems(
    price: {
        sessionFee?: number,
        pricePerKwh?: number,
        perMinute?: number,
        gracePeriod?: number,
        maxBlockFee?: number
    },
    data: {
        totalEnergy?: number,
        totalTime?: number
    }
): InvoicePos[] {
    const invoiceItems: InvoicePos[] = [];

    // Startgebühr hinzufügen, wenn vorhanden
    if (price.sessionFee) {
        invoiceItems.push({
            title: "Startgebühr",
            unit: "Stk",
            amount: 1,
            unit_price_gross: price.sessionFee / 100,
            tax_rate: 19
        });
    }

    // Energiekosten hinzufügen, wenn vorhanden und Energy-Daten verfügbar sind
    if (price.pricePerKwh && data.totalEnergy) {
        invoiceItems.push({
            title: "Energiekosten",
            unit: "kWh",
            amount: data.totalEnergy,
            unit_price_gross: price.pricePerKwh / 100,
            tax_rate: 19
        });
    }

    // Blockiergebühren hinzufügen, wenn vorhanden und Zeitdaten verfügbar sind
    if (price.perMinute && data.totalTime) {
        let blockingFeeAmount = data.totalTime * 60 * price.perMinute;
        let blockfeeMin = data.totalTime * 60;

        // Karenzzeit berücksichtigen
        if (price.gracePeriod && blockfeeMin > price.gracePeriod) {
            blockfeeMin = blockfeeMin - price.gracePeriod;
            blockingFeeAmount = blockfeeMin * price.perMinute;
        }

        // Maximale Blockiergebühr berücksichtigen
        if (price.maxBlockFee && blockingFeeAmount > price.maxBlockFee) {
            blockfeeMin = price.maxBlockFee / price.perMinute;
        }

        invoiceItems.push({
            title: `Blockiergebühren${price.gracePeriod ? ` ab ${price.gracePeriod} Minuten` : ''}`,
            unit: "Min",
            amount: blockfeeMin,
            unit_price_gross: price.perMinute / 100,
            tax_rate: 19
        });
    }

    return invoiceItems;
}


/**
 * Berechnet den zu belastenden Betrag anhand von Preis und CDR-Daten
 */
export function calculatePriceByTimeAndKwh(
    price: {
        sessionFee?: number,
        pricePerKwh?: number,
        perMinute?: number,
        gracePeriod?: number,
        maxBlockFee?: number
    },
    cdrData: {
        totalEnergy?: number,
        totalTime?: number
    }
): number {
    const invoiceItems = createInvoiceItems(price, cdrData);

    // Gesamtbetrag berechnen (in Cent)
    let totalAmount = 0;

    invoiceItems.forEach(item => {
        if (item.unit_price_gross) {
            totalAmount += item.unit_price_gross * item.amount * 100; // Umrechnung in Cent
        }
    });

    return Math.round(totalAmount); // Runden auf ganze Cent
}
