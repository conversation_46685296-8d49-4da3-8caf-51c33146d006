/**
 * API Service für Backend-Zugriff
 */
import axios from "axios";
import type { PayterTerminal } from "~/services/payter-api";

// Basis-URL aus der Umgebungsvariable oder Standard-URL
const API_URL =
	process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337";

// Axios-Instanz mit Basiseinstellungen
const apiClient = axios.create({
	baseURL: API_URL,
	headers: {
		"Content-Type": "application/json",
	},
});

// Request-Interceptor zum Hinzufügen des Authorization-Headers
apiClient.interceptors.request.use(
	(config) => {
		// Token aus localStorage holen
		const token = localStorage.getItem("strapiToken");

		// Wenn Token vorhanden ist, füge Authorization-Header hinzu
		if (token) {
			config.headers.Authorization = `Bearer ${token}`;
		}

		return config;
	},
	(error) => {
		return Promise.reject(error);
	},
);

// Response-Interceptor zum Behandeln von 401-Fehlern
apiClient.interceptors.response.use(
	(response) => response,
	(error) => {
		if (error.response && error.response.status === 401) {
			console.error(
				"401 Unauthorized: Kein gültiger API-Token oder abgelaufene Sitzung.",
			);

			// Bei 401-Fehler lokalen Token löschen und zur Login-Seite weiterleiten
			localStorage.removeItem("strapiToken");

			// Nur zur Login-Seite weiterleiten, wenn wir im Browser sind (nicht bei SSR)
			if (typeof window !== "undefined") {
				window.location.href = "/login";
			}
		}
		return Promise.reject(error);
	},
);

// Strapi Terminal-Typ
export interface TerminalData {
	id: number;
	documentId: string;
	serialNumber: string;
	terminalName: string;
	online: boolean;
	state: string;
	lastUpdate: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	evses?: {
		id: number;
		documentId: string;
		EvseId: string;
		Charger: string;
	}[];
	mandant?: {
		id: number;
		documentId: string;
		name?: string;
	};
	payter_connection?: {
		id: number;
		documentId: string;
		name: string;
		type: "Prod" | "Test";
		apiKey: string;
		apiUrl: string;
	};
}

export interface TerminalResponse {
	data: TerminalData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

// Payter Connection Typen
export interface PayterConnectionData {
	id: number;
	documentId: string;
	name: string;
	apiKey: string;
	apiUrl: string;
	type: "Prod" | "Test";
	createdAt: string;
	updatedAt: string;
	mandants?: {
		data: any[];
	};
	terminals?: {
		data: any[];
	};
}

export interface PayterConnectionResponse {
	data: PayterConnectionData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

// Global Config Typen
export interface GlobalConfigData {
	id: number;
	PayterApiUrlProd: string;
	PayterApiUrlTest: string;
	PayterApiUrlDev: string;
	ApplicationName: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
}

export interface GlobalConfigResponse {
	data: GlobalConfigData;
}

/**
 * Terminals API
 */
export const terminalsApi = {
	// Alle Terminals abrufen
	getAll: async (
		environment?: "test" | "prod",
		mandantId?: number,
	): Promise<TerminalResponse> => {
		try {
			// Parameter für die API-Anfrage vorbereiten
			const params: Record<string, any> = {
				"populate[0]": "payter_connection",
				"populate[1]": "mandant",
				"populate[2]": "evses",
				"populate[3]": "location",
			};

			// Wenn eine Umgebung angegeben ist, füge den Filter hinzu
			if (environment) {
				// Konvertiere environment zu Type (Prod, Test)
				const typeValue =
					environment.charAt(0).toUpperCase() + environment.slice(1);
				params["filters[payter_connection][type][$eq]"] = typeValue;
			}

			// Wenn ein Mandant angegeben ist, füge den Filter hinzu
			if (mandantId) {
				params["filters[mandant][id][$eq]"] = mandantId;
			}

			const response = await apiClient.get("/api/terminals", { params });
			return response.data;
		} catch (error) {
			console.error("Error fetching terminals:", error);
			throw error;
		}
	},

	// Ein Terminal anhand der ID abrufen
	getById: async (id: number): Promise<TerminalData> => {
		try {
			const response = await apiClient.get(`/api/terminals/${id}?populate=*`);
			return response.data.data;
		} catch (error) {
			console.error(`Error fetching terminal with id ${id}:`, error);
			throw error;
		}
	},

	// Ein Terminal anhand der Seriennummer finden
	findBySerialNumber: async (
		serialNumber: string,
	): Promise<TerminalData | null> => {
		try {
			const response = await apiClient.get(
				`/api/terminals?filters[serialNumber][$eq]=${serialNumber}&populate=*`,
			);
			if (response.data.data.length > 0) {
				return response.data.data[0];
			}
			return null;
		} catch (error) {
			console.error(
				`Error finding terminal with serial number ${serialNumber}:`,
				error,
			);
			throw error;
		}
	},

	// Neues Terminal erstellen
	create: async (terminalData: any): Promise<TerminalData> => {
		try {
			const response = await apiClient.post("/api/terminals", {
				data: terminalData,
			});
			return response.data.data;
		} catch (error) {
			console.error("Error creating terminal:", error);
			throw error;
		}
	},

	// Terminal aktualisieren
	update: async (
		documentId: string,
		terminalData: any,
	): Promise<TerminalData> => {
		try {
			console.log(
				"Terminal update request:",
				JSON.stringify(terminalData, null, 2),
			);
			// Hier wird das terminalData-Objekt direkt übergeben
			const response = await apiClient.put(
				`/api/terminals/${documentId}`,
				terminalData,
			);
			return response.data.data;
		} catch (error: any) {
			console.error(
				`Error updating terminal with documentId ${documentId}:`,
				error,
			);
			if (error.response) {
				console.error("Response data:", error.response.data);
			}
			throw error;
		}
	},

	// Terminal löschen
	delete: async (id: number): Promise<void> => {
		try {
			await apiClient.delete(`/api/terminals/${id}`);
		} catch (error) {
			console.error(`Error deleting terminal with id ${id}:`, error);
			throw error;
		}
	},
};

/**
 * Payter Connections API
 */
export const payterConnectionsApi = {
	// Alle Payter Connections abrufen
	getAll: async (params = {}): Promise<PayterConnectionResponse> => {
		try {
			const response = await apiClient.get("/api/payter-connections", {
				params,
			});
			return response.data;
		} catch (error) {
			console.error("Error fetching payter connections:", error);
			throw error;
		}
	},

	// Eine Payter Connection anhand der ID abrufen
	getById: async (id: number): Promise<PayterConnectionData> => {
		try {
			const response = await apiClient.get(
				`/api/payter-connections/${id}?populate=*`,
			);
			return response.data.data;
		} catch (error) {
			console.error(`Error fetching payter connection with id ${id}:`, error);
			throw error;
		}
	},

	// Neue Payter Connection erstellen
	create: async (connectionData: any): Promise<PayterConnectionData> => {
		try {
			const response = await apiClient.post("/api/payter-connections", {
				data: connectionData,
			});
			return response.data.data;
		} catch (error) {
			console.error("Error creating payter connection:", error);
			throw error;
		}
	},

	// Payter Connection aktualisieren
	update: async (
		id: number,
		connectionData: any,
	): Promise<PayterConnectionData> => {
		try {
			const response = await apiClient.put(`/api/payter-connections/${id}`, {
				data: connectionData,
			});
			return response.data.data;
		} catch (error) {
			console.error(`Error updating payter connection with id ${id}:`, error);
			throw error;
		}
	},

	// Payter Connection löschen
	delete: async (id: number): Promise<void> => {
		try {
			await apiClient.delete(`/api/payter-connections/${id}`);
		} catch (error) {
			console.error(`Error deleting payter connection with id ${id}:`, error);
			throw error;
		}
	},

	// Webhook für eine Payter Connection initialisieren
	initializeWebhook: async (documentId: string): Promise<any> => {
		try {
			const response = await apiClient.post(
				`/api/payter-connections/${documentId}/initialize-webhook`,
			);
			return response.data;
		} catch (error) {
			console.error(
				`Error initializing webhook for payter connection ${documentId}:`,
				error,
			);
			throw error;
		}
	},
};

/**
 * Locations API
 */
export interface LocationData {
	id: number;
	documentId: string;
	ocpiId: string;
	countryCode: string;
	partyId: string;
	name: string;
	address: string;
	city: string;
	postalCode: string;
	country: string;
	facilities?: any[];
	timeZone?: string;
	chargingWhenClosed?: boolean;
	lastUpdated: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	publish: boolean;
	publishAllowedTo?: any[];
	evses?: {
		id: number;
		EvseId: string;
		Charger: string;
	}[];
	terminals?: {
		id: number;
		serialNumber: string;
		terminalName: string;
	}[];
	mandant?: {
		data?: {
			id: number;
			documentId?: string;
			name?: string;
		};
	};
}

export interface LocationResponse {
	data: LocationData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

export const locationsApi = {
	getAllByMandant: async (
		mandantdocumentId: string | undefined,
		environment?: "Test" | "Prod",
	): Promise<LocationResponse> => {
		try {
			if (!mandantdocumentId) {
				throw new Error("Mandant ID is required");
			}
			const params = {
				"filters[mandant][documentId][$eq]": mandantdocumentId,
				"populate[0]": "mandant",
				...(environment && {
					"filters[terminals][payter_connection][type][$eq]":
						environment.charAt(0).toUpperCase() + environment.slice(1),
				}),
			};
			const response = await apiClient.get("/api/locations", { params });
			return response.data;
		} catch (error) {
			console.error("Error fetching locations:", error);
			throw error;
		}
	},

	// Alle Locations abrufen
	getAll: async (): Promise<LocationResponse> => {
		try {
			const response = await apiClient.get(
				"/api/locations?populate[0]=mandant",
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching locations:", error);
			throw error;
		}
	},

	// Eine Location anhand der ID abrufen
	getById: async (documentId: string): Promise<LocationData> => {
		try {
			const response = await apiClient.get(
				`/api/locations/${documentId}?populate=*`,
			);
			return response.data.data;
		} catch (error) {
			console.error(
				`Error fetching location with documentId ${documentId}:`,
				error,
			);
			throw error;
		}
	},

	// Neue Location erstellen
	create: async (locationData: any): Promise<LocationData> => {
		try {
			const response = await apiClient.post("/api/ocpi-locations", {
				data: locationData,
			});
			return response.data.data;
		} catch (error) {
			console.error("Error creating location:", error);
			throw error;
		}
	},

	// Location aktualisieren
	update: async (
		documentId: string,
		locationData: any,
	): Promise<LocationData> => {
		try {
			const response = await apiClient.put(
				`/api/ocpi-locations/${documentId}`,
				{
					data: locationData,
				},
			);
			return response.data.data;
		} catch (error) {
			console.error(
				`Error updating location with documentId ${documentId}:`,
				error,
			);
			throw error;
		}
	},

	// Location löschen
	delete: async (documentId: string): Promise<void> => {
		try {
			await apiClient.delete(`/api/ocpi-locations/${documentId}`);
		} catch (error) {
			console.error(
				`Error deleting location with documentId ${documentId}:`,
				error,
			);
			throw error;
		}
	},

	// Mandant einer Location zuordnen
	assignMandant: async (
		documentId: string,
		mandantId?: string,
	): Promise<LocationData> => {
		try {
			const response = await apiClient.put(
				`/api/locations/${documentId}/assign-mandant`,
				{
					mandantId,
				},
			);
			return response.data.data;
		} catch (error) {
			console.error(
				`Error assigning mandant to location with documentId ${documentId}:`,
				error,
			);
			throw error;
		}
	},
};

/**
 * EVSE API
 */
export interface EvseData {
	id: number;
	documentId: string;
	EvseId: string;
	Charger: string;
	PhysicalReference: string;
	PowerType: string;
	EvseStatus: string;
	EvseUid: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	terminal?: {
		id: number;
		documentId: string;
		serialNumber: string;
		terminalName: string;
	};
	mandant?: {
		data?: {
			id: number;
			documentId: string;
			name?: string;
		};
	};
}

export interface EvseResponse {
	data: EvseData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

export const evsesApi = {
	// Alle EVSEs abrufen
	getAll: async (): Promise<EvseResponse> => {
		try {
			const response = await apiClient.get("/api/evses?populate=*");
			return response.data;
		} catch (error) {
			console.error("Error fetching EVSEs:", error);
			throw error;
		}
	},

	// Ein EVSE anhand der ID abrufen
	getById: async (id: number): Promise<EvseData> => {
		try {
			const response = await apiClient.get(`/api/evses/${id}?populate=*`);
			return response.data.data;
		} catch (error) {
			console.error(`Error fetching EVSE with id ${id}:`, error);
			throw error;
		}
	},

	// Neues EVSE erstellen
	create: async (evseData: any): Promise<EvseData> => {
		try {
			const response = await apiClient.post("/api/evses", {
				data: evseData,
			});
			return response.data.data;
		} catch (error) {
			console.error("Error creating EVSE:", error);
			throw error;
		}
	},

	// EVSE aktualisieren
	update: async (documentId: string, evseData: any): Promise<EvseData> => {
		try {
			// Hier wird das evseData-Objekt direkt übergeben, ohne es in ein weiteres data-Objekt zu verpacken
			const response = await apiClient.put(
				`/api/evses/${documentId}`,
				evseData,
			);
			return response.data.data;
		} catch (error) {
			console.error(
				`Error updating EVSE with documentId ${documentId}:`,
				error,
			);
			throw error;
		}
	},

	// EVSE löschen
	delete: async (id: number): Promise<void> => {
		try {
			await apiClient.delete(`/api/evses/${id}`);
		} catch (error) {
			console.error(`Error deleting EVSE with id ${id}:`, error);
			throw error;
		}
	},
};

/**
 * Payter Webhook API
 */
export interface WebhookResponse {
	status_code: number;
	status_message: string;
	payter_response_status?: number;
	payter_response_data?: any;
	error_details?: any;
}

export const payterWebhookApi = {
	// Webhook für eine bestimmte Umgebung registrieren
	registerWebhook: async (
		environment: "prod" | "test" | "dev",
	): Promise<WebhookResponse> => {
		try {
			// Sende die Webhook-Registrierung an das Backend
			// Das Backend kümmert sich um alle Details (API-URL, API-Key, Callback-URL)
			const response = await apiClient.put("/api/payter/webhook", {
				environment: environment,
			});

			return response.data;
		} catch (error: any) {
			console.error(
				`Error registering webhook for ${environment} environment:`,
				error,
			);
			if (error?.response?.data) {
				return error.response.data;
			}
			return {
				status_code: 500,
				status_message: `Fehler bei der Webhook-Registrierung für ${environment}: ${error.message}`,
			};
		}
	},
};

/**
 * Mandant API Typen
 */
export interface MandantData {
	id: number;
	documentId: string;
	name: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	parent?: {
		data?: {
			id: number;
			documentId: string;
			name: string;
		};
	};
	children?: {
		data: {
			id: number;
			documentId: string;
			name: string;
		}[];
	};
	users?: {
		data: {
			id: number;
			username: string;
			email: string;
		}[];
	};
}

export interface MandantResponse {
	data: MandantData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

/**
 * Mandanten API
 */
export const mandantsApi = {
	// Alle Mandanten abrufen
	getAll: async (): Promise<MandantResponse> => {
		try {
			const response = await apiClient.get(
				"/api/mandants?populate[0]=parent&populate[1]=children&populate[2]=users\n",
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching mandants:", error);
			throw error;
		}
	},

	// Ein Mandant anhand der ID abrufen
	getById: async (id: number): Promise<MandantData> => {
		try {
			const response =
				await apiClient.get(`/api/mandants/${id}?populate[0]=parent&populate[1]=children&populate[2]=users
`);
			return response.data.data;
		} catch (error) {
			console.error(`Error fetching mandant with id ${id}:`, error);
			throw error;
		}
	},

	// Neuer Mandant erstellen
	create: async (mandantData: any): Promise<MandantData> => {
		try {
			const response = await apiClient.post("/api/mandants", {
				data: mandantData,
			});
			return response.data.data;
		} catch (error) {
			console.error("Error creating mandant:", error);
			throw error;
		}
	},

	// Mandant aktualisieren
	update: async (id: number, mandantData: any): Promise<MandantData> => {
		try {
			const response = await apiClient.put(`/api/mandants/${id}`, {
				data: mandantData,
			});
			return response.data.data;
		} catch (error) {
			console.error(`Error updating mandant with id ${id}:`, error);
			throw error;
		}
	},

	// Mandant löschen
	delete: async (id: number): Promise<void> => {
		try {
			await apiClient.delete(`/api/mandants/${id}`);
		} catch (error) {
			console.error(`Error deleting mandant with id ${id}:`, error);
			throw error;
		}
	},
};

// Payter API
export const payterApi = {
	// Terminal initialisieren
	initTerminal: async (terminalId: string): Promise<any> => {
		try {
			const response = await apiClient.get(
				`/api/payter/init?terminalId=${terminalId}`,
			);
			return response.data;
		} catch (error) {
			console.error(`Error initializing terminal ${terminalId}:`, error);
			throw error;
		}
	},

	// Terminal auf Out-of-Order setzen
	setOutOfOrder: async (terminalId: string): Promise<any> => {
		try {
			const response = await apiClient.get(
				`/api/payter/out-of-order?terminalId=${terminalId}`,
			);
			return response.data;
		} catch (error) {
			console.error(
				`Error setting terminal ${terminalId} to out of order:`,
				error,
			);
			throw error;
		}
	},
};

// Terminal Message Log API
export const terminalMessageLogApi = {
	// Alle Logs abrufen (mit Paginierung und Filterung)
	getAll: async (params = {}): Promise<any> => {
		try {
			const response = await apiClient.get("/api/terminal-message-logs", {
				params,
			});
			return response.data;
		} catch (error) {
			console.error("Error fetching terminal message logs:", error);
			throw error;
		}
	},

	// Einen einzelnen Log abrufen
	getById: async (id: number): Promise<any> => {
		try {
			const response = await apiClient.get(`/api/terminal-message-logs/${id}`);
			return response.data.data;
		} catch (error) {
			console.error(
				`Error fetching terminal message log with id ${id}:`,
				error,
			);
			throw error;
		}
	},

	// Alle eindeutigen Terminal-IDs für die Filterung abrufen
	getTerminalIds: async (): Promise<string[]> => {
		try {
			const response = await apiClient.get(
				"/api/terminal-message-logs/terminal-ids",
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching terminal IDs:", error);
			return [];
		}
	},

	// Einen Log manuell erstellen (z.B. für Testzwecke)
	create: async (data: any): Promise<any> => {
		try {
			const response = await apiClient.post("/api/terminal-message-logs", {
				data,
			});
			return response.data.data;
		} catch (error) {
			console.error("Error creating terminal message log:", error);
			throw error;
		}
	},

	// Log-Nachricht vom Server zum Terminal
	logToTerminal: async (
		terminalId: string,
		payload: any,
		options = {},
	): Promise<any> => {
		try {
			const response = await apiClient.post(
				"/api/terminal-message-log/log-to-terminal",
				{
					terminalId,
					payload,
					...options,
				},
			);
			return response.data;
		} catch (error) {
			console.error("Error logging message to terminal:", error);
			throw error;
		}
	},

	// Log-Nachricht vom Terminal zum Server
	logFromTerminal: async (
		terminalId: string,
		payload: any,
		options = {},
	): Promise<any> => {
		try {
			const response = await apiClient.post(
				"/api/terminal-message-log/log-from-terminal",
				{
					terminalId,
					payload,
					...options,
				},
			);
			return response.data;
		} catch (error) {
			console.error("Error logging message from terminal:", error);
			throw error;
		}
	},

	// Fehlermeldung loggen
	logError: async (
		terminalId: string,
		payload: any,
		options = {},
	): Promise<any> => {
		try {
			const response = await apiClient.post(
				"/api/terminal-message-log/log-error",
				{
					terminalId,
					payload,
					...options,
				},
			);
			return response.data;
		} catch (error) {
			console.error("Error logging terminal error:", error);
			throw error;
		}
	},
};

/**
 * OCPI CDR API
 */
export interface OcpiCdrData {
	id: number;
	documentId: string;
	cdrId: string;
	sessionId?: string;
	totalCost: number;
	currency: string;
	totalTime: number;
	timestamp: string;
	countryCode?: string;
	partyId?: string;
	startDateTime?: string;
	endDateTime?: string;
	authMethod?: string;
	authorizationReference?: string;
	lastUpdated?: string;
	totalFixedCost?: number;
	totalEnergyCost?: number;
	totalTimeCost?: number;
	totalParkingCost?: number;
	totalEnergy?: number;
	createdAt: string;
	updatedAt: string;
	mandant?: {
		data?: {
			id: number;
			documentId: string;
			name?: string;
		};
	};
}

export interface OcpiCdrResponse {
	data: OcpiCdrData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

export const ocpiCdrApi = {
	getAll: async (params = {}): Promise<OcpiCdrResponse> => {
		try {
			const response = await apiClient.get("/api/ocpi-cdrs", { params });
			return response.data;
		} catch (error) {
			console.error("Error fetching OCPI CDRs:", error);
			throw error;
		}
	},

	getById: async (id: string): Promise<OcpiCdrData> => {
		try {
			const response = await apiClient.get(`/api/ocpi-cdrs/${id}`);
			return response.data.data;
		} catch (error) {
			console.error(`Error fetching OCPI CDR with id ${id}:`, error);
			throw error;
		}
	},
};

/**
 * Invoice API
 */
export interface InvoiceData {
	id: number;
	documentId: string;
	invoice_number?: string;
	invoice_status: "DRAFT" | "INMUTABLE_WRITTEN" | "PAID";
	kindOfInvoice: "INVOICE" | "CREDIT" | "STORNO" | "CREDIT_STORNO";
	sum_net: number;
	sum_gross: number;
	vat_amount: number;
	vat_percentage: number;
	period_start_utc: string;
	period_end_utc: string;
	createdAt: string;
	updatedAt: string;
	mandant?: {
		documentId: string;
		name: string;
	};
	payment_session?: {
		documentId: string;
		paymentIntent: string;
	};
	ocpi_session?: {
		documentId: string;
		sessionId: string;
	};
	file_path:string;
}

export interface InvoiceResponse {
	data: InvoiceData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

export const invoiceApi = {
	getAll: async (params = {}): Promise<InvoiceResponse> => {
		try {
			const response = await apiClient.get("/api/invoices", { params });
			return response.data;
		} catch (error) {
			console.error("Error fetching invoices:", error);
			throw error;
		}
	},

	getById: async (id: string): Promise<{ data: InvoiceData }> => {
		try {
			const response = await apiClient.get(`/api/invoices/${id}`, {
				params: {
					populate: "*",
				},
			});
			return response.data;
		} catch (error) {
			console.error(`Error fetching invoice with id ${id}:`, error);
			throw error;
		}
	},

	generatePdf: async (id: string, force = false): Promise<any> => {
		try {
			const response = await apiClient.post(
				`/api/invoices/${id}/generate-pdf`,
				{},
				{
					params: { force },
				},
			);
			return response.data;
		} catch (error) {
			console.error(`Error generating PDF for invoice ${id}:`, error);
			throw error;
		}
	},

	downloadPdf: async (id: string): Promise<Blob> => {
		try {
			const response = await apiClient.get(`/api/invoices/${id}/download-pdf`, {
				responseType: "blob",
			});
			return response.data;
		} catch (error) {
			console.error(`Error downloading PDF for invoice ${id}:`, error);
			throw error;
		}
	},


};

/**
 * OCPI Session API
 */
export interface OcpiSessionData {
	id: number;
	documentId: string;
	sessionId: string;
	startTime: string;
	endTime?: string;
	kwh?: number;
	totalCost?: number;
	currency: string;
	ocpiStatus: "ACTIVE" | "COMPLETED" | "INVALID" | "PENDING" | "RESERVATION";
	countryCode?: string;
	partyId?: string;
	locationId?: string;
	evseUid?: string;
	connectorId?: string;
	authMethod?: string;
	authorizationReference?: string;
	lastUpdated?: string;
	createdAt: string;
	updatedAt: string;
	mandant?: {
		data?: {
			id: number;
			documentId: string;
			name?: string;
		};
	};
}

export interface OcpiSessionResponse {
	data: OcpiSessionData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

export const ocpiSessionApi = {
	getAll: async (params = {}): Promise<OcpiSessionResponse> => {
		try {
			const response = await apiClient.get("/api/ocpi-sessions", { params });
			return response.data;
		} catch (error) {
			console.error("Error fetching OCPI Sessions:", error);
			throw error;
		}
	},

	getById: async (id: string): Promise<OcpiSessionData> => {
		try {
			const response = await apiClient.get(`/api/ocpi-sessions/${id}`);
			return response.data.data;
		} catch (error) {
			console.error(`Error fetching OCPI Session with id ${id}:`, error);
			throw error;
		}
	},
};

/**
 * Payment Session API
 */
export type PaymentSessionData = {
	id: number;
	documentId: string;
	paymentIntent: string;
	state: "started" | "authorized" | "captured" | "canceled";
	blockedAmount: number;
	capturedAmount: number;
	cardId: string;
	maskedPan: string;
	brand: string;
	merchantReference: string;
	authorizationCode: string;
	authorizationHostReference: string;
	authorizedAt: string;
	closedAt: string;
	createdAt: string;
	updatedAt: string;
	// Strapi 5 Relationen
	terminal?: {
		id: number;
		documentId: string;
		serialNumber?: string;
		terminalName?: string;
	};
	mandant?: {
		id: number;
		documentId: string;
		name?: string;
	};
	ocpi_evse?: {
		id: number;
		documentId: string;
		evseId?: string;
	};
	ocpi_cdr?: {
		id: number;
		documentId: string;
		cdrId?: string;
	};
	ocpi_session?: {
		id: number;
		documentId: string;
		sessionId?: string;
	};
};

export interface PaymentSessionResponse {
	data: PaymentSessionData[];
	meta: {
		pagination: {
			page: number;
			pageSize: number;
			pageCount: number;
			total: number;
		};
	};
}

export const paymentSessionApi = {
	getAll: async (params = {}): Promise<PaymentSessionResponse> => {
		try {
			const response = await apiClient.get("/api/payment-sessions", { params });
			return response.data;
		} catch (error) {
			console.error("Error fetching payment sessions:", error);
			throw error;
		}
	},

	getById: async (
		id: string,
		params = {},
	): Promise<{ data: PaymentSessionData }> => {
		try {
			const response = await apiClient.get(`/api/payment-sessions/${id}`, {
				params,
			});
			return response.data;
		} catch (error) {
			console.error(`Error fetching payment session with ID ${id}:`, error);
			throw error;
		}
	},
};

// Export des API-Clients für direkte Verwendung
export { apiClient };
