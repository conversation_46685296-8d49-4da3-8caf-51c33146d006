{"kind": "collectionType", "collectionName": "ocpi_sessions", "info": {"singularName": "ocpi-session", "pluralName": "ocpi-sessions", "displayName": "OCPI Session"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"sessionId": {"type": "string", "unique": true, "required": true}, "startTime": {"type": "datetime", "required": true}, "endTime": {"type": "datetime"}, "kwh": {"type": "decimal"}, "totalCost": {"type": "decimal"}, "currency": {"type": "string", "required": true}, "ocpiStatus": {"type": "enumeration", "enum": ["ACTIVE", "COMPLETED", "INVALID", "PENDING", "RESERVATION"], "default": "INVALID", "required": true}, "countryCode": {"type": "string", "maxLength": 2}, "partyId": {"type": "string", "maxLength": 3}, "locationId": {"type": "string"}, "evseUid": {"type": "string"}, "connectorId": {"type": "string"}, "authMethod": {"type": "enumeration", "enum": ["AUTH_REQUEST", "COMMAND", "WHITELIST"]}, "authorizationReference": {"type": "string"}, "cdrToken": {"type": "json"}, "chargingPeriods": {"type": "json"}, "lastUpdated": {"type": "datetime"}, "payment_session": {"type": "relation", "relation": "oneToOne", "target": "api::payment-session.payment-session", "mappedBy": "ocpi_session"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "ocpi_sessions"}}}