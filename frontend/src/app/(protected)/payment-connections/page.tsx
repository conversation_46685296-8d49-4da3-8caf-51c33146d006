"use client";

import React, { useState, useEffect, useCallback } from "react";
import { FiRefreshCw } from "react-icons/fi";
import PaymentConnectionTable from "~/components/payment/PaymentConnectionTable";
import type { PaymentConnection } from "~/components/payment/PaymentConnectionTable";
import { Button } from "~/components/ui/button";
import { Spinner } from "~/components/ui/spinner";
import { useDebounce } from "~/hooks/useDebounce";
import { apiClient } from "~/services/api";

export default function PaymentConnectionsPage() {
	const [connections, setConnections] = useState<PaymentConnection[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// Filter states
	const [filters, setFilters] = useState({
		name: "",
		apiUrl: "",
		type: "",
	});

	// Debounced filters for API calls
	const debouncedFilters = useDebounce(filters, 300);

	const handleFilterChange = (name: string, value: string) => {
		setFilters((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const loadConnections = useCallback(async () => {
		setIsLoading(true);
		setError(null);

		try {
			// Build filter parameters
			const params: Record<string, any> = {
				"sort[0]": "name:asc",
				"pagination[pageSize]": 100,
				populate: "*", // Make sure all relations are loaded
			};

			// Add filters if they exist
			if (debouncedFilters.name) {
				params["filters[name][$containsi]"] = debouncedFilters.name;
			}

			if (debouncedFilters.apiUrl) {
				params["filters[apiUrl][$containsi]"] = debouncedFilters.apiUrl;
			}

			if (debouncedFilters.type) {
				params["filters[type][$eq]"] = debouncedFilters.type;
			}

			const response = await apiClient.get("/api/payter-connections", {
				params,
			});
			setConnections(response.data.data);
		} catch (err: any) {
			console.error("Error loading payment connections:", err);
			setError(err.message || "Fehler beim Laden der Payment-Connections");
		} finally {
			setIsLoading(false);
		}
	}, [debouncedFilters]);

	// Load connections on initial render and when filters change
	useEffect(() => {
		loadConnections();
	}, [loadConnections]);

	return (
		<div className="container mx-auto space-y-6 py-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Payment Connections</h1>
				<Button
					onClick={() => loadConnections()}
					variant="outline"
					className="flex items-center"
					disabled={isLoading}
				>
					<FiRefreshCw className={`mr-2 ${isLoading ? "animate-spin" : ""}`} />
					Aktualisieren
				</Button>
			</div>

			{error && (
				<div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
					{error}
				</div>
			)}

			{isLoading ? (
				<div className="flex items-center justify-center py-12">
					<Spinner size="lg" />
				</div>
			) : (
				<PaymentConnectionTable
					connections={connections}
					onRefresh={loadConnections}
					filters={filters}
					onFilterChange={handleFilterChange}
				/>
			)}
		</div>
	);
}
