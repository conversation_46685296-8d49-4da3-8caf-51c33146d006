{"collectionName": "components_ocpi_energy_mixes", "info": {"displayName": "EnergyMix", "description": "OCPI 2.2.1 EnergyMix object"}, "options": {}, "attributes": {"isGreenEnergy": {"type": "boolean", "required": true, "comment": "True if 100% from regenerative sources"}, "energySources": {"type": "component", "repeatable": true, "component": "ocpi.energy-source", "comment": "List of energy sources and their proportions"}, "environImpact": {"type": "component", "repeatable": true, "component": "ocpi.environmental-impact", "comment": "List of environmental impacts and their quantities"}, "supplierName": {"type": "string", "comment": "Name of the energy supplier"}, "energyProductName": {"type": "string", "comment": "Name of the energy product"}}}