/**
 * OCPI Command Controller
 *
 * Implementiert die OCPI 2.2.1 Command-Endpunkte:
 * - startSession
 * - stopSession
 * - unlockConnector
 */

import { factories } from '@strapi/strapi';

// Interface für die OCPI-Antwort
interface OCPIResponse {
  status_code: number;
  status_message?: string;
  data?: any;
  timestamp: string;
}

// Interface für die OCPI-Fehlerantwort
interface OCPIErrorResponse {
  status_code: number;
  status_message: string;
  timestamp: string;
}

// Enum für OCPI-Statuscodes
enum OCPIStatusCode {
  SUCCESS = 1000,
  CLIENT_ERROR = 2000,
  SERVER_ERROR = 3000,
  HUB_ERROR = 4000,
  NOT_IMPLEMENTED = 2001,
  INVALID_PARAMETERS = 2002,
  NOT_FOUND = 2003,
  TIMEOUT = 2004
}

// Enum für OCPI-Versionen
enum OCPIVersion {
  V2_1_1 = '2.1.1',
  V2_2 = '2.2',
  V2_2_1 = '2.2.1'
}

export default factories.createCoreController('api::ocpi-command.ocpi-command', ({ strapi }) => ({

  /**
   * Beendet eine Ladesession
   *
   * OCPI 2.2.1 Command: STOP_SESSION
   *
   * @param {Object} ctx - Koa-Kontext
   * @returns {Object} OCPI-Antwort
   */
  async stopSession(ctx) {
    try {
      // Extrahiere die OCPI-Version aus dem Header oder verwende die Standardversion
      const ocpiVersion = ctx.request.header['ocpi-version'] || OCPIVersion.V2_2_1;

      // Validiere die OCPI-Version
      if (!Object.values(OCPIVersion).includes(ocpiVersion as OCPIVersion)) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.CLIENT_ERROR,
          `Unsupported OCPI version: ${ocpiVersion}`
        ));
      }

      // Extrahiere die Parameter aus dem Request-Body
      const { session_id } = ctx.request.body || {};

      // Validiere die erforderlichen Parameter
      if (!session_id) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.INVALID_PARAMETERS,
          'Missing required parameter: session_id'
        ));
      }

      // Implementiere hier die Logik zum Beenden einer Ladesession
      // Dies ist ein Beispiel und sollte an Ihre spezifischen Anforderungen angepasst werden

      // Suche nach der Ladesession in der Datenbank
      // Hinweis: In einer realen Implementierung müssten Sie hier Ihre eigene Logik implementieren,
      // um die Ladesession zu finden. Dies ist nur ein Beispiel.
      // Verwende Type Assertion, um TypeScript-Fehler zu vermeiden
      const session = await strapi.entityService.findOne('api::charging-session.charging-session' as any, session_id, {
        populate: { evse: { populate: ['connectors'] } }
      });

      if (!session) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.NOT_FOUND,
          `Session not found: ${session_id}`
        ));
      }

      // Prüfe, ob die Session bereits beendet wurde
      // Verwende Type Assertion, um TypeScript-Fehler zu vermeiden
      if ((session as any).status === 'COMPLETED') {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.CLIENT_ERROR,
          `Session already completed: ${session_id}`
        ));
      }

      // Aktualisiere die Ladesession
      // Hinweis: In einer realen Implementierung müssten Sie hier Ihre eigene Logik implementieren,
      // um die Ladesession zu aktualisieren. Dies ist nur ein Beispiel.
      // Verwende Type Assertion, um TypeScript-Fehler zu vermeiden
      const updatedSession = await strapi.entityService.update('api::charging-session.charging-session' as any, session_id, {
        data: {
          status: 'COMPLETED',
          end_time: new Date().toISOString()
        }
      });

      // Aktualisiere den Status des Connectors
      // Hinweis: In einer realen Implementierung müssten Sie hier Ihre eigene Logik implementieren,
      // um den Connector zu aktualisieren. Dies ist nur ein Beispiel.
      // Verwende Type Assertion, um TypeScript-Fehler zu vermeiden
      const sessionAny = session as any;
      if (sessionAny.evse && sessionAny.evse.connectors) {
        const connector = sessionAny.evse.connectors.find(c => c.id === sessionAny.connector_id);
        if (connector) {
          await strapi.entityService.update('api::connector.connector', connector.id, {
            data: {
              status: 'AVAILABLE'
            }
          });
        }
      }

      // Erstelle die Antwort
      const response: OCPIResponse = {
        status_code: OCPIStatusCode.SUCCESS,
        status_message: 'Session stopped successfully',
        data: {
          session_id: session.id,
          status: 'COMPLETED',
          end_time: (updatedSession as any).end_time
        },
        timestamp: new Date().toISOString()
      };

      return ctx.send(response);
    } catch (error) {
      console.error('Error stopping session:', error);

      return ctx.send(createErrorResponse(
        OCPIStatusCode.SERVER_ERROR,
        `Error stopping session: ${error.message || 'Unknown error'}`
      ));
    }
  },

  /**
   * Entsperrt einen Connector
   *
   * OCPI 2.2.1 Command: UNLOCK_CONNECTOR
   *
   * @param {Object} ctx - Koa-Kontext
   * @returns {Object} OCPI-Antwort
   */
  async unlockConnector(ctx) {
    try {
      // Extrahiere die OCPI-Version aus dem Header oder verwende die Standardversion
      const ocpiVersion = ctx.request.header['ocpi-version'] || OCPIVersion.V2_2_1;

      // Validiere die OCPI-Version
      if (!Object.values(OCPIVersion).includes(ocpiVersion as OCPIVersion)) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.CLIENT_ERROR,
          `Unsupported OCPI version: ${ocpiVersion}`
        ));
      }

      // Extrahiere die Parameter aus dem Request-Body
      const { location_id, evse_uid, connector_id } = ctx.request.body || {};

      // Validiere die erforderlichen Parameter
      if (!location_id || !evse_uid || !connector_id) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.INVALID_PARAMETERS,
          'Missing required parameters: location_id, evse_uid, connector_id'
        ));
      }

      // Implementiere hier die Logik zum Entsperren eines Connectors
      // Dies ist ein Beispiel und sollte an Ihre spezifischen Anforderungen angepasst werden

      // Suche nach dem EVSE in der Datenbank
      // Hinweis: In einer realen Implementierung müssten Sie hier Ihre eigene Logik implementieren,
      // um die EVSEs zu finden. Dies ist nur ein Beispiel.
      const evses = await strapi.db.query('api::evse.evse').findMany({
        where: {
          uid: evse_uid,
          location: {
            uid: location_id
          }
        },
        populate: { connectors: true, location: true }
      });

      if (!evses || evses.length === 0) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.NOT_FOUND,
          `EVSE not found: ${evse_uid} at location ${location_id}`
        ));
      }

      const evse = evses[0];

      // Suche nach dem Connector
      const connector = evse.connectors.find(c => c.id === connector_id);
      if (!connector) {
        return ctx.send(createErrorResponse(
          OCPIStatusCode.NOT_FOUND,
          `Connector not found: ${connector_id} at EVSE ${evse_uid}`
        ));
      }

      // Aktualisiere den Status des Connectors
      // Hinweis: In einer realen Implementierung müssten Sie hier Ihre eigene Logik implementieren,
      // um den Connector zu aktualisieren. Dies ist nur ein Beispiel.
      await strapi.entityService.update('api::connector.connector', connector.id, {
        data: {
          status: 'AVAILABLE'
        }
      });

      // Erstelle die Antwort
      const response: OCPIResponse = {
        status_code: OCPIStatusCode.SUCCESS,
        status_message: 'Connector unlocked successfully',
        data: {
          location_id,
          evse_uid,
          connector_id,
          status: 'AVAILABLE'
        },
        timestamp: new Date().toISOString()
      };

      return ctx.send(response);
    } catch (error) {
      console.error('Error unlocking connector:', error);

      return ctx.send(createErrorResponse(
        OCPIStatusCode.SERVER_ERROR,
        `Error unlocking connector: ${error.message || 'Unknown error'}`
      ));
    }
  }
}));

/**
 * Erstellt eine OCPI-Fehlerantwort
 *
 * @param {number} statusCode - OCPI-Statuscode
 * @param {string} statusMessage - Fehlermeldung
 * @returns {OCPIErrorResponse} OCPI-Fehlerantwort
 */
function createErrorResponse(statusCode: number, statusMessage: string): OCPIErrorResponse {
  return {
    status_code: statusCode,
    status_message: statusMessage,
    timestamp: new Date().toISOString()
  };
}
