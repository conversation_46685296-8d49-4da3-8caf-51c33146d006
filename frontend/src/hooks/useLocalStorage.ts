import { useEffect, useState } from "react";

/**
 * Hook zum Speichern und Abru<PERSON> von Werten aus dem localStorage
 * @param key - Der Schlüssel, unter dem der Wert im localStorage gespeichert wird
 * @param initialValue - Der Initialwert, falls kein Wert im localStorage vorhanden ist
 * @returns Ein Tupel mit dem aktuellen Wert und einer Funktion zum Setzen des Werts
 */
export function useLocalStorage<T>(
	key: string,
	initialValue: T,
): [T, (value: T | ((val: T) => T)) => void] {
	// Zustand für den aktuellen Wert
	const [storedValue, setStoredValue] = useState<T>(() => {
		if (typeof window === "undefined") {
			return initialValue;
		}

		try {
			// Versuche, den Wert aus dem localStorage zu lesen
			const item = window.localStorage.getItem(key);
			// Wenn der Wert existiert, parse ihn, sonst verwende den Initialwert
			return item ? JSON.parse(item) : initialValue;
		} catch (error) {
			// Bei Fehlern verwende den Initialwert
			console.error(`Error reading localStorage key "${key}":`, error);
			return initialValue;
		}
	});

	// Effekt zum Aktualisieren des localStorage, wenn sich der Wert ändert
	useEffect(() => {
		if (typeof window === "undefined") {
			return;
		}

		try {
			// Speichere den Wert im localStorage
			window.localStorage.setItem(key, JSON.stringify(storedValue));
		} catch (error) {
			console.error(`Error setting localStorage key "${key}":`, error);
		}
	}, [key, storedValue]);

	// Funktion zum Setzen des Werts
	const setValue = (value: T | ((val: T) => T)) => {
		try {
			// Erlaube funktionale Updates wie bei useState
			const valueToStore =
				value instanceof Function ? value(storedValue) : value;
			// Aktualisiere den Zustand
			setStoredValue(valueToStore);
		} catch (error) {
			console.error(
				`Error setting value for localStorage key "${key}":`,
				error,
			);
		}
	};

	return [storedValue, setValue];
}
