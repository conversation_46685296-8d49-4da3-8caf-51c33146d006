import payterApiService from '../../payter/services/payter-api';
import { Context } from 'koa';
import { Public } from '@strapi/strapi';

// Strapi-spezifische Typen
interface StrapiContext extends Context {
  params: Record<string, any>;
  query: Record<string, any>;
  request: Context['request'] & { body?: any };
  state: Record<string, any>;
  send: (data: any) => any;
  badRequest: (message: string | object) => any;
  notFound: (message: string) => any;
  unauthorized: (message: string) => any;
  forbidden: (message: string) => any;
  internalServerError: (message: string) => any;
}

// Typdefinitionen
interface SyncResult {
  created: Array<{
    id: string;
    serialNumber: string;
    terminalName: string;
    state: string;
  }>;
  updated: Array<{
    id: string;
    serialNumber: string;
    terminalName: string;
    state: string;
  }>;
  deactivated: Array<{
    id: string;
    serialNumber: string;
    terminalName: string;
    state: string;
  }>;
  errors: Array<{
    connection: string;
    error: string;
  }>;
}

interface PayterTerminal {
  serialNumber: string;
  terminalName: string;
  online: boolean;
  state?: string;
  [key: string]: any;
}

// Definiere den Typ für PayterConnection basierend auf dem Strapi-Schema
type PayterConnection = Public.ContentTypeSchemas['api::payter-connection.payter-connection']

// Definiere den Typ für Terminal basierend auf dem Strapi-Schema
type Terminal = Public.ContentTypeSchemas['api::terminal.terminal']

// Interface für die Antwort von strapi.documents
interface TerminalDocument {
  documentId: string;
  serialNumber?: string;
  terminalName?: string;
  online?: boolean;
  terminalState?: 'OUT_OF_ORDER' | 'ACTIVE' | 'MAINTENANCE' | 'IDLE';
  lastUpdate?: string | Date;
  payter_connection?: string;
  mandant?: string;
  [key: string]: any;
}

export default {
    /**
     * Synchronisiert Payter-Terminals mit der Strapi-Datenbank
     * - Ruft Terminals von der Payter API ab
     * - Legt neue Terminals in Strapi an
     * - Aktualisiert bestehende Terminals
     * - Markiert nicht mehr existierende Terminals als inaktiv
     *
     * @param {Object} ctx - Der Kontext der Anfrage
     * @returns {Object} - Das Ergebnis der Synchronisierung
     */
    async getSync(ctx) {
        try {
            // Parameter aus der URL extrahieren
            const { mandantDocumentId, environment } = ctx.params;

            // Ergebnisse für die Antwort vorbereiten
            const result: SyncResult = {
                created: [],
                updated: [],
                deactivated: [],
                errors: []
            };

            // Filter für Payter-Verbindungen vorbereiten
            const filters: any = {};

            // Wenn eine Mandanten-ID angegeben ist, nur Verbindungen für diesen Mandanten abrufen
            if (mandantDocumentId && mandantDocumentId !== 'all') {
                filters.mandants = {
                    documentId: mandantDocumentId
                };
            }

            // Wenn eine Umgebung angegeben ist, nur Verbindungen für diese Umgebung abrufen
            if (environment && environment !== 'all') {
                // Umgebung in den Typ konvertieren (Prod, Test)
                const typeValue = environment.charAt(0).toUpperCase() + environment.slice(1).toLowerCase();
                if (typeValue === 'Prod' || typeValue === 'Test') {
                    filters.type = typeValue;
                }
            }

            // Payter-Verbindungen aus der Datenbank abrufen
            const payterConnections = await strapi.documents('api::payter-connection.payter-connection').findMany({
                filters,
                populate: ['mandants']
            });

            if (!payterConnections || payterConnections.length === 0) {
                return ctx.badRequest('Keine Payter-Verbindungen gefunden');
            }

            // Für jede Verbindung die Terminals synchronisieren
            for (const connection of payterConnections) {
                try {
                    // API-Client für die Verbindung erstellen
                    const apiClient = payterApiService.getApiClient(connection.apiUrl as string, connection.apiKey as string);

                    if (!apiClient) {
                        result.errors.push({
                            connection: connection.name as string,
                            error: 'API-Client konnte nicht erstellt werden'
                        });
                        continue;
                    }

                    // Terminals von der Payter API abrufen
                    const response = await apiClient.get('/terminals');
                    const payterTerminals = response.data.map(terminal => ({
                        ...terminal,
                        terminalState: terminal.state,  // Konvertiere state zu terminalState
                    })) as PayterTerminal[];


                    // Alle bestehenden Terminals für diese Verbindung aus der Datenbank abrufen
                    const existingTerminals = await strapi.documents('api::terminal.terminal').findMany({
                        filters: {
                            payter_connection: { documentId: connection.documentId as string }
                        }
                    });

                    // Mapping für schnelleren Zugriff erstellen
                    const existingTerminalsMap: Record<string, TerminalDocument> = {};
                    existingTerminals.forEach(terminal => {
                        if (terminal.serialNumber) {
                            // Terminal als TerminalDocument typisieren
                            const terminalDoc: TerminalDocument = {
                                ...terminal,
                                documentId: terminal.documentId as string,
                                serialNumber: terminal.serialNumber as string
                            };
                            existingTerminalsMap[terminal.serialNumber as string] = terminalDoc;
                        }
                    });

                    // Tracking für Terminals, die in Payter existieren
                    const activeTerminalSerialNumbers = new Set<string>();

                    // Terminals verarbeiten
                    for (const payterTerminal of payterTerminals) {
                        const serialNumber = payterTerminal.serialNumber;
                        activeTerminalSerialNumbers.add(serialNumber);

                        // Prüfen, ob das Terminal bereits in der Datenbank existiert
                        if (existingTerminalsMap[serialNumber]) {
                            // Terminal aktualisieren
                            const existingTerminal = existingTerminalsMap[serialNumber];
                            const updatedTerminal = await strapi.documents('api::terminal.terminal').update({
                                documentId: existingTerminal.documentId as string,
                                data: {
                                    terminalName: payterTerminal.terminalName,
                                    online: payterTerminal.online,
                                    terminalState: payterTerminal.online ? 'ACTIVE' : 'OUT_OF_ORDER',
                                    lastUpdate: new Date()
                                }
                            });

                            result.updated.push({
                                id: updatedTerminal.documentId as string,
                                serialNumber: updatedTerminal.serialNumber as string,
                                terminalName: updatedTerminal.terminalName as string,
                                state: updatedTerminal.terminalState as string
                            });

                            // Terminal-Kommunikation loggen
                            await strapi.service('api::terminal-message-log.terminal-message-log').logToTerminal(
                                serialNumber,
                                { action: 'sync', status: 'updated' },
                                {
                                    direction: 'ServerToTerminal',
                                    messageType: 'info',
                                    responsePayload: payterTerminal
                                }
                            );
                        } else {
                            // Neues Terminal anlegen
                            // Wenn die Verbindung Mandanten hat, nehmen wir den ersten
                            const mandantId = connection.mandants && (connection.mandants as any[]).length > 0
                                ? (connection.mandants as any[])[0].documentId as string
                                : null;

                            const newTerminal = await strapi.documents('api::terminal.terminal').create({
                                data: {
                                    serialNumber: serialNumber,
                                    terminalName: payterTerminal.terminalName,
                                    online: payterTerminal.online,
                                    terminalState: payterTerminal.online ? 'ACTIVE' : 'OUT_OF_ORDER',
                                    lastUpdate: new Date(),
                                    payter_connection: connection.documentId as string,
                                    mandant: mandantId,
                                    currentLanguage: 'de' // Standardsprache
                                }
                            });

                            result.created.push({
                                id: newTerminal.documentId as string,
                                serialNumber: newTerminal.serialNumber as string,
                                terminalName: newTerminal.terminalName as string,
                                state: newTerminal.terminalState as string
                            });

                            // Terminal-Kommunikation loggen
                            await strapi.service('api::terminal-message-log.terminal-message-log').logToTerminal(
                                serialNumber,
                                { action: 'sync', status: 'created' },
                                {
                                    direction: 'ServerToTerminal',
                                    messageType: 'info',
                                    responsePayload: payterTerminal
                                }
                            );
                        }
                    }

                    // Terminals, die nicht mehr bei Payter existieren, als inaktiv markieren
                    for (const terminal of existingTerminals) {
                        if (!activeTerminalSerialNumbers.has(terminal.serialNumber as string)) {
                            const deactivatedTerminal = await strapi.documents('api::terminal.terminal').update({
                                documentId: terminal.documentId as string,
                                data: {
                                    online: false,
                                    terminalState: 'OUT_OF_ORDER',
                                    lastUpdate: new Date()
                                }
                            });

                            result.deactivated.push({
                                id: deactivatedTerminal.documentId as string,
                                serialNumber: deactivatedTerminal.serialNumber as string,
                                terminalName: deactivatedTerminal.terminalName as string,
                                state: deactivatedTerminal.terminalState as string
                            });

                            // Terminal-Kommunikation loggen
                            await strapi.service('api::terminal-message-log.terminal-message-log').logToTerminal(
                                terminal.serialNumber as string,
                                { action: 'sync', status: 'deactivated' },
                                {
                                    direction: 'ServerToTerminal',
                                    messageType: 'warning'
                                }
                            );
                        }
                    }
                } catch (connectionError) {
                    console.error(`Fehler bei der Synchronisierung der Verbindung ${connection.name as string}:`, connectionError);
                    result.errors.push({
                        connection: connection.name as string,
                        error: connectionError.message || 'Unbekannter Fehler'
                    });
                }
            }

            // Zusammenfassung erstellen
            const summary = {
                total: {
                    created: result.created.length,
                    updated: result.updated.length,
                    deactivated: result.deactivated.length,
                    errors: result.errors.length
                },
                details: result
            };

            return ctx.send(summary);
        } catch (error) {
            console.error('Fehler bei der Terminal-Synchronisierung:', error);
            return ctx.badRequest({
                error: 'Fehler bei der Terminal-Synchronisierung',
                details: error.message
            });
        }
    }
}
