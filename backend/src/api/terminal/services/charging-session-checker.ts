/**
 * Service zur Prüfung aktiver Ladevorgänge an EVSEs
 */

/**
 * Prüft, ob an einer EVSE bereits ein aktiver Ladevorgang läuft
 * @param evseDocumentId Die Document-ID der EVSE
 * @returns Promise<{hasActiveSession: boolean, paymentSession?: any}>
 */
export async function checkActiveChargingSession(evseDocumentId: string): Promise<{
  hasActiveSession: boolean;
  paymentSession?: any;
}> {
  try {
    // Suche nach aktiven Payment-Sessions für diese EVSE
    const activePaymentSessions = await strapi.documents('api::payment-session.payment-session').findMany({
      filters: {
        ocpi_evse: {
          documentId: evseDocumentId
        },
        paymentSessionState: {
          $in: ['authorized', 'started']
        },
        closedAt: null // Nur Sessions ohne closedAt
      },
      sort: { createdAt: 'desc' }, // Sortiere nach createdAt absteigend (neueste zuerst)
      limit: 1, // Nur die neueste Session
      populate: {
        ocpi_session: {
          fields: ['startTime', 'endTime', 'kwh', 'authorizationReference', 'currency', 'totalCost', 'ocpiStatus']
        },
        terminal: {
          fields: ['serialNumber', 'terminalName']
        },
        ocpi_evse: {
          fields: ['evseId', 'labelForTerminal',"ocpiStatus"]
        }
      }
    });

    if (activePaymentSessions && activePaymentSessions.length > 0) {
      const paymentSession = activePaymentSessions[0];
      
      // Prüfe zusätzlich, ob die OCPI-Session aktiv ist
      const hasActiveOcpiSession = paymentSession.ocpi_evse.ocpiStatus == "CHARGING"

      return {
        hasActiveSession: hasActiveOcpiSession,
        paymentSession: hasActiveOcpiSession ? paymentSession : undefined
      };
    }

    return {
      hasActiveSession: false
    };
  } catch (error) {
    console.error(`Fehler beim Prüfen der aktiven Ladesession für EVSE ${evseDocumentId}:`, error);
    return {
      hasActiveSession: false
    };
  }
}

/**
 * Stoppt eine aktive Ladesession
 * @param paymentSessionId Die ID der Payment-Session
 * @returns Promise<{success: boolean, message?: string}>
 */
export async function stopChargingSession(paymentSessionId: string): Promise<{
  success: boolean;
  message?: string;
}> {
  try {
    // Hole die Payment-Session mit allen relevanten Daten
    const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
      documentId: paymentSessionId,
      populate: {
        ocpi_evse: {
          populate: {
            location: {
              populate: {
                ocpiConnection: true
              }
            }
          }
        },
        ocpi_session: true,
        terminal: true,
        mandant: true
      }
    });

    if (!paymentSession) {
      return {
        success: false,
        message: 'Payment-Session nicht gefunden'
      };
    }

    // Verwende den OCPI STOP_SESSION Command
    const stopSessionResult = await strapi.service('api::ocpi-command.ocpi-command').stopSession({
      session_id: paymentSession.ocpi_session?.sessionId,
      evse_id: paymentSession.ocpi_evse?.evseId,
      mandant_id: paymentSession.mandant?.documentId
    });

    if (stopSessionResult.success) {
      // Aktualisiere die Payment-Session
      await strapi.documents('api::payment-session.payment-session').update({
        documentId: paymentSessionId,
        data: {
          paymentSessionState: 'canceled',
          closedAt: new Date()
        }
      });

      return {
        success: true,
        message: 'Ladevorgang erfolgreich gestoppt'
      };
    } else {
      return {
        success: false,
        message: stopSessionResult.response?.status_message || 'Fehler beim Stoppen des Ladevorgangs'
      };
    }
  } catch (error) {
    console.error(`Fehler beim Stoppen der Ladesession ${paymentSessionId}:`, error);
    return {
      success: false,
      message: 'Unerwarteter Fehler beim Stoppen des Ladevorgangs'
    };
  }
}

export default {
  checkActiveChargingSession,
  stopChargingSession
};
