@import "tailwindcss";
@tailwind utilities;

/* Custom component classes */
@layer components {
	.btn-primary {
		@apply rounded-md bg-gradient-to-b from-[rgb(23,85,104)] via-[rgb(33,105,124)] to-[rgb(43,135,154)] px-4 py-2 font-medium text-sm text-white shadow-md hover:opacity-90 transition-opacity disabled:opacity-50;
	}
}

:root {
	--font-sans: var(--font-source-sans), ui-sans-serif, system-ui, sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	--font-heading: var(--font-montserrat), var(--font-source-sans), ui-sans-serif,
		system-ui, sans-serif;
	--color-primary: oklch(0.546 0.245 262.881);
	--color-primary-light: rgb(43, 135, 154);
	--color-primary-dark: rgb(23, 85, 104);
}

/* Use Montserrat for all headings */
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--font-heading);
}

/* Use Source Sans 3 as default body font */
body {
	font-family: var(--font-sans);
}

/* Ensure all buttons have pointer cursor */
button {
	cursor: pointer;
}
