// src/api/ocpi-log/services/ocpi-logger.ts
'use strict';

// Wenn uuid nicht installiert ist, bitte folgendes ausführen:
// npm install uuid
// npm install --save-dev @types/uuid
import { randomUUID } from 'crypto';

// Typendefinitionen für Logger-Parameter
interface LoggerOptions {
    endpoint?: string | null;
    request?: any;
    response?: any;
    mandantId?: number | null;
}

// Strapi spezifischer Log-Level Typ
type LogLevel = 'info' | 'warn' | 'error' | 'debug';

/**
 * OCPI Logger Service
 * Dient zur Protokollierung von OCPI-Kommunikation
 */
module.exports = {
    /**
     * Erstellt einen neuen OCPI-Log-Eintrag
     *
     * @param {Object} options - Logging-Optionen
     * @param {string} options.message - Nachricht, die geloggt werden soll
     * @param {string} options.level - Log-Level (info, warn, error, debug)
     * @param {string} options.endpoint - OCPI-Endpunkt
     * @param {Object} options.request - Request-Objekt (optional)
     * @param {Object} options.response - Response-Objekt (optional)
     * @param {number} options.mandantId - ID des zugehörigen Mandanten (optional)
     * @returns {Promise<Object>} - Erstellter Log-Eintrag
     */
    async log({
                  message,
                  level = 'info',
                  endpoint = null,
                  request = null,
                  response = null,
                  mandantId = null
              }: {
        message: string;
        level?: LogLevel;
        endpoint?: string | null;
        request?: any;
        response?: any;
        mandantId?: number | null;
    }) {
        try {
            const logEntry: any = {
                logId: randomUUID(),
                message,
                level,
                endpoint,
                timestamp: new Date().toISOString(),
            };

            // Nur JSON-String für nicht-null request/response einfügen
            if (request) {
                logEntry.request = JSON.stringify(request);
            }

            if (response) {
                logEntry.response = JSON.stringify(response);
            }

            // Mandant-Relation hinzufügen, falls eine ID übergeben wurde
            if (mandantId) {
                // Die richtige Strapi-Beziehungsstruktur verwenden
                logEntry.mandant = { connect: [{ id: mandantId }] };
            }

            // Log in die Datenbank schreiben
            const result = await strapi.entityService.create('api::ocpi-log.ocpi-log', {
                data: logEntry,
            });

            return result;
        } catch (error) {
            console.error('Fehler beim Erstellen des OCPI-Logs:', error);

            // Auch bei Logfehler nicht abstürzen, stattdessen console.error verwenden
            return null;
        }
    },

    /**
     * Generiert einen Info-Log
     */
    async info(message: string, options: LoggerOptions = {}) {
        return this.log({
            message,
            level: 'info' as LogLevel,
            endpoint: options.endpoint,
            request: options.request,
            response: options.response,
            mandantId: options.mandantId
        });
    },

    /**
     * Generiert einen Warn-Log
     */
    async warn(message: string, options: LoggerOptions = {}) {
        return this.log({
            message,
            level: 'warn' as LogLevel,
            endpoint: options.endpoint,
            request: options.request,
            response: options.response,
            mandantId: options.mandantId
        });
    },

    /**
     * Generiert einen Error-Log
     */
    async error(message: string, options: LoggerOptions = {}) {
        return this.log({
            message,
            level: 'error' as LogLevel,
            endpoint: options.endpoint,
            request: options.request,
            response: options.response,
            mandantId: options.mandantId
        });
    },

    /**
     * Generiert einen Debug-Log
     */
    async debug(message: string, options: LoggerOptions = {}) {
        return this.log({
            message,
            level: 'debug' as LogLevel,
            endpoint: options.endpoint,
            request: options.request,
            response: options.response,
            mandantId: options.mandantId
        });
    }
};
