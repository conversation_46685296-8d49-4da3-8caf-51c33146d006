// src/api/ocpi-2.2.1-credentials/routes/credential.ts
'use strict';

export default {
    routes: [
        {
            method: 'POST',
            path: '/payter',
            handler: 'payter.postPayter',
            config: {
                auth: false
            }
        },
        {
            method: 'GET',
            path: '/payter/callback',
            handler: 'payter.getCallback',
            config: {
                auth: false
            }
        },
        {
            method: 'POST',
            path: '/payter/callback',
            handler: 'payter.postCallback',
            config: {
                auth: false
            }
        },
        {
            method: 'POST',
            path: '/payter/uiCallback',
            handler: 'payter.postUiCallback',
            config: {
                auth: false
            }
        },
        {
            method: 'POST',
            path: '/payter/stateCallback',
            handler: 'payter.postStateCallback',
            config: {
                auth: false
            }
        },
        {
            method: 'PUT',
            path: '/payter/webhook',
            handler: 'payter.putWebhook',
            config: {
                auth: false
            }
        },
        {
          method: 'POST',
          path: '/payter/authorizeCallback',
          handler: 'payter.authorizeCallback',
          config: {
            auth: false
          }
        },
        {
            method: 'GET',
            path: '/payter/init',
            handler: 'payter.getInit',
            config: {
                auth: false
            }
        },
        {
            method: 'GET',
            path: '/payter/charging-points',
            handler: 'payter.getChargingPoints',
            config: {
                auth: false
            }
        },
        {
            method: 'GET',
            path: '/payter/reading',
            handler: 'payter.getReading',
            config: {
                auth: false
            }
        },
        {
            method: 'GET',
            path: '/payter/charging-started',
            handler: 'payter.getChargingStarted',
            config: {
                auth: false
            }
        },
        {
            method: 'GET',
            path: '/payter/out-of-order',
            handler: 'payter.getOutOfOrder',
            config: {
                auth: false
            }
        },
        {
            method: 'POST',
            path: '/payter/cardInfoCallback',
            handler: 'payter.cardInfoCallback',
            config: {
                auth: false
            }
        }
    ]
};
