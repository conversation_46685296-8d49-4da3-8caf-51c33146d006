/**
 * Terminal Status Controller
 * Verwaltet den Status der Terminals basierend auf dem Status der zugehörigen EVSEs
 */

import { updateTerminalScreenByLocationStatus } from '../services/terminal-status';

export default {
  /**
   * Aktualisiert den Terminal-Screen basierend auf dem Status der EVSEs an einer Location
   * @param {Object} ctx - Der Kontext der Anfrage
   * @returns {Object} - Das Ergebnis der Aktualisierung
   */
  async updateScreenByLocationStatus(ctx) {
    try {
      const { locationId } = ctx.params;

      if (!locationId) {
        return ctx.badRequest('Location ID ist erforderlich');
      }

      // Rufe die Service-Funktion auf
      await updateTerminalScreenByLocationStatus(locationId);

      return ctx.send({
        status_code: 1000,
        status_message: "Terminal-Screen erfolgreich aktualisiert",
        data: {
          locationId
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('<PERSON><PERSON> beim Aktualisieren des Terminal-Screens:', error);
      return ctx.internalServerError('Ein Fehler ist aufgetreten');
    }
  }
};
