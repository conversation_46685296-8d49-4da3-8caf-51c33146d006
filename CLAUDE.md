# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# EulektroTerminalVerwaltung Development Guide

## Build & Development Commands
- **Frontend**: `cd frontend && npm run dev` - Start NextJS dev server
- **Backend**: `cd backend && npm run develop` - Start Strapi with inspector
- **Type Check**: `cd frontend && npm run typecheck` - TypeScript validation
- **Lint**: `cd frontend && npm run check` - Run Biome linter
- **Format**: `cd frontend && npm run check:write` - Auto-format with Biome
- **Build**: `npm run build` (root), `cd frontend && npm run build` (frontend), `cd backend && npm run build` (backend)

## Testing Commands
- **Run All Tests**: `cd backend && npm test` - Run Jest tests with force exit
- **Single Test**: `cd backend && npx jest path/to/test.js` - Run specific test file
- **Pattern Match**: `cd backend && npx jest -t "test pattern"` - Run tests matching pattern
- **Watch Mode**: `cd backend && npx jest --watch` - Run tests in watch mode

## Code Style Guidelines
- **Imports**: Use organized imports (frontend uses Biome for auto-organization)
- **Types**: Strong typing preferred. Frontend uses strict TypeScript with `noUncheckedIndexedAccess`
- **Naming**: Use camelCase for variables/functions, PascalCase for components/classes, UPPER_CASE for constants
- **Path Aliases**: Frontend uses `~/*` for src directory imports
- **Format**: Frontend uses Biome formatter with default rules and useSortedClasses
- **Error Handling**: Use proper error handling and avoid silent failures 
- **API Structure**: Backend follows Strapi conventions with controllers, services, and routes
- **Architecture**: Frontend is Next.js (T3 Stack), Backend is Strapi