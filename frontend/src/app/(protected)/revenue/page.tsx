/**
 * Umsatzseite
 * Zeigt payment-sessions in einer Tabelle an mit Filtermöglichkeiten
 */

"use client";

import { format } from "date-fns";
import { de } from "date-fns/locale";
import { useCallback, useEffect, useState } from "react";
import {
	FaColumns,
	FaDownload,
	FaEye,
	FaFilter,
	FaSearch,
	FaSync,
} from "react-icons/fa";
import { toast } from "react-toastify";
import { useMandant } from "~/components/MandantContext";
import { Spinner } from "~/components/Spinner";
import { useDebounce } from "~/hooks/useDebounce";
import { useLocalStorage } from "~/hooks/useLocalStorage";
import { type PaymentSessionData, paymentSessionApi } from "~/services/api";
import { formatCurrency } from "~/utils/formatters";

// Standard-Spalten für die Tabelle
const DEFAULT_COLUMNS = {
	paymentIntent: true,
	state: true,
	blockedAmount: true,
	capturedAmount: true,
	maskedPan: true,
	brand: true,
	merchantReference: true,
	authorizedAt: true,
	closedAt: true,
	terminal: true,
	mandant: true,
	evse: true,
	cdr: true,
	session: true,
};

// Polling-Intervall in Millisekunden
const POLLING_INTERVAL = 30000; // 30 Sekunden

export default function RevenuePage() {
	// Zustandsvariablen
	const [paymentSessions, setPaymentSessions] = useState<PaymentSessionData[]>(
		[],
	);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [isLive, setIsLive] = useState(false);
	const [showFilters, setShowFilters] = useState(false);
	const [showColumnSelector, setShowColumnSelector] = useState(false);

	// Filter-Zustände
	const [filters, setFilters] = useState({
		search: "",
		startDate: "",
		endDate: "",
		state: "",
	});

	// Spalten-Einstellungen aus localStorage
	const [columns, setColumns] = useLocalStorage(
		"payment-session-columns",
		DEFAULT_COLUMNS,
	);

	// Mandant-Kontext
	const { activeMandant } = useMandant();

	// Debounced Suche
	const debouncedSearch = useDebounce(filters.search, 500);

	// Daten laden mit Paginierung
	const fetchPaymentSessions = useCallback(
		async (resetPage = false) => {
			try {
				setIsLoading(true);
				setError(null);

				const currentPage = resetPage ? 1 : page;
				if (resetPage) {
					setPage(1);
					setPaymentSessions([]);
				}

				// Filterbedingungen aufbauen
				const params: Record<string, any> = {
					sort: "createdAt:desc",
					"pagination[page]": currentPage,
					"pagination[pageSize]": 20,
					populate: "*",
				};

				// Suchfilter
				if (debouncedSearch) {
					params["filters[$or][0][paymentIntent][$containsi]"] =
						debouncedSearch;
					params["filters[$or][1][merchantReference][$containsi]"] =
						debouncedSearch;
					params["filters[$or][2][maskedPan][$containsi]"] = debouncedSearch;
				}

				// Datumsfilter
				if (filters.startDate) {
					params["filters[createdAt][$gte]"] = filters.startDate;
				}

				if (filters.endDate) {
					params["filters[createdAt][$lte]"] = filters.endDate;
				}

				// Status-Filter
				if (filters.state) {
					params["filters[state][$eq]"] = filters.state;
				}

				// Mandantfilter
				if (activeMandant && activeMandant.documentId !== "root") {
					params["filters[mandant][documentId][$eq]"] =
						activeMandant.documentId;
				}

				const response = await paymentSessionApi.getAll(params);

				if (resetPage) {
					setPaymentSessions(response.data);
				} else {
					setPaymentSessions((prev) => [...prev, ...response.data]);
				}

				// Prüfen, ob noch mehr Daten verfügbar sind
				setHasMore(
					response.meta.pagination.page < response.meta.pagination.pageCount,
				);

				if (currentPage === 1 && response.data.length === 0) {
					setHasMore(false);
				}
			} catch (err: any) {
				console.error("Fehler beim Laden der Payment Sessions:", err);
				setError(
					`Payment Sessions konnten nicht geladen werden. ${err.message || ""}`,
				);
			} finally {
				setIsLoading(false);
			}
		},
		[page],
	);

	// Effekt für das Laden der Daten bei Änderung der Filter
	useEffect(() => {
		fetchPaymentSessions(true);
	}, [
		fetchPaymentSessions,
		debouncedSearch,
		filters.startDate,
		filters.endDate,
		filters.state,
		activeMandant,
	]);

	// Live-Polling
	useEffect(() => {
		let interval: NodeJS.Timeout | null = null;

		if (isLive) {
			interval = setInterval(() => {
				fetchPaymentSessions(true);
			}, POLLING_INTERVAL);
		}

		return () => {
			if (interval) {
				clearInterval(interval);
			}
		};
	}, [isLive, fetchPaymentSessions]);

	// Weitere Daten laden
	const loadMore = () => {
		if (!isLoading && hasMore) {
			setPage((prevPage) => prevPage + 1);
		}
	};

	// Formatierungsfunktionen
	const formatDate = (dateString: string | undefined) => {
		if (!dateString) return "-";
		try {
			return format(new Date(dateString), "dd.MM.yyyy HH:mm:ss", {
				locale: de,
			});
		} catch (e) {
			return dateString;
		}
	};

	const formatState = (state: string | undefined) => {
		if (!state) return "-";

		const stateMap: Record<string, { label: string; className: string }> = {
			started: { label: "Gestartet", className: "bg-blue-100 text-blue-800" },
			authorized: {
				label: "Autorisiert",
				className: "bg-yellow-100 text-yellow-800",
			},
			captured: {
				label: "Abgebucht",
				className: "bg-green-100 text-green-800",
			},
			canceled: { label: "Abgebrochen", className: "bg-red-100 text-red-800" },
		};

		const stateInfo = stateMap[state] || {
			label: state,
			className: "bg-gray-100 text-gray-800",
		};

		return (
			<span
				className={`rounded-full px-2 py-1 font-medium text-xs ${stateInfo.className}`}
			>
				{stateInfo.label}
			</span>
		);
	};

	// CSV-Export
	const exportToCSV = () => {
		try {
			// Spaltenüberschriften
			const headers = [
				columns.paymentIntent ? "Payment Intent" : null,
				columns.state ? "Status" : null,
				columns.blockedAmount ? "Reservierter Betrag" : null,
				columns.capturedAmount ? "Abgebuchter Betrag" : null,
				columns.maskedPan ? "Karten-PAN" : null,
				columns.brand ? "Kartentyp" : null,
				columns.merchantReference ? "Merchant Reference" : null,
				columns.authorizedAt ? "Autorisiert am" : null,
				columns.closedAt ? "Abgeschlossen am" : null,
				columns.terminal ? "Terminal" : null,
				columns.mandant ? "Mandant" : null,
				columns.evse ? "EVSE ID" : null,
				columns.cdr ? "CDR ID" : null,
				columns.session ? "Session ID" : null,
			]
				.filter(Boolean)
				.join(",");

			// Datenzeilen
			const rows = paymentSessions
				.map((session) => {
					const row = [
						columns.paymentIntent ? `"${session.paymentIntent || ""}"` : null,
						columns.state ? `"${session.state || ""}"` : null,
						columns.blockedAmount ? session.blockedAmount || 0 : null,
						columns.capturedAmount ? session.capturedAmount || 0 : null,
						columns.maskedPan ? `"${session.maskedPan || ""}"` : null,
						columns.brand ? `"${session.brand || ""}"` : null,
						columns.merchantReference
							? `"${session.merchantReference || ""}"`
							: null,
						columns.authorizedAt
							? `"${session.authorizedAt ? formatDate(session.authorizedAt) : ""}"`
							: null,
						columns.closedAt
							? `"${session.closedAt ? formatDate(session.closedAt) : ""}"`
							: null,
						columns.terminal
							? `"${session.terminal?.serialNumber || ""}"`
							: null,
						columns.mandant ? `"${session.mandant?.name || ""}"` : null,
						columns.evse ? `"${session.ocpi_evse?.evseId || ""}"` : null,
						columns.cdr ? `"${session.ocpi_cdr?.cdrId || ""}"` : null,
						columns.session
							? `"${session.ocpi_session?.sessionId || ""}"`
							: null,
					]
						.filter(Boolean)
						.join(",");
					return row;
				})
				.join("\n");

			// CSV-Datei erstellen und herunterladen
			const csv = `${headers}\n${rows}`;
			const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
			const url = URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.setAttribute("href", url);
			link.setAttribute(
				"download",
				`payment-sessions-${format(new Date(), "yyyy-MM-dd")}.csv`,
			);
			link.style.visibility = "hidden";
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			toast.success("CSV-Export erfolgreich");
		} catch (err) {
			console.error("Fehler beim CSV-Export:", err);
			toast.error("Fehler beim CSV-Export");
		}
	};

	return (
		<div className="container mx-auto px-4 py-8">
			<h1 className="mb-6 font-bold text-2xl">Umsätze</h1>

			{/* Toolbar */}
			<div className="mb-4 flex flex-wrap items-center justify-between gap-2">
				<div className="flex items-center space-x-2">
					<div className="relative">
						<input
							type="text"
							placeholder="Suchen..."
							className="rounded-lg border py-2 pr-4 pl-10 focus:outline-none focus:ring-2 focus:ring-blue-500"
							value={filters.search}
							onChange={(e) =>
								setFilters((prev) => ({ ...prev, search: e.target.value }))
							}
						/>
						<FaSearch className="absolute top-3 left-3 text-gray-400" />
					</div>

					<button
						className={`rounded-lg p-2 ${showFilters ? "bg-blue-500 text-white" : "bg-gray-200"}`}
						onClick={() => setShowFilters(!showFilters)}
						title="Filter anzeigen/ausblenden"
					>
						<FaFilter />
					</button>

					<button
						className={`rounded-lg p-2 ${showColumnSelector ? "bg-blue-500 text-white" : "bg-gray-200"}`}
						onClick={() => setShowColumnSelector(!showColumnSelector)}
						title="Spalten anzeigen/ausblenden"
					>
						<FaColumns />
					</button>

					<button
						className={`rounded-lg p-2 ${isLive ? "bg-green-500 text-white" : "bg-gray-200"}`}
						onClick={() => setIsLive(!isLive)}
						title={
							isLive
								? "Live-Aktualisierung deaktivieren"
								: "Live-Aktualisierung aktivieren"
						}
					>
						<FaSync className={isLive ? "animate-spin" : ""} />
					</button>
				</div>

				<div>
					<button
						className="flex items-center space-x-2 rounded-lg bg-green-500 px-4 py-2 text-white"
						onClick={exportToCSV}
						disabled={isLoading || paymentSessions.length === 0}
					>
						<FaDownload />
						<span>CSV Export</span>
					</button>
				</div>
			</div>

			{/* Filter */}
			{showFilters && (
				<div className="mb-4 rounded-lg bg-gray-100 p-4">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-4">
						<div>
							<label className="mb-1 block font-medium text-gray-700 text-sm">
								Status
							</label>
							<select
								className="w-full rounded-lg border p-2"
								value={filters.state}
								onChange={(e) =>
									setFilters((prev) => ({ ...prev, state: e.target.value }))
								}
							>
								<option value="">Alle</option>
								<option value="started">Gestartet</option>
								<option value="authorized">Autorisiert</option>
								<option value="captured">Abgebucht</option>
								<option value="canceled">Abgebrochen</option>
							</select>
						</div>

						<div>
							<label className="mb-1 block font-medium text-gray-700 text-sm">
								Von
							</label>
							<input
								type="datetime-local"
								className="w-full rounded-lg border p-2"
								value={filters.startDate}
								onChange={(e) =>
									setFilters((prev) => ({ ...prev, startDate: e.target.value }))
								}
							/>
						</div>

						<div>
							<label className="mb-1 block font-medium text-gray-700 text-sm">
								Bis
							</label>
							<input
								type="datetime-local"
								className="w-full rounded-lg border p-2"
								value={filters.endDate}
								onChange={(e) =>
									setFilters((prev) => ({ ...prev, endDate: e.target.value }))
								}
							/>
						</div>

						<div className="flex items-end">
							<button
								className="rounded-lg bg-gray-500 px-4 py-2 text-white"
								onClick={() =>
									setFilters({
										search: "",
										startDate: "",
										endDate: "",
										state: "",
									})
								}
							>
								Filter zurücksetzen
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Spaltenauswahl */}
			{showColumnSelector && (
				<div className="mb-4 rounded-lg bg-gray-100 p-4">
					<h3 className="mb-2 font-medium">Spalten anzeigen/ausblenden</h3>
					<div className="grid grid-cols-2 gap-2 md:grid-cols-4 lg:grid-cols-6">
						{Object.keys(DEFAULT_COLUMNS).map((column) => (
							<div key={column} className="flex items-center space-x-2">
								<input
									type="checkbox"
									id={`column-${column}`}
									checked={
										columns?.[column as keyof typeof DEFAULT_COLUMNS] ?? true
									}
									onChange={() => {
										setColumns((prev) => ({
											...(prev || DEFAULT_COLUMNS),
											[column]: !(
												prev?.[column as keyof typeof DEFAULT_COLUMNS] ?? true
											),
										}));
									}}
								/>
								<label htmlFor={`column-${column}`} className="select-none">
									{column === "paymentIntent"
										? "Payment Intent"
										: column === "state"
											? "Status"
											: column === "blockedAmount"
												? "Reservierter Betrag"
												: column === "capturedAmount"
													? "Abgebuchter Betrag"
													: column === "maskedPan"
														? "Karten-PAN"
														: column === "brand"
															? "Kartentyp"
															: column === "merchantReference"
																? "Merchant Reference"
																: column === "authorizedAt"
																	? "Autorisiert am"
																	: column === "closedAt"
																		? "Abgeschlossen am"
																		: column === "terminal"
																			? "Terminal"
																			: column === "mandant"
																				? "Mandant"
																				: column === "evse"
																					? "EVSE ID"
																					: column === "cdr"
																						? "CDR ID"
																						: column === "session"
																							? "Session ID"
																							: column}
								</label>
							</div>
						))}
					</div>
				</div>
			)}

			{/* Tabelle */}
			<div className="overflow-hidden rounded-lg bg-white shadow">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								{columns?.paymentIntent && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Payment Intent
									</th>
								)}
								{columns?.state && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Status
									</th>
								)}
								{columns?.blockedAmount && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Reservierter Betrag
									</th>
								)}
								{columns?.capturedAmount && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Abgebuchter Betrag
									</th>
								)}
								{columns?.maskedPan && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Karten-PAN
									</th>
								)}
								{columns?.brand && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Kartentyp
									</th>
								)}
								{columns?.merchantReference && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Merchant Reference
									</th>
								)}
								{columns?.authorizedAt && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Autorisiert am
									</th>
								)}
								{columns?.closedAt && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Abgeschlossen am
									</th>
								)}
								{columns?.terminal && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Terminal
									</th>
								)}
								{columns?.mandant && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Mandant
									</th>
								)}
								{columns?.evse && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										EVSE ID
									</th>
								)}
								{columns?.cdr && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										CDR ID
									</th>
								)}
								{columns?.session && (
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Session ID
									</th>
								)}
								<th className="px-6 py-3 text-right font-medium text-gray-500 text-xs uppercase tracking-wider">
									Aktionen
								</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-200 bg-white">
							{paymentSessions.map((session) => (
								<tr key={session.documentId} className="hover:bg-gray-50">
									{columns?.paymentIntent && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.paymentIntent || "-"}
										</td>
									)}
									{columns?.state && (
										<td className="whitespace-nowrap px-6 py-4 text-sm">
											{formatState(session.state)}
										</td>
									)}
									{columns?.blockedAmount && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{formatCurrency(session.blockedAmount)}
										</td>
									)}
									{columns?.capturedAmount && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{formatCurrency(session.capturedAmount)}
										</td>
									)}
									{columns?.maskedPan && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.maskedPan || "-"}
										</td>
									)}
									{columns?.brand && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.brand || "-"}
										</td>
									)}
									{columns?.merchantReference && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.merchantReference || "-"}
										</td>
									)}
									{columns?.authorizedAt && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{formatDate(session.authorizedAt)}
										</td>
									)}
									{columns?.closedAt && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{formatDate(session.closedAt)}
										</td>
									)}
									{columns?.terminal && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.terminal?.serialNumber || "-"}
										</td>
									)}
									{columns?.mandant && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.mandant?.name || "-"}
										</td>
									)}
									{columns?.evse && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.ocpi_evse?.evseId || "-"}
										</td>
									)}
									{columns?.cdr && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.ocpi_cdr?.cdrId || "-"}
										</td>
									)}
									{columns?.session && (
										<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
											{session.ocpi_session?.sessionId || "-"}
										</td>
									)}
									<td className="whitespace-nowrap px-6 py-4 text-right font-medium text-sm">
										<button
											className="text-blue-600 hover:text-blue-900"
											onClick={() => {
												// Hier könnte eine Detailansicht implementiert werden
												toast.info(
													`Details für Payment Session ${session.paymentIntent}`,
												);
											}}
										>
											<FaEye />
										</button>
									</td>
								</tr>
							))}

							{paymentSessions.length === 0 && !isLoading && (
								<tr>
									<td
										colSpan={
											Object.values(columns || {}).filter(Boolean).length + 1
										}
										className="px-6 py-4 text-center text-gray-500 text-sm"
									>
										Keine Daten gefunden
									</td>
								</tr>
							)}
						</tbody>
					</table>
				</div>

				{/* Lade-Indikator und "Mehr laden"-Button */}
				<div className="border-gray-200 border-t px-6 py-4">
					{isLoading ? (
						<div className="flex justify-center">
							<Spinner />
						</div>
					) : error ? (
						<div className="text-center text-red-500">{error}</div>
					) : hasMore ? (
						<button
							className="w-full rounded-lg bg-gray-100 py-2 text-gray-700 hover:bg-gray-200"
							onClick={loadMore}
						>
							Mehr laden
						</button>
					) : (
						<div className="text-center text-gray-500 text-sm">
							{paymentSessions.length > 0 ? "Keine weiteren Daten" : ""}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
