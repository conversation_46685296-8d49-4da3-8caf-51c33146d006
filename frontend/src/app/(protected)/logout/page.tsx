"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "~/components/AuthContext";

export default function LogoutPage() {
	const { logout } = useAuth();
	const router = useRouter();

	useEffect(() => {
		// Führe Logout aus
		logout();

		// Weiterleitung zur Login-Seite
		router.push("/login");
	}, [logout, router]);

	return (
		<div className="flex min-h-[60vh] flex-col items-center justify-center">
			<div className="text-center">
				<h1 className="mb-4 font-bold text-2xl text-gray-900">Abmelden...</h1>
				<p className="mb-6 text-gray-600">
					Sie werden abgemeldet und zur Login-Seite weitergeleitet.
				</p>
				<div className="mx-auto h-10 w-10 animate-spin rounded-full border-blue-700 border-b-2" />
			</div>
		</div>
	);
}
