"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Spinner } from "~/components/ui/spinner";
import { type MandantData, locationsApi, mandantsApi } from "~/services/api";

interface MandantZuordnungModalProps {
	standortDocumentId: string;
	currentMandantId?: string;
	onClose: () => void;
	onSuccess: () => void;
}

export default function MandantZuordnungModal({
	standortDocumentId,
	currentMandantId,
	onClose,
	onSuccess,
}: MandantZuordnungModalProps) {
	const [mandanten, setMandanten] = useState<MandantData[]>([]);
	const [selectedMandantId, setSelectedMandantId] = useState<
		string | undefined
	>(currentMandantId);
	const [isLoading, setIsLoading] = useState(true);
	const [isSaving, setIsSaving] = useState(false);
	const [error, setError] = useState<string | null>(null);
	// Keine Filterung nach Berechtigungen mehr notwendig

	// Mandanten laden
	useEffect(() => {
		const fetchMandanten = async () => {
			try {
				const response = await mandantsApi.getAll();
				// Alle Mandanten anzeigen, unabhängig von Berechtigungen
				setMandanten(response.data);
			} catch (err) {
				console.error("Fehler beim Laden der Mandanten:", err);
				setError("Mandanten konnten nicht geladen werden.");
			} finally {
				setIsLoading(false);
			}
		};

		fetchMandanten();
	}, []);

	const handleSave = async () => {
		setIsSaving(true);
		setError(null);

		try {
			// Standort mit neuem Mandanten aktualisieren
			await locationsApi.assignMandant(standortDocumentId, selectedMandantId);

			onSuccess();
		} catch (err) {
			console.error("Fehler beim Zuordnen des Mandanten:", err);
			setError(
				"Mandant konnte nicht zugeordnet werden. Bitte versuchen Sie es später erneut.",
			);
		} finally {
			setIsSaving(false);
		}
	};

	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
			<div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
				<h2 className="mb-4 font-semibold text-xl">Mandant zuordnen</h2>

				{error && (
					<div className="mb-4 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
						{error}
					</div>
				)}

				{isLoading ? (
					<div className="flex justify-center py-8">
						<Spinner size="lg" />
					</div>
				) : (
					<div className="space-y-4">
						<div>
							<label
								htmlFor="mandant"
								className="mb-1 block font-medium text-gray-700 text-sm"
							>
								Mandant
							</label>
							<select
								id="mandant"
								value={selectedMandantId || ""}
								onChange={(e) => setSelectedMandantId(e.target.value)}
								className="flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm"
								disabled={isSaving}
							>
								<option value="">Keinen Mandanten zuordnen</option>
								{mandanten.map((mandant) => (
									<option key={mandant.documentId} value={mandant.documentId}>
										{mandant.name}
									</option>
								))}
							</select>
						</div>

						<div className="flex justify-end space-x-2 pt-4">
							<Button
								type="button"
								variant="outline"
								onClick={onClose}
								disabled={isSaving}
							>
								Abbrechen
							</Button>
							<Button type="button" onClick={handleSave} disabled={isSaving}>
								{isSaving ? (
									<>
										<Spinner size="sm" className="mr-2" />
										Speichern...
									</>
								) : (
									"Speichern"
								)}
							</Button>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
