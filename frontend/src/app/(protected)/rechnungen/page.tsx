"use client";

import React, { useState, useEffect, useCallback } from "react";
import { FiDownload, FiFileText, FiRefreshCw, FiSearch } from "react-icons/fi";
import { toast } from "react-toastify";
import { useMandant } from "~/components/MandantContext";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Spinner } from "~/components/ui/spinner";
import { formatDateTime } from "~/lib/utils";
import { invoiceApi, type InvoiceData } from "~/services/api";

export default function RechnungenPage() {
	const { activeMandant } = useMandant();
	const [invoices, setInvoices] = useState<InvoiceData[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [page, setPage] = useState(1);
	const [hasMore, setHasMore] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [generatingPdf, setGeneratingPdf] = useState<string | null>(null);

	// Debounced search
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 500);
		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Rechnungen laden
	const fetchInvoices = useCallback(
		async (resetPage = false) => {
			try {
				setIsLoading(true);
				setError(null);

				const currentPage = resetPage ? 1 : page;
				if (resetPage) {
					setPage(1);
					setInvoices([]);
				}

				// Filterbedingungen aufbauen
				const params: Record<string, any> = {
					sort: "createdAt:desc",
					"pagination[page]": currentPage,
					"pagination[pageSize]": 20,
					populate: {
						mandant: {
							fields: ["name"],
						},
						payment_session: {
							fields: ["paymentIntent"],
						},
						ocpi_session: {
							fields: ["sessionId"],
						},
						file: {
							fields: ["name", "url"],
						},
					},
				};

				// Suchfilter
				if (debouncedSearchTerm) {
					params["filters[$or][0][invoice_number][$containsi]"] =
						debouncedSearchTerm;
					params["filters[$or][1][payment_session][paymentIntent][$containsi]"] =
						debouncedSearchTerm;
					params["filters[$or][2][ocpi_session][sessionId][$containsi]"] =
						debouncedSearchTerm;
				}

				// Status-Filter
				if (statusFilter) {
					params["filters[invoice_status][$eq]"] = statusFilter;
				}

				// Mandantfilter
				if (activeMandant && activeMandant.documentId !== "root") {
					params["filters[mandant][documentId][$eq]"] =
						activeMandant.documentId;
				}

				const response = await invoiceApi.getAll(params);

				if (resetPage) {
					setInvoices(response.data);
				} else {
					setInvoices((prev) => [...prev, ...response.data]);
				}

				// Prüfen, ob noch mehr Daten verfügbar sind
				setHasMore(
					response.meta.pagination.page < response.meta.pagination.pageCount,
				);
			} catch (err) {
				console.error("Error fetching invoices:", err);
				setError("Fehler beim Laden der Rechnungen");
				toast.error("Fehler beim Laden der Rechnungen");
			} finally {
				setIsLoading(false);
			}
		},
		[page, debouncedSearchTerm, statusFilter, activeMandant],
	);

	// Initial load und bei Filter-Änderungen
	useEffect(() => {
		fetchInvoices(true);
	}, [debouncedSearchTerm, statusFilter, activeMandant]);

	// Mehr laden
	const loadMore = () => {
		if (!isLoading && hasMore) {
			setPage((prev) => prev + 1);
		}
	};

	useEffect(() => {
		if (page > 1) {
			fetchInvoices();
		}
	}, [page]);

	// PDF herunterladen
	const handleDownloadPdf = async (invoice: InvoiceData) => {
		try {
			const blob = await invoiceApi.downloadPdf(invoice.documentId);
			const url = window.URL.createObjectURL(blob);
			const link = document.createElement("a");
			link.href = url;
			link.download = `Rechnung_${invoice.invoice_number || invoice.documentId}.pdf`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			window.URL.revokeObjectURL(url);
			toast.success("PDF erfolgreich heruntergeladen");
		} catch (error) {
			console.error("Error downloading PDF:", error);
			toast.error("Fehler beim Herunterladen der PDF");
		}
	};

	// PDF generieren
	const handleGeneratePdf = async (invoice: InvoiceData) => {
		try {
			setGeneratingPdf(invoice.documentId);
			const response = await invoiceApi.generatePdf(invoice.documentId);
			if(response.ok)
			{
				toast.success("PDF erfolgreich generiert");
			}
			else
			{
				const message = response?.error?.message  ?? "Fehler beim Generieren der PDF";
				toast.error(message);
			}
			console.log(response)

			// Rechnung neu laden um den PDF-Status zu aktualisieren
			fetchInvoices(true);
		} catch (error) {
			let errorMessage = "Fehler beim Generieren der PDF";

			if (error instanceof Error) {
				// Prüfen ob es ein Axios Error ist
				if ('response' in error && error.response) {
					const axiosError = error as any;
					// Server Response Message verwenden falls verfügbar
					if (axiosError.response.data?.message) {
						errorMessage = axiosError.response.data.message;
					} else if (axiosError.response.data?.error) {
						errorMessage = axiosError.response.data.error;
					} else if (axiosError.message) {
						errorMessage = axiosError.message;
					}
				}
			}
			toast.error(errorMessage);


		} finally {
			setGeneratingPdf(null);
		}
	};

	// Status-Badge rendern
	const renderStatusBadge = (status: string) => {
		const statusConfig = {
			DRAFT: { label: "Entwurf", color: "bg-gray-100 text-gray-800" },
			INMUTABLE_WRITTEN: { label: "Geschrieben", color: "bg-blue-100 text-blue-800" },
			PAID: { label: "Bezahlt", color: "bg-green-100 text-green-800" },
		};

		const config = statusConfig[status as keyof typeof statusConfig] || {
			label: status,
			color: "bg-gray-100 text-gray-800",
		};

		return (
			<span
				className={`inline-flex rounded-full px-2 font-semibold text-xs leading-5 ${config.color}`}
			>
				{config.label}
			</span>
		);
	};

	// Art-Badge rendern
	const renderKindBadge = (kind: string) => {
		const kindConfig = {
			INVOICE: { label: "Rechnung", color: "bg-gray-100 text-gray-800" },
			CREDIT: { label: "Gutschrift", color: "bg-green-100 text-green-800" },
			STORNO: { label: "Storno", color: "bg-red-100 text-red-800" },
			CREDIT_STORNO: { label: "Gutschrift Storno", color: "bg-orange-100 text-orange-800" },
		};

		const config = kindConfig[kind as keyof typeof kindConfig] || {
			label: kind,
			color: "bg-gray-100 text-gray-800",
		};

		return (
			<span
				className={`inline-flex rounded-full px-2 font-semibold text-xs leading-5 ${config.color}`}
			>
				{config.label}
			</span>
		);
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl text-gray-900">Rechnungen</h1>
				<Button
					onClick={() => fetchInvoices(true)}
					variant="outline"
					className="flex items-center"
					disabled={isLoading}
				>
					<FiRefreshCw className={`mr-2 ${isLoading ? "animate-spin" : ""}`} />
					Aktualisieren
				</Button>
			</div>

			{/* Filter */}
			<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
				<div className="relative">
					<FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
					<Input
						type="text"
						placeholder="Rechnungsnummer, Payment Intent oder Session ID suchen..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="pl-10"
					/>
				</div>
				<select
					value={statusFilter}
					onChange={(e) => setStatusFilter(e.target.value)}
					className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
				>
					<option value="">Alle Status</option>
					<option value="DRAFT">Entwurf</option>
					<option value="INMUTABLE_WRITTEN">Geschrieben</option>
					<option value="PAID">Bezahlt</option>
				</select>
			</div>

			{/* Tabelle */}
			{error ? (
				<div className="rounded-md bg-red-50 p-4">
					<div className="text-red-700 text-sm">{error}</div>
				</div>
			) : (
				<div className="overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Rechnungsnummer
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Art
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Status
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Betrag
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Zeitraum
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Mandant
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Erstellt
									</th>
									<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
										Aktionen
									</th>
								</tr>
							</thead>
							<tbody className="divide-y divide-gray-200 bg-white">
								{invoices.length === 0 && !isLoading ? (
									<tr>
										<td
											colSpan={8}
											className="px-6 py-4 text-center text-gray-500"
										>
											Keine Rechnungen gefunden.
										</td>
									</tr>
								) : (
									invoices.map((invoice) => (
										<tr key={invoice.documentId} className="hover:bg-gray-50">
											<td className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 text-sm">
												{invoice.invoice_number || "-"}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-sm">
												{renderKindBadge(invoice.kindOfInvoice)}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-sm">
												{renderStatusBadge(invoice.invoice_status)}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-gray-900 text-sm">
												€{invoice.sum_gross.toFixed(2)}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
												{new Date(invoice.period_start_utc).toLocaleDateString()} -{" "}
												{new Date(invoice.period_end_utc).toLocaleDateString()}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
												{invoice.mandant?.name || "-"}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
												{formatDateTime(invoice.createdAt)}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-sm">
												<div className="flex items-center space-x-2">
													{invoice.file_path? (
														<Button
															onClick={() => handleDownloadPdf(invoice)}
															variant="outline"
															size="sm"
															className="flex items-center"
														>
															<FiDownload className="mr-1 h-3 w-3" />
															Download
														</Button>
													) : (
														<Button
															onClick={() => handleGeneratePdf(invoice)}
															variant="outline"
															size="sm"
															className="flex items-center"
															disabled={generatingPdf === invoice.documentId}
														>
															{generatingPdf === invoice.documentId ? (
																<Spinner size="sm" className="mr-1" />
															) : (
																<FiFileText className="mr-1 h-3 w-3" />
															)}
															PDF erstellen
														</Button>
													)}
												</div>
											</td>
										</tr>
									))
								)}
							</tbody>
						</table>
					</div>

					{/* Mehr laden Button */}
					{hasMore && (
						<div className="border-t border-gray-200 bg-gray-50 px-6 py-3">
							<Button
								onClick={loadMore}
								variant="outline"
								className="w-full"
								disabled={isLoading}
							>
								{isLoading ? (
									<>
										<Spinner size="sm" className="mr-2" />
										Lädt...
									</>
								) : (
									"Mehr laden"
								)}
							</Button>
						</div>
					)}
				</div>
			)}
		</div>
	);
}
