// frontend/src/app/[mandant]/session/[paymentIntent]/page.tsx
"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import type React from "react";
import { useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { publicApiService } from "~/services/public-api";

interface PaymentSession {
	documentId: string;
	paymentIntent: string;
	state: "started" | "authorized" | "captured" | "canceled" | "error";
	blockedAmount: number;
	capturedAmount: number;
	cardId: string;
	maskedPan: string;
	brand: string;
	authorizedAt: string;
	costumerEmailForInvoice?: string;
	terminal: {
		documentId: string;
		serialNumber: string;
		terminalName: string;
	};
	ocpi_evse: {
		documentId: string;
		evseId: string;
		labelForTerminal: string;
	};
	ocpi_session: {
		documentId: string;
		sessionId: string;
		startTime: string;
		endTime: string | null;
		kwh: number;
		totalCost: number;
		currency: string;
		ocpiStatus: "ACTIVE" | "COMPLETED" | "INVALID" | "PENDING" | "RESERVATION";
	} | null;
	mandant: {
		documentId: string;
		name: string;
		logo?: {
			url: string;
			width: number;
			height: number;
		};
	};
	invoice?: {
		documentId: string;
		invoice_number?: string;
		invoice_status: "DRAFT" | "INMUTABLE_WRITTEN" | "PAID";
	} | null;
}

interface TariffInfo {
	pricePerKwh: number;
	sessionFee: number;
	perMinute: number;
	maxBlockFee: number;
	gracePeriod: number;
}

interface ApiResponse {
	paymentSession: PaymentSession;
	tariff: TariffInfo | null;
	currentPrice: number | null;
}

export default function PaymentSessionDetailPage() {
	const params = useParams();
	const router = useRouter();
	const paymentIntent = params.paymentIntent as string;
	const mandantSlug = params.mandant as string;

	const [session, setSession] = useState<PaymentSession | null>(null);
	const [tariffInfo, setTariffInfo] = useState<TariffInfo | null>(null);
	const [currentPrice, setCurrentPrice] = useState<number | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [email, setEmail] = useState("");
	const [emailSubmitted, setEmailSubmitted] = useState(false);
	const [checkingInvoice, setCheckingInvoice] = useState(false);
	const [invoiceAvailable, setInvoiceAvailable] = useState(false);
	const [countdown, setCountdown] = useState(10.0);
	const [showSpinner, setShowSpinner] = useState(false);

	// Ref für die URL-Kopier-Funktionalität
	const pageUrlRef = useRef<HTMLInputElement>(null);

	// Funktion zum Laden der Payment-Session-Daten
	const fetchPaymentSession = async () => {
		try {
			setLoading(true);
			const data = await publicApiService.getPaymentSession(paymentIntent);

			if (data?.paymentSession) {
				// Session-Daten setzen
				setSession(data.paymentSession);

				// Aktuellen Preis setzen, falls vorhanden
				if (data.currentPrice != null) {
					setCurrentPrice(data.currentPrice);
				}

				// Tarif-Informationen setzen, falls vorhanden
				if (data.tariff) {
					setTariffInfo({
						pricePerKwh: data.tariff.pricePerKwh || 0,
						sessionFee: data.tariff.sessionFee || 0,
						perMinute: data.tariff.perMinute || 0,
						maxBlockFee: data.tariff.maxBlockFee || 0,
						gracePeriod: data.tariff.gracePeriod || 0,
					});
				} else {
					setTariffInfo(null);
				}

				// Prüfen, ob eine E-Mail bereits gespeichert ist
				const savedEmail = data.paymentSession.costumerEmailForInvoice;
				if (savedEmail) {
					// Die E-Mail ist bereits maskiert (z.B. m********n@e*****e.com)
					setEmail(savedEmail);
					setEmailSubmitted(true);
				} else {
					setEmailSubmitted(false);
				}

				// Prüfen, ob eine Rechnung verfügbar ist
				const hasInvoice = !!data.paymentSession.invoice;
				const invoiceStatus =
					hasInvoice && data.paymentSession.invoice
						? data.paymentSession.invoice.invoice_status
						: null;
				setInvoiceAvailable(
					hasInvoice && invoiceStatus && invoiceStatus !== "DRAFT",
				);
			} else {
				setError("Keine Daten für diese Session gefunden");
			}
		} catch (err) {
			console.error("Fehler beim Laden der Payment-Session:", err);
			setError(
				"Fehler beim Laden der Daten. Bitte versuchen Sie es später erneut.",
			);
		} finally {
			setLoading(false);
		}
	};

	// Polling-Funktion zum Prüfen der Rechnungsverfügbarkeit
	const checkInvoiceAvailability = async () => {
		// Nur prüfen, wenn die Session vorhanden ist und noch keine Rechnung verfügbar ist
		// Oder wenn die Session abgeschlossen ist (COMPLETED) aber noch keine Rechnung verfügbar ist
		if (!session) {
			setShowSpinner(false);
			return; // Keine Session vorhanden
		}

		if (invoiceAvailable) {
			setShowSpinner(false);
			return; // Rechnung bereits verfügbar
		}

		// Prüfen, ob die Session noch aktiv ist oder bereits abgeschlossen
		const isSessionActive = session.ocpi_session?.ocpiStatus === "ACTIVE";
		const isSessionCompleted = session.ocpi_session?.ocpiStatus === "COMPLETED";
		const isSessionCaptured = session.state === "captured";

		// Wenn die Session weder aktiv noch abgeschlossen ist, nicht prüfen
		if (!isSessionActive && !isSessionCompleted && !isSessionCaptured) {
			setShowSpinner(false);
			return;
		}

		try {
			setCheckingInvoice(true);
			// Verwende den speziellen Endpunkt zum Prüfen der Rechnungsverfügbarkeit
			const data =
				await publicApiService.checkInvoiceAvailability(paymentIntent);

			if (data?.paymentSession) {
				// Session-Daten aktualisieren, aber nur die relevanten Felder
				setSession((prev) => {
					if (!prev) return data.paymentSession;

					return {
						...prev,
						state: data.paymentSession.state || prev.state,
						invoice: data.paymentSession.invoice || prev.invoice,
						// Stelle sicher, dass alle vorherigen Daten erhalten bleiben, die nicht im Update enthalten sind
						ocpi_session:
							prev.ocpi_session && data.paymentSession.ocpi_session
								? {
										...prev.ocpi_session,
										...data.paymentSession.ocpi_session,
									}
								: prev.ocpi_session || data.paymentSession.ocpi_session,
						terminal: prev.terminal || data.paymentSession.terminal,
						ocpi_evse: prev.ocpi_evse || data.paymentSession.ocpi_evse,
						mandant: prev.mandant || data.paymentSession.mandant,
					};
				});

				// Aktuellen Preis aktualisieren, falls vorhanden
				if (data.currentPrice != null) {
					setCurrentPrice(data.currentPrice);
				}

				// Tarif-Informationen aktualisieren, falls vorhanden
				if (data.tariff) {
					setTariffInfo({
						pricePerKwh: data.tariff.pricePerKwh || 0,
						sessionFee: data.tariff.sessionFee || 0,
						perMinute: data.tariff.perMinute || 0,
						maxBlockFee: data.tariff.maxBlockFee || 0,
						gracePeriod: data.tariff.gracePeriod || 0,
					});
				}

				// Prüfen, ob eine Rechnung verfügbar ist
				const newInvoiceAvailable = data.invoiceAvailable === true;

				if (newInvoiceAvailable !== invoiceAvailable) {
					setInvoiceAvailable(newInvoiceAvailable);

					// Wenn eine Rechnung neu verfügbar ist, Benachrichtigung anzeigen
					if (newInvoiceAvailable) {
						toast.success("Die Rechnung ist jetzt verfügbar!");
					}
				}
			}
		} catch (err) {
			console.error("Fehler beim Prüfen der Rechnungsverfügbarkeit:", err);
		} finally {
			// Prüfung abgeschlossen, Countdown zurücksetzen
			setCheckingInvoice(false);
			// Wenn keine Rechnung verfügbar ist, Countdown zurücksetzen
			if (!invoiceAvailable) {
				setCountdown(10.0);
			}
		}
	};

	// Initial laden
	useEffect(() => {
		fetchPaymentSession();
	}, [paymentIntent]);

	// Countdown-Timer für die Rechnungsprüfung
	useEffect(() => {
		// Nur starten, wenn die Session vorhanden ist und noch keine Rechnung verfügbar ist
		if (!session) {
			return; // Keine Session vorhanden
		}

		if (invoiceAvailable) {
			return; // Rechnung bereits verfügbar
		}

		if (checkingInvoice) {
			return; // Bereits am Prüfen
		}

		// Prüfen, ob die Session noch aktiv ist oder bereits abgeschlossen
		const isSessionActive = session.ocpi_session?.ocpiStatus === "ACTIVE";
		const isSessionCompleted = session.ocpi_session?.ocpiStatus === "COMPLETED";
		const isSessionCaptured = session.state === "captured";

		// Wenn die Session weder aktiv noch abgeschlossen ist, nicht prüfen
		if (!isSessionActive && !isSessionCompleted && !isSessionCaptured) {
			return;
		}

		// Wenn der Spinner angezeigt wird oder gerade eine Prüfung läuft, keinen Countdown starten
		if (showSpinner || checkingInvoice) {
			return;
		}

		// Countdown-Timer starten - mit 100ms Intervall für flüssigere Animation
		const countdownId = setInterval(() => {
			setCountdown((prevCount) => {
				// Wenn der Countdown bei 0 angekommen ist, Spinner anzeigen und Intervall stoppen
				if (prevCount <= 0.1) {
					// Spinner anzeigen
					setShowSpinner(true);

					// Nach 2 Sekunden Spinner ausblenden und Prüfung starten
					setTimeout(() => {
						// Prüfung starten
						checkInvoiceAvailability();
						// Spinner ausblenden
						setShowSpinner(false);
					}, 2000);

					// Countdown zurücksetzen
					return 10.0;
				}
				// Countdown dekrementieren (0.1 Sekunden pro Schritt)
				return prevCount - 0.1;
			});
		}, 100); // Alle 100ms aktualisieren für flüssigere Animation

		// Cleanup beim Unmount
		return () => clearInterval(countdownId);
	}, [session, invoiceAvailable, checkingInvoice, showSpinner]);

	const handleEmailSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!email || !email.includes("@")) {
			toast.error("Bitte geben Sie eine gültige E-Mail-Adresse ein.");
			return;
		}

		if (!session) {
			toast.error("Keine aktive Session gefunden.");
			return;
		}

		try {
			// API-Anfrage, um die E-Mail zu speichern
			await publicApiService.savePaymentSessionEmail(session.documentId, email);

			toast.success(
				"E-Mail erfolgreich gespeichert. Die Rechnung wird nach Abschluss des Ladevorgangs zugesandt.",
			);
			setEmailSubmitted(true);

			// Session-Daten aktualisieren, um die gespeicherte E-Mail anzuzeigen
			fetchPaymentSession();
		} catch (error) {
			console.error("Fehler beim Speichern der E-Mail:", error);
			toast.error(
				"Die E-Mail konnte nicht gespeichert werden. Bitte versuchen Sie es später erneut.",
			);
		}
	};

	const copyPageUrl = () => {
		if (pageUrlRef.current) {
			pageUrlRef.current.select();
			document.execCommand("copy");
			toast.success("Link wurde in die Zwischenablage kopiert.");
		}
	};

	if (loading) {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="h-12 w-12 animate-spin rounded-full border-blue-500 border-t-2 border-b-2" />
			</div>
		);
	}

	if (error) {
		return (
			<div className="m-4 border-red-500 border-l-4 bg-red-50 p-4">
				<div className="flex">
					<div className="flex-shrink-0">
						<svg
							className="h-5 w-5 text-red-500"
							viewBox="0 0 20 20"
							fill="currentColor"
						>
							<path
								fillRule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
								clipRule="evenodd"
							/>
						</svg>
					</div>
					<div className="ml-3">
						<p className="text-red-700 text-sm">{error}</p>
					</div>
				</div>
			</div>
		);
	}

	if (!session) {
		return (
			<div className="container mx-auto px-4 py-8">
				<div className="border-yellow-500 border-l-4 bg-yellow-50 p-4">
					<div className="flex">
						<div className="flex-shrink-0">
							<svg
								className="h-5 w-5 text-yellow-500"
								viewBox="0 0 20 20"
								fill="currentColor"
							>
								<path
									fillRule="evenodd"
									d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
									clipRule="evenodd"
								/>
							</svg>
						</div>
						<div className="ml-3">
							<p className="text-sm text-yellow-700">
								Ladevorgang nicht gefunden oder nicht mehr verfügbar.
							</p>
						</div>
					</div>
				</div>
				<button
					onClick={() => router.back()}
					className="mt-4 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
				>
					Zurück
				</button>
			</div>
		);
	}

	// Bestimme, ob die Session aktiv ist
	const isActive = session?.ocpi_session?.ocpiStatus === "ACTIVE";
	const pageUrl = typeof window !== "undefined" ? window.location.href : "";

	// Bookmark-Link Abschnitt (überarbeitete Version)
	const handleAddBookmark = () => {
		const title = "Ladevorgang Details";
		const url = window.location.href;

		// Moderne Browser unterstützen das programmgesteuerte Hinzufügen zu Favoriten nicht direkt
		// Wir bieten stattdessen eine Anleitung für den Benutzer

		// @ts-ignore - Alte Browser-APIs
		if (window.sidebar?.addPanel) {
			// Firefox < 23
			// @ts-ignore - Alte Browser-APIs
			window.sidebar.addPanel(title, url, "");
		} else if (window.external && "AddFavorite" in window.external) {
			// IE
			// @ts-ignore - Alte Browser-APIs
			window.external.AddFavorite(url, title);
		} else {
			// Moderne Browser - Zeige Anweisungen an
			const isMac = navigator.platform.indexOf("Mac") !== -1;
			const keyCmd = isMac ? "⌘+D" : "Strg+D";

			toast(
				`Drücken Sie ${keyCmd}, um diese Seite zu Ihren Lesezeichen hinzuzufügen.`,
			);
		}
	};

	const showToast = (message: string) => {
		toast(message, {
			position: "top-right",
			autoClose: 5000,
			hideProgressBar: false,
			closeOnClick: true,
			pauseOnHover: true,
			draggable: true,
			progress: undefined,
		});
	};

	return (
		<div className="container mx-auto px-4 py-8">
			{/* Header mit Mandanten-Logo */}
			<div className="mb-8 flex items-center justify-between">
				<h1 className="font-bold text-2xl text-gray-900">
					Ladevorgang Details
				</h1>
				{session.mandant?.logo?.url && (
					<img
						src={`${process.env.NEXT_PUBLIC_STRAPI_API_URL}${session.mandant.logo.url}`}
						alt={`Logo ${session.mandant.name}`}
						className="h-12 object-contain"
					/>
				)}
			</div>

			{/* Status-Banner */}
			<div
				className={`mb-6 rounded-md p-4 ${isActive ? "bg-blue-50" : invoiceAvailable ? "bg-green-50" : "bg-yellow-50"}`}
			>
				<div className="flex">
					<div className="flex-shrink-0">
						{isActive ? (
							<svg
								className="h-5 w-5 text-blue-400"
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 20 20"
								fill="currentColor"
							>
								<path
									fillRule="evenodd"
									d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
									clipRule="evenodd"
								/>
							</svg>
						) : invoiceAvailable ? (
							<svg
								className="h-5 w-5 text-green-400"
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 20 20"
								fill="currentColor"
							>
								<path
									fillRule="evenodd"
									d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
									clipRule="evenodd"
								/>
							</svg>
						) : (
							<svg
								className="h-5 w-5 text-yellow-400"
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 20 20"
								fill="currentColor"
							>
								<path
									fillRule="evenodd"
									d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
									clipRule="evenodd"
								/>
							</svg>
						)}
					</div>
					<div className="ml-3">
						<h3
							className={`font-medium text-sm ${isActive ? "text-blue-800" : invoiceAvailable ? "text-green-800" : "text-yellow-800"}`}
						>
							{isActive
								? "Ladevorgang aktiv"
								: invoiceAvailable
									? "Rechnung verfügbar"
									: "Warte auf Rechnung"}
						</h3>
						<div
							className={`mt-2 text-sm ${isActive ? "text-blue-700" : invoiceAvailable ? "text-green-700" : "text-yellow-700"}`}
						>
							<p>
								{isActive
									? "Der Ladevorgang läuft noch. Die Rechnung wird nach Abschluss hier verfügbar sein."
									: invoiceAvailable
										? "Der Ladevorgang ist abgeschlossen. Sie können die Rechnung jetzt herunterladen."
										: "Der Ladevorgang ist abgeschlossen. Die Rechnung wird in Kürze erstellt und hier verfügbar sein."}
							</p>
							{checkingInvoice && !invoiceAvailable && !isActive && (
								<p className="mt-2 flex items-center">
									<span className="mr-2 inline-block h-3 w-3 animate-pulse rounded-full bg-yellow-400" />
									Prüfe Rechnungsverfügbarkeit...
								</p>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Rechnung herunterladen */}
			{!isActive && (
				<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
					<div className="px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg leading-6">
							Rechnung
						</h3>
						<p className="mt-1 max-w-2xl text-gray-500 text-sm">
							{invoiceAvailable
								? "Laden Sie Ihre Rechnung herunter"
								: "Die Rechnung wird in Kürze erstellt"}
						</p>
					</div>
					<div className="border-gray-200 border-t px-4 py-5">
						{invoiceAvailable ? (
							<button
								type="button"
								className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
								onClick={() => {
									// Hier die Rechnung herunterladen
									if (session?.invoice?.documentId) {
										// Verwende die Invoice-ID für den Download
										const apiUrl =
											process.env.NEXT_PUBLIC_STRAPI_API_URL ||
											"http://localhost:1337";
										window.open(
											`${apiUrl}/api/invoices/download/${session.invoice.documentId}`,
											"_blank",
										);
										toast.success("Rechnung wird heruntergeladen...");
									} else {
										toast.error("Rechnung nicht gefunden");
									}
								}}
							>
								<svg
									className="-ml-1 mr-2 h-5 w-5"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
									fill="currentColor"
								>
									<path
										fillRule="evenodd"
										d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
										clipRule="evenodd"
									/>
								</svg>
								Rechnung herunterladen
							</button>
						) : (
							<div className="flex items-center">
								<div className="mr-4 flex-shrink-0 self-center">
									<svg
										className="h-8 w-8 animate-pulse text-yellow-400"
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
								</div>
								<div>
									<p className="font-medium text-gray-900 text-sm">
										Rechnung wird erstellt
									</p>
									<div className="text-gray-500 text-sm">
										<p>
											Die Rechnung wird automatisch erstellt und in Kürze hier
											verfügbar sein.
										</p>
										{checkingInvoice ? (
											<p className="mt-2">
												<span className="text-yellow-600">
													Prüfe Verfügbarkeit...
												</span>
											</p>
										) : showSpinner ? (
											<div className="mt-2 flex items-center">
												<svg
													className="-ml-1 mr-2 h-4 w-4 animate-spin text-blue-600"
													xmlns="http://www.w3.org/2000/svg"
													fill="none"
													viewBox="0 0 24 24"
												>
													<circle
														className="opacity-25"
														cx="12"
														cy="12"
														r="10"
														stroke="currentColor"
														strokeWidth="4"
													/>
													<path
														className="opacity-75"
														fill="currentColor"
														d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
													/>
												</svg>
												<span className="text-blue-600">Frage Server...</span>
											</div>
										) : (
											<p className="mt-2">
												<span className="text-blue-600">
													Nächste Prüfung in{" "}
													{countdown.toFixed(1).replace(".", ",")} Sekunden
												</span>
											</p>
										)}
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			)}

			{/* Ladevorgang-Informationen */}
			<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
				<div className="px-4 py-5 sm:px-6">
					<h3 className="font-medium text-gray-900 text-lg leading-6">
						Ladevorgang Details
					</h3>
					<p className="mt-1 max-w-2xl text-gray-500 text-sm">
						Details zum aktuellen Ladevorgang
					</p>
				</div>
				<div className="border-gray-200 border-t">
					<dl>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">Ladepunkt</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{session?.ocpi_evse?.labelForTerminal ||
									session?.ocpi_evse?.evseId ||
									"Unbekannt"}
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">Terminal</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{session?.terminal?.terminalName ||
									session?.terminal?.serialNumber ||
									"Unbekannt"}
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">Startzeit</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{session?.ocpi_session?.startTime
									? new Date(session.ocpi_session.startTime).toLocaleString(
											"de-DE",
										)
									: "Unbekannt"}
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">Endzeit</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{session?.ocpi_session?.endTime
									? new Date(session.ocpi_session.endTime).toLocaleString(
											"de-DE",
										)
									: "Noch laufend"}
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Geladene Energie
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								<div>
									{session?.ocpi_session?.kwh != null
										? `${Number(session.ocpi_session.kwh).toFixed(2)} kWh*`
										: "0 kWh*"}
								</div>
								<div className="mt-1 text-gray-500 text-xs">
									* Die angezeigte Energiemenge wird nicht in Echtzeit
									übertragen und kann abweichungen aufweisen. Am Ende des
									Ladevorgang wird die exakte Summe über geeichte Zähler
									ausgelesen.
								</div>
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Kosten bisher
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{currentPrice != null
									? `${(currentPrice / 100).toFixed(2)} EUR`
									: session?.ocpi_session?.totalCost != null
										? `${Number(session.ocpi_session.totalCost).toFixed(2)} ${session.ocpi_session.currency || "EUR"}`
										: "0.00 EUR"}
								<div className="mt-1 text-gray-500 text-xs">
									* Die angezeigten Kosten werden nicht in Echtzeit übertragen
									und können abweichungen aufweisen. Am Ende des Ladevorgang
									wird die exakte Summe über geeichte Zähler ausgelesen.
								</div>
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Zahlungsinformationen
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{session?.brand && (
									<p>
										Karte: {session.brand}{" "}
										{session.maskedPan
											? `••••${session.maskedPan.slice(-4)}`
											: ""}
									</p>
								)}
								{session?.blockedAmount != null && (
									<p>
										Reservierter Betrag:{" "}
										{(session.blockedAmount / 100).toFixed(2)} EUR
									</p>
								)}
								{session?.state === "captured" &&
									session?.capturedAmount != null && (
										<p>
											Abgerechneter Betrag:{" "}
											{(session.capturedAmount / 100).toFixed(2)} EUR
										</p>
									)}
							</dd>
						</div>
					</dl>
				</div>
			</div>

			{/* Tarif-Informationen */}
			{tariffInfo && (
				<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
					<div className="px-4 py-5 sm:px-6">
						<h3 className="font-medium text-gray-900 text-lg leading-6">
							Tarif-Informationen
						</h3>
						<p className="mt-1 max-w-2xl text-gray-500 text-sm">
							Details zum angewendeten Tarif
						</p>
					</div>
					<div className="border-gray-200 border-t">
						<dl>
							<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
								<dt className="font-medium text-gray-500 text-sm">
									Preis pro kWh
								</dt>
								<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
									{((tariffInfo.pricePerKwh || 0) / 100).toFixed(2)} EUR
								</dd>
							</div>
							{(tariffInfo.sessionFee || 0) > 0 && (
								<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
									<dt className="font-medium text-gray-500 text-sm">
										Startgebühr
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{((tariffInfo.sessionFee || 0) / 100).toFixed(2)} EUR
									</dd>
								</div>
							)}
							{(tariffInfo.perMinute || 0) > 0 && (
								<>
									<div
										className={`${(tariffInfo.sessionFee || 0) > 0 ? "bg-gray-50" : "bg-white"} px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6`}
									>
										<dt className="font-medium text-gray-500 text-sm">
											Blockiergebühr
										</dt>
										<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
											{((tariffInfo.perMinute || 0) / 100).toFixed(2)} EUR/min
											(nach {tariffInfo.gracePeriod || 0} Stunden)
										</dd>
									</div>
									<div
										className={`${(tariffInfo.sessionFee || 0) > 0 ? "bg-white" : "bg-gray-50"} px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6`}
									>
										<dt className="font-medium text-gray-500 text-sm">
											Max. Blockiergebühr
										</dt>
										<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
											{((tariffInfo.maxBlockFee || 0) / 100).toFixed(2)} EUR
										</dd>
									</div>
								</>
							)}
						</dl>
					</div>
				</div>
			)}

			{/* E-Mail-Benachrichtigung */}
			<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
				<div className="px-4 py-5 sm:px-6">
					<h3 className="font-medium text-gray-900 text-lg leading-6">
						Rechnung per E-Mail erhalten
					</h3>
					<p className="mt-1 max-w-2xl text-gray-500 text-sm">
						{emailSubmitted
							? "Die Rechnung wird nach Abschluss des Ladevorgangs an die hinterlegte E-Mail-Adresse gesendet"
							: "Geben Sie Ihre E-Mail-Adresse an, um die Rechnung nach Abschluss des Ladevorgangs zu erhalten"}
					</p>
				</div>
				<div className="border-gray-200 border-t px-4 py-5">
					{emailSubmitted ? (
						<div className="border-green-400 border-l-4 bg-green-50 p-4">
							<div className="flex">
								<div className="flex-shrink-0">
									<svg
										className="h-5 w-5 text-green-400"
										xmlns="http://www.w3.org/2000/svg"
										viewBox="0 0 20 20"
										fill="currentColor"
									>
										<path
											fillRule="evenodd"
											d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
								<div className="ml-3">
									<p className="text-green-700 text-sm">
										<strong>E-Mail-Adresse hinterlegt:</strong> {email}
										<br />
										Die Rechnung wird nach Abschluss des Ladevorgangs an diese
										Adresse gesendet.
									</p>
								</div>
							</div>
						</div>
					) : (
						<form
							onSubmit={handleEmailSubmit}
							className="sm:flex sm:items-center"
						>
							<div className="w-full sm:max-w-xs">
								<label htmlFor="email" className="sr-only">
									E-Mail-Adresse
								</label>
								<input
									type="email"
									name="email"
									id="email"
									className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
									placeholder="<EMAIL>"
									value={email}
									onChange={(e) => setEmail(e.target.value)}
									required
								/>
							</div>
							<button
								type="submit"
								className="mt-3 inline-flex w-full items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
							>
								Speichern
							</button>
						</form>
					)}
				</div>
			</div>

			{/* Bookmark-Link (überarbeitete Version) */}
			<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
				<div className="px-4 py-5 sm:px-6">
					<h3 className="font-medium text-gray-900 text-lg leading-6">
						Seite speichern
					</h3>
					<p className="mt-1 max-w-2xl text-gray-500 text-sm">
						Speichern Sie diese Seite als Lesezeichen oder kopieren Sie den Link
					</p>
				</div>
				<div className="border-gray-200 border-t px-4 py-5">
					<div className="sm:flex sm:items-center">
						<div className="w-full">
							<input
								ref={pageUrlRef}
								type="text"
								className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
								value={pageUrl}
								readOnly
							/>
						</div>
						<div className="mt-3 flex sm:mt-0 sm:ml-3">
							<button
								type="button"
								onClick={copyPageUrl}
								className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
							>
								<svg
									className="-ml-0.5 mr-2 h-4 w-4"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
									fill="currentColor"
								>
									<path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
									<path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
								</svg>
								Kopieren
							</button>
							<button
								type="button"
								onClick={handleAddBookmark}
								className="ml-2 inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
							>
								<svg
									className="-ml-0.5 mr-2 h-4 w-4"
									xmlns="http://www.w3.org/2000/svg"
									viewBox="0 0 20 20"
									fill="currentColor"
								>
									<path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
								</svg>
								Als Lesezeichen speichern
							</button>
						</div>
					</div>
					<p className="mt-2 text-gray-500 text-xs">
						Drücken Sie Strg+D (Windows) oder ⌘+D (Mac), um diese Seite zu Ihren
						Lesezeichen hinzuzufügen
					</p>
				</div>
			</div>

			{/* Zurück-Button */}
			<div className="mt-6 flex justify-start">
				<button
					onClick={() => router.back()}
					className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
				>
					Zurück
				</button>
			</div>
		</div>
	);
}
