// backend/src/api/ocpi-version/routes/ocpi-version.ts
'use strict';

module.exports = {
    routes: [
        {
            method: 'GET',
            path: '/ocpi/versions',
            handler: 'ocpi-version.getVersions',
            config: {
                auth: false
            }
        },
        {
            method: 'GET',
            path: '/ocpi/versions/2.2.1',
            handler: 'ocpi-version-2-2-1.getModules',
            config: {
                auth: false
            }
        },
    ],
};
