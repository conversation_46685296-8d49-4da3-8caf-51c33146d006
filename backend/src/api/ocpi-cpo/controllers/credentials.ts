'use strict';

import { randomBytes } from 'crypto';

interface SenderData {
    version?: string;      // Optional, weil der Sender es evtl. nicht liefert
    modules?: string[];    // Optional, weil der Sender es evtl. nicht liefert
}

module.exports = {
    async register2(ctx) {
        try {
            // Erwartete Payload vom Sender:
            // {
            //   connection_id: "1234",             // ID der OCPI Connection
            //   send_secret: "abc123...",           // credentials_token_b (SendSecret)
            //   sender_versions_url: "https://sender.com/ocpi/emsp/versions",
            //   ocpi_version: "2.2",                // optional, falls der Sender diese Angabe mitschickt
            //   modules: ["charging", "tariff"]      // optional, Liste der unterstützten Module
            // }
            const {
                connection_id,
                send_secret,
                sender_versions_url,
                ocpi_version,
                modules
            } = ctx.request.body;

            if (!connection_id || !send_secret || !sender_versions_url) {
                return ctx.send({
                    status_code: 4000,
                    status_message: "Bad Request: Fehlende Daten"
                });
            }

            // Abruf der Sender-Daten zur Validierung und optionalen Übernahme von Version und Module
            const senderResponse = await fetch(sender_versions_url, {
                method: 'GET',
                headers: {
                    'Authorization': `Token ${send_secret}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!senderResponse.ok) {
                return ctx.send({
                    status_code: 4001,
                    status_message: "Sender Daten konnten nicht abgerufen werden"
                });
            }

            const senderData: SenderData = await senderResponse.json();

            // Falls der Sender keine Version oder Module mitsendet, können diese aus den Sender-Daten übernommen werden
            const finalVersion = ocpi_version || senderData.version || "2.2";
            const finalModules = modules || senderData.modules || [];

            // Generiere das finale Token (ReceivingSecret, also credentials_token_c)
            const receivingSecret = randomBytes(16).toString('hex');

            // Aktualisiere den OCPI Connection-Datensatz in der Datenbank.
            // Hierbei werden folgende Felder gesetzt:
            // - ReceivingSecret: Das finale Secret
            // - registrationStatus: z. B. "completed"
            // - OCPIVersion: Die verwendete Version
            // - modules: Die unterstützten Module (muss im Model als Attribut definiert sein)
            await strapi.db.query('api::ocpi-connection.ocpi-connection').update({
                where: { id: connection_id },
                data: {
                    ReceivingSecret: receivingSecret,
                    registrationStatus: "completed",
                    OCPIVersion: finalVersion,
                    modules: finalModules
                }
            });

            return ctx.send({
                status_code: 1000,
                status_message: "Registration successful",
                data: {
                    credentials_token: receivingSecret
                }
            });
        } catch (error) {
            ctx.throw(500, error);
        }
    }
};
