"use client";

import React, { useState, useEffect } from "react";
import { FiEdit, FiLink, FiPlus, FiRefreshCw, FiTrash2 } from "react-icons/fi";
import { useMandant } from "~/components/MandantContext";
import { Button } from "~/components/ui/button";
import { Spinner } from "~/components/ui/spinner";
import { type LocationData, locationsApi } from "~/services/api";
import MandantZuordnungModal from "./MandantZuordnungModal";
import StandortForm from "./StandortForm";

export default function StandorteTable() {
	const [locations, setLocations] = useState<LocationData[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showForm, setShowForm] = useState(false);
	const [editingStandortId, setEditingStandortId] = useState<
		string | undefined
	>(undefined);
	const [showMandantModal, setShowMandantModal] = useState(false);
	const [selectedLocationForMandant, setSelectedLocationForMandant] =
		useState<LocationData | null>(null);
	const { activeMandant, getAllowedMandantIds } = useMandant();

	const fetchLocations = async () => {
		setIsLoading(true);
		setError(null);
		try {
			const response = await locationsApi.getAll();

			// Wenn ein aktiver Mandant ausgewählt ist, filtere die Standorte
			// ToDo Mandaten filter, aber erstn wenn Standorte einen Mandaten zugeordner wurden,
			// wenn ein Stanodort kein Mandant zugeordner ist, dann zeigen diesen wenn der Mandant root ausgewählt wurde.
			// Ebenfalls sollen alle Standorte angezeigt werden, wenn der Mandant root ausgewählt wurde.
			// if (activeMandant) {
			//   const allowedMandantIds = getAllowedMandantIds();
			//   const filteredLocations = response.data.filter(location =>
			//     location.mandant?.data?.id && allowedMandantIds.includes(location.mandant.data.id)
			//   );
			//   setLocations(filteredLocations);
			// } else {
			//   setLocations(response.data);
			// }

			setLocations(response.data);
		} catch (err) {
			console.error("Fehler beim Laden der Standorte:", err);
			setError(
				"Standorte konnten nicht geladen werden. Bitte versuchen Sie es später erneut.",
			);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchLocations();
	}, [activeMandant]);

	const handleEdit = (documentId: string) => {
		setEditingStandortId(documentId);
		setShowForm(true);
	};

	const handleFormClose = () => {
		setShowForm(false);
		setEditingStandortId(undefined);
	};

	const handleFormSuccess = () => {
		setShowForm(false);
		setEditingStandortId(undefined);
		fetchLocations();
	};

	const handleOpenMandantModal = (location: LocationData) => {
		setSelectedLocationForMandant(location);
		setShowMandantModal(true);
	};

	const handleMandantModalClose = () => {
		setShowMandantModal(false);
		setSelectedLocationForMandant(null);
	};

	const handleMandantModalSuccess = () => {
		setShowMandantModal(false);
		setSelectedLocationForMandant(null);
		fetchLocations();
	};

	const handleDelete = async (documentId: string) => {
		if (window.confirm("Möchten Sie diesen Standort wirklich löschen?")) {
			try {
				await locationsApi.delete(documentId);
				// Nach erfolgreichem Löschen die Liste aktualisieren
				fetchLocations();
			} catch (err) {
				console.error(
					`Fehler beim Löschen des Standorts mit documentId ${documentId}:`,
					err,
				);
				setError(
					"Standort konnte nicht gelöscht werden. Bitte versuchen Sie es später erneut.",
				);
			}
		}
	};

	return (
		<div className="space-y-4">
			{showForm ? (
				<StandortForm
					standortId={editingStandortId}
					onSuccess={handleFormSuccess}
					onCancel={handleFormClose}
				/>
			) : (
				<>
					<div className="flex items-center justify-between">
						<h2 className="font-semibold text-xl">Standorte</h2>
						<div className="flex space-x-2">
							<Button
								onClick={fetchLocations}
								variant="outline"
								className="flex items-center"
								disabled={isLoading}
							>
								<FiRefreshCw
									className={`mr-2 ${isLoading ? "animate-spin" : ""}`}
								/>
								Aktualisieren
							</Button>
							<Button
								className="flex items-center"
								onClick={() => {
									setEditingStandortId(undefined);
									setShowForm(true);
								}}
							>
								<FiPlus className="mr-2" />
								Neuer Standort
							</Button>
						</div>
					</div>

					{error && (
						<div className="rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
							{error}
						</div>
					)}

					{isLoading ? (
						<div className="flex justify-center py-8">
							<Spinner size="lg" />
						</div>
					) : (
						<div className="overflow-x-auto rounded-lg border border-gray-200">
							<table className="min-w-full divide-y divide-gray-200">
								<thead className="bg-gray-50">
									<tr>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Name
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											OCPI ID
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Adresse
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Stadt
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Land
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											EVSEs
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Mandant
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Aktionen
										</th>
									</tr>
								</thead>
								<tbody className="divide-y divide-gray-200 bg-white">
									{locations.length === 0 ? (
										<tr>
											<td
												colSpan={8}
												className="px-6 py-4 text-center text-gray-500 text-sm"
											>
												Keine Standorte gefunden. Klicken Sie auf "Neuer
												Standort", um einen Standort anzulegen.
											</td>
										</tr>
									) : (
										locations.map((location) => (
											<tr key={location.id} className="hover:bg-gray-50">
												<td className="whitespace-nowrap px-6 py-4 font-medium text-gray-900 text-sm">
													{location.name}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{location.ocpiId}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{location.address}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{location.city}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{location.country}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{location.evses?.length || 0}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{location.mandant?.data?.name || "-"}
												</td>
												<td className="whitespace-nowrap px-6 py-4 font-medium text-sm">
													<div className="flex space-x-2">
														<Button
															onClick={() => handleEdit(location.documentId)}
															variant="outline"
															size="sm"
															className="flex items-center"
														>
															<FiEdit className="mr-1" />
															Bearbeiten
														</Button>
														<Button
															onClick={() => handleOpenMandantModal(location)}
															variant="outline"
															size="sm"
															className="flex items-center"
														>
															<FiLink className="mr-1" />
															Mandant
														</Button>
														<Button
															onClick={() => handleDelete(location.documentId)}
															variant="destructive"
															size="sm"
															className="flex items-center"
														>
															<FiTrash2 className="mr-1" />
															Löschen
														</Button>
													</div>
												</td>
											</tr>
										))
									)}
								</tbody>
							</table>
						</div>
					)}
				</>
			)}

			{/* Mandanten-Zuordnungs-Modal */}
			{showMandantModal && selectedLocationForMandant && (
				<MandantZuordnungModal
					standortDocumentId={selectedLocationForMandant.documentId}
					currentMandantId={
						selectedLocationForMandant.mandant?.data?.documentId
					}
					onClose={handleMandantModalClose}
					onSuccess={handleMandantModalSuccess}
				/>
			)}
		</div>
	);
}
