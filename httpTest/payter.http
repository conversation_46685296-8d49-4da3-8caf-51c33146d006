POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
    "id": "my-info-screen",
    "type": "info",
    "properties": {
      "title": "Beleg",
      "subtitle": "Scannen Sie den QR-Code um einen Beleg für ihren Ladevorgang zu erhalten",
      "qr": "www.example.com",
      "buttons.ok": "",
      "buttons.ok.label": "OK"
    }
 }


####

POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "my-info-screen",
  "type": "dialog",
  "properties": {
    "title": "Beleg",
    "message": "Scannen Sie den QR-Code um einen Beleg für ihren Ladevorgang zu erhalten",
    "qr": "www.example.com",
    "buttons.ok": "",
    "buttons.ok.label": "OK"
  }
}


###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "my-info-screen",
  "type": "carousel",
  "properties": {
    "title": "Willkommen",
    "subtitle": "Wähle deinen Ladepunkt",
    "cards.0": "",
    "cards.0.logo": "mastercard",
    "cards.0.cardholder": "Mastercard",
    "cards.1": "",
    "cards.1.logo": "visa",
    "cards.1.cardholder": "Visa",
    "cards.3": "",
    "cards.3.logo": "vpay",
    "cards.3.cardholder": "VPAY",
    "buttons.ok": "",
    "buttons.ok.label": "OK",
    "buttons.lang": "",
    "buttons.lang.label": "Menü",
    "buttons.lang.lang": "false"
  }
}

###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "my-info-screen",
  "type": "selection",
  "properties": {
    "title": "Ladepunkt wählen",
    "type": "3",
    "items.prod1": "",
    "items.prod1.label": "Ladepunkt 1",
    "items.prod2": "",
    "items.prod2.label": "Ladepunkt 2",
    "items.prod3": "",
    "items.prod3.label": "Ladepunkt 3",
    "items.prod4": "",
    "items.prod4.label": "Ladepunkt 4",
    "items.prod5": "",
    "items.prod5.label": "Ladepunkt 5",
    "items.prod6": "",
    "items.prod6.label": "Ladepunkt 6"
  }
}

###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "my-info-screen",
  "type": "dialog",
  "properties": {
    "title": "Ladepunkt 1",
    "message": "Startgebühr 1€\n0,42€/kWh\n\n\n ab 12h 0,10€/min \nmax Blockiergebühr 10€",
    "buttons.ok": "",
    "buttons.ok.label": "Bezahlen",
    "buttons.cancel": "",
    "buttons.cancel.label": "Abbrechen"
  }
}

### Start
POST https://cps-test.mypayter.com/terminals/APO20243201849/start?authorizedAmount=5000&callbackUrl=https%3A%2F%2Fjr.payter.eulektro.de%2Fapi%2Fpayter%2FcardInfoCallback%3FevseDocumentId%3Dsbsco3hxp25cvw3l8u41rmqc%26datetimeForShowPrice%3D2025-04-22T14%25253A52%25253A04.736Z&uiMessage=Es+werden+50%2C00+%E2%82%AC+reserviert&customIdleScreen=false&uiMessageTitle=Bitte+Karte+vorhalten
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json



### Authorize
POST https://cps-test.mypayter.com/terminals/APO20243201849/authorize
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json



### Erro
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "screen-error",
  "type": "dialog",
  "properties": {
    "title": "Zahlungsfehler",
    "subtitle": "",
    "message": "Abgebrochen",
    "icon": "declined",
    "color": "2",
    "buttons.ok": "",
    "buttons.ok.label": "OK"
  }
}


###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "screen-starting-session",
  "type": "spinner",
  "properties": {
    "title": "Ladevorgang wird gestartet..."
  }
}

###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "screen-processing",
  "type": "spinner",
  "properties": {
    "title": "Zahlung in Bearbeitung"
  }
}

###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "my-info-screen",
  "type": "info",
  "properties": {
    "title": "Beleg",
    "subtitle": "Scannen Sie den QR-Code um einen Beleg für ihren Ladevorgang zu erhalten",
    "qr": "www.example.com",
    "buttons.ok": "",
    "buttons.ok.label": "OK"
  }
}

###
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "my-info-screen",
  "type": "selection",
  "properties": {
    "title": "Select your option",
    "type": "3",
    "items.de": "",
    "items.de.label": "Deutsch",
    "items.de.ico": "de",
    "items.prod2": "",
    "items.prod2.label": "Französisch",
    "items.prod2.ico": "fr",
    "items.prod3": "",
    "items.prod6": "",
    "items.prod6.label": "Niederländisch",
    "items.prod6.ico": "nl",
    "items.prod7.label": "Italienisch",
    "items.prod7.ico": "it",
    "items.prod8": "",
    "items.prod8.label": "Spanisch",
    "items.prod8.ico": "es",
    "items.prod9": "",
    "items.prod3.label": "Polnisch",
    "items.prod3.ico": "pl",
    "items.prod4": "",
    "items.prod4.label": "Tschechisch",
    "items.prod4.ico": "cz",
    "items.prod5": "",
    "items.prod5.label": "Dänisch",
    "items.prod5.ico": "dk",
    "items.prod7": "",
    "items.prod9.label": "Schwedisch",
    "items.prod9.ico": "sv",
    "items.prod10": "",
    "items.prod10.label": "Norwegisch",
    "items.prod10.ico": "no"
  }
}


###
GET https://cps.mypayter.com/terminals/
Authorization: CPS apikey="8b1c582ed423f2a5a86730c3bd82e052"
Content-Type: application/json

### Starten Fehlgeschlagen Screen
POST https://cps-test.mypayter.com/terminals/APO20243201849/ui
Authorization: CPS apikey="9f04e7195562a9924680074b51de0dae"
Content-Type: application/json

{
  "id": "screen-start-command-response-error",
  "type": "dialog",
  "properties": {
    "title": "Fehlgeschlagen",
    "message": "Ladevorgang konnte nicht gestartet werden",
    "icon": "declined",
    "buttons.ok": "",
    "buttons.ok.label": "Home"
  }
}
