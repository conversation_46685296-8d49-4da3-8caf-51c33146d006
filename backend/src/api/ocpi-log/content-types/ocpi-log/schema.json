{"kind": "collectionType", "collectionName": "ocpi_logs", "info": {"singularName": "ocpi-log", "pluralName": "ocpi-logs", "displayName": "OCPI Log"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"logId": {"type": "string", "unique": true, "required": true}, "message": {"type": "text", "required": true}, "level": {"type": "enumeration", "enum": ["info", "warn", "error", "debug"], "default": "info", "required": true}, "endpoint": {"type": "string"}, "request": {"type": "json"}, "response": {"type": "json"}, "timestamp": {"type": "datetime", "required": true}}}