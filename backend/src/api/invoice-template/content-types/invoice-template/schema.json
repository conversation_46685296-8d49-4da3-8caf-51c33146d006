{"kind": "collectionType", "collectionName": "invoice_templates", "info": {"singularName": "invoice-template", "pluralName": "invoice-templates", "displayName": "InvoiceTemplate", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"from": {"type": "date"}, "to": {"type": "date"}, "companyName": {"type": "string"}, "vatId": {"type": "string"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "invoice_templates"}, "invoice_prefix": {"type": "string", "required": true, "unique": true}, "street": {"type": "string", "required": true}, "city": {"type": "string", "required": true}, "country": {"type": "string", "required": true}, "zip": {"type": "string", "required": true}, "houseNr": {"type": "string", "required": true}, "website": {"type": "string"}, "email": {"type": "email"}, "bankName": {"type": "string"}, "bankIBAN": {"type": "string"}, "bankBIC": {"type": "string"}, "ceo": {"type": "string", "description": "Geschäftsführer"}, "commercialRegister": {"type": "string", "description": "Handelsregisternummer"}}}