import * as React from "react";
import { cn } from "~/lib/utils";

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement> {
	variant?:
		| "default"
		| "destructive"
		| "outline"
		| "secondary"
		| "ghost"
		| "link";
	size?: "default" | "sm" | "lg" | "icon";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant = "default", size = "default", ...props }, ref) => {
		return (
			<button
				className={cn(
					"inline-flex cursor-pointer items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",

					// Varianten
					variant === "default" && "btn-primary",
					variant === "destructive" && "bg-red-500 text-white hover:bg-red-600",
					variant === "outline" &&
						"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50",
					variant === "secondary" &&
						"bg-gray-100 text-gray-900 hover:bg-gray-200",
					variant === "ghost" &&
						"text-gray-700 hover:bg-gray-100 hover:text-gray-900",
					variant === "link" &&
						"text-blue-600 underline-offset-4 hover:underline",

					// Größen
					size === "default" && "h-10 px-4 py-2",
					size === "sm" && "h-8 px-3 text-sm",
					size === "lg" && "h-12 px-6 text-lg",
					size === "icon" && "h-10 w-10",

					className,
				)}
				ref={ref}
				{...props}
			/>
		);
	},
);

Button.displayName = "Button";

export { Button };
