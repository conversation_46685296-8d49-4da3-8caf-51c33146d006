{"kind": "collectionType", "collectionName": "payter_connections", "info": {"singularName": "payter-connection", "pluralName": "payter-connections", "displayName": "PayterConnection", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "apiKey": {"type": "string"}, "apiUrl": {"type": "string"}, "type": {"type": "enumeration", "enum": ["Prod", "Test"]}, "mandants": {"type": "relation", "relation": "oneToMany", "target": "api::mandant.mandant", "mappedBy": "payter_connection"}, "terminals": {"type": "relation", "relation": "oneToMany", "target": "api::terminal.terminal", "mappedBy": "payter_connection"}, "lastWebhookInit": {"type": "datetime"}}}