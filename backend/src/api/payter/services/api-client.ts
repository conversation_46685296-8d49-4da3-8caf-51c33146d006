// backend/src/api/payter/services/api-client.ts
import payterApiService from "./payter-api";
import {AxiosInstance} from 'axios';

/**
 * API Client utility module für Payter Terminal Kommunikation
 */
export interface ApiClientResponse {
    apiClient: AxiosInstance | null;
    callback: {
        state: string | null;
        ui: string | null;
        cardInfo: string | null;
    };
}

// Cache für API-Clients, um unnötige Neuinstanziierungen zu vermeiden
const apiClientCache: Record<string, { apiClient: AxiosInstance, timestamp: number }> = {};

// Cache-Gültigkeitsdauer in Millisekunden (10 Minuten)
const CACHE_TTL = 10 * 60 * 1000;

/**
 * Erzeugt einen API Client und Callback URLs für die Payter Terminal API
 * basierend auf der Terminal-ID
 *
 * @param terminalId - Die Terminal-ID (serialNumber)
 * @returns ApiClientResponse mit apiClient und callback-Objekt
 */
export default {

    async getPayterApiClientByConnectionType(mandant, connectionType: "Test" | "Prod"): Promise<ApiClientResponse> {
        try {
            // Cache-Key für den Verbindungstyp erstellen
            const cacheKey = `connection_${connectionType}`;
            const now = Date.now();
            const cachedClient = apiClientCache[cacheKey];

            // Verwende Cache, wenn vorhanden und noch gültig
            if (cachedClient && (now - cachedClient.timestamp) < CACHE_TTL) {
                console.log(`Verwende gecachten API-Client für Verbindungstyp ${connectionType}`);

                // Hole die Server-Konfiguration für die Callback-URLs
                const publicURL = strapi.config.get('server.publicURL');

                // Erstelle die Callback-URLs
                return {
                    apiClient: cachedClient.apiClient,
                    callback: {
                        state: `${publicURL}/api/payter/stateCallback`,
                        ui: `${publicURL}/api/payter/uiCallback`,
                        cardInfo: `${publicURL}/api/payter/cardInfoCallback`
                    }
                };
            }

            // Hole die Verbindungskonfiguration
            const payterConnection = await strapi.documents('api::payter-connection.payter-connection').findFirst({
                filters: {
                    type: connectionType,
                    mandant: {
                        documentId: mandant.documentId
                    }
                },
            });

            if (!payterConnection) {
                console.error(`Keine Payter-Verbindung für Verbindungstyp ${connectionType} gefunden`);
                return {
                    apiClient: null,
                    callback: {state: null, ui: null, cardInfo: null}
                };
            }

            // Hole die Verbindungsdaten
            const apiKey = payterConnection.apiKey;

            if (!apiKey) {
                console.error(`Kein API-Schlüssel für Verbindungstyp ${connectionType} gefunden`);
                return {
                    apiClient: null,
                    callback: {state: null, ui: null, cardInfo: null}
                };
            }

            // Erstelle einen API-Client für die Payter API
            const apiClient = payterApiService.getApiClient(payterConnection.apiUrl, apiKey);

            if (!apiClient) {
                console.error(`Fehler beim Erstellen des API-Clients für Verbindungstyp ${connectionType}`);
                return {
                    apiClient: null,
                    callback: {state: null, ui: null, cardInfo: null}
                };
            }

            // Speichere den API-Client im Cache
            apiClientCache[cacheKey] = {
                apiClient,
                timestamp: now
            };

            // Hole die Server-Konfiguration für die Callback-URL
            const publicURL = strapi.config.get('server.publicURL');

            if (!publicURL) {
                console.error('Keine öffentliche URL in der Server-Konfiguration gefunden');
                return {
                    apiClient,
                    callback: {state: null, ui: null, cardInfo: null}
                };
            }

            // Erstelle die Callback-URLs
            return {
                apiClient,
                callback: {
                    state: `${publicURL}/api/payter/stateCallback`,
                    ui: `${publicURL}/api/payter/uiCallback`,
                    cardInfo: `${publicURL}/api/payter/cardInfoCallback`
                }
            };
        } catch (error) {
            console.error(`Fehler beim Erstellen des Payter API-Clients für Verbindungstyp ${connectionType}:`, error);
            return {
                apiClient: null,
                callback: {state: null, ui: null, cardInfo: null}
            };
        }
    },


    /**
     * Holt einen API-Client für ein bestimmtes Terminal
     *
     * @param ctx - Der Strapi Kontext
     * @returns ApiClientResponse mit apiClient und callback-Objekt
     */
    async getPayterApiClient(ctx): Promise<ApiClientResponse> {
        try {
            // Prüfe, ob ein gültiger Cache-Eintrag existiert
            const cacheKey = `terminal_${ctx.state.terminal.serialNumber}`;
            const now = Date.now();
            const cachedClient = apiClientCache[cacheKey];

            if (cachedClient && (now - cachedClient.timestamp) < CACHE_TTL) {
                console.log(`Using cached API client for terminal ${ctx.state.terminal.serialNumber}`);

                // Hole die Server-Konfiguration für die Callback-URLs
                const publicURL = strapi.config.get('server.publicURL');

                // Erstelle die Callback-URLs
                return {
                    apiClient: cachedClient.apiClient,
                    callback: {
                        state: `${publicURL}/api/payter/stateCallback`,
                        ui: `${publicURL}/api/payter/uiCallback`,
                        cardInfo: `${publicURL}/api/payter/cardInfoCallback`
                    }
                };
            }

            // Hole das Terminal mit der payter_connection
            const terminal = await strapi.documents('api::terminal.terminal').findFirst({
                filters: {serialNumber: ctx.state.terminal.serialNumber},
                populate: {
                    payter_connection: true
                }
            });

            if (!terminal || !terminal.payter_connection) {
                console.error(`Terminal ${ctx.state.terminal.serialNumber} not found or has no payter_connection`);
                return {
                    apiClient: null,
                    callback: {state: null, ui: null, cardInfo: null}
                };
            }

            // Hole die Verbindungsdaten
            const connection = terminal.payter_connection;
            const apiKey = connection.apiKey;

            if (!apiKey) {
                console.error(`No API key found for terminal ${ctx.state.terminal.serialNumber}`);
                return {
                    apiClient: null,
                    callback: {state: null, ui: null, cardInfo: null}
                };
            }

            // Erstelle einen API-Client für die Payter API
            const apiClient = payterApiService.getApiClient(terminal.payter_connection.apiUrl, apiKey);

            // Speichere den API-Client im Cache
            apiClientCache[cacheKey] = {
                apiClient,
                timestamp: now
            };
            const publicUrl = strapi.config.get('server.publicURL', 'localhost:1337');
            // Erstelle die Callback-URLs
            return {
                apiClient,
                callback: {
                    state: `${publicUrl}/api/payter/stateCallback`,
                    ui: `${publicUrl}/api/payter/uiCallback`,
                    cardInfo: `${publicUrl}/api/payter/cardInfoCallback`
                }
            };
        } catch (error) {
            console.error(`Error creating Payter API client for terminal ${ctx.state.terminal.serialNumber}:`, error);
            return {
                apiClient: null,
                callback: {state: null, ui: null, cardInfo: null}
            };
        }
    }
}

/**
 * Standardisierte Fehlerbehandlung für Payter API-Aufrufe
 *
 * @param ctx - Der Strapi Kontext
 * @param error - Der aufgetretene Fehler
 * @param terminalId - Die Terminal-ID
 * @returns Eine formatierte Fehlerantwort
 */
export function handleApiError(ctx, error, terminalId) {
    console.error(`Error calling Payter API for terminal ${terminalId}:`, error);
    if (error.response) {
        ctx.status = error.response.status;
        return ctx.send({
            status_code: error.response.status,
            status_message: `Error sending request to Payter for terminal ${terminalId}`,
            error_details: error.response.data
        });
    } else if (error.request) {
        ctx.status = 504;
        return ctx.send({
            status_code: 504,
            status_message: `No response received from Payter API for terminal ${terminalId}`
        });
    } else {
        ctx.status = 500;
        return ctx.send({
            status_code: 500,
            status_message: `Internal error setting up Payter request for terminal ${terminalId}`,
            error_details: error.message
        });
    }
}
