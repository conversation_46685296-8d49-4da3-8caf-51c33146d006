


module.exports = {

    async getLocations(ctx) {
        const locations = await strapi.documents('api::ocpi-location.ocpi-location').findMany({
            populate: "*" // Populiert alle Relationen auf erster Ebene
            // Oder spezifisch: populate: ["mandant", "evses", "ocpiConnection"]
        });

        return ctx.send({
            status_code: 1000,
            status_message: "Success",
            data: locations || [],
            timestamp: new Date().toISOString()
        });
    },

    async assignMandant(ctx) {
        try {
            const { documentId } = ctx.params;
            const { mandantId } = ctx.request.body;

            if (!documentId) {
                return ctx.badRequest('Location Document ID ist erforderlich');
            }

            // Überprüfen, ob die Location existiert
            const location = await strapi.documents('api::ocpi-location.ocpi-location').findOne({
                documentId: documentId,
            });

            if (!location) {
                return ctx.notFound('Location nicht gefunden');
            }

            // Mandant zuordnen oder Zuordnung entfernen
            let updateData = {};

            if (mandantId) {
                // Mandant zuordnen
                updateData = {
                    mandant: { connect: [mandantId] }
                };
            } else {
                // Mandant-Zuordnung entfernen
                updateData = {
                    mandant: { disconnect: true }
                };
            }

            // Location aktualisieren
            const updatedLocation = await strapi.documents('api::ocpi-location.ocpi-location').update({
                documentId: documentId,
                data: updateData
            });

            return ctx.send({
                status_code: 1000,
                status_message: "Success",
                data: updatedLocation,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            strapi.log.error('Fehler bei der Zuordnung eines Mandanten zu einer Location:', error);
            return ctx.internalServerError('Ein Fehler ist aufgetreten');
        }
    }
}
