/**
 * Terminal Logger Service
 *
 * Dieser Service bietet Funktionen zum Loggen der Kommunikation mit Terminals.
 * Er verwendet die Backend-API-Endpunkte für das Logging.
 */
import axios from "axios";

export interface TerminalLogOptions {
	terminalId: string;
	payload: any;
	responsePayload?: any;
	paymentSessionId?: string;
	mandantId?: number;
}

export interface TerminalDebugOptions extends TerminalLogOptions {
	direction?:
		| "ServerToTerminal"
		| "TerminalToServer"
		| "WebToServer"
		| "ServerToWeb"
		| "Unknown";
}

// Map zur Speicherung von Request-IDs und zugehörigen Payloads
const requestMap = new Map<string, any>();

/**
 * Generiert eine eindeutige Request-ID
 */
function generateRequestId(): string {
	return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Terminal Logger Klasse
 */
export class TerminalLogger {
	private static instance: TerminalLogger;

	private constructor() {}

	/**
	 * Gibt die Singleton-Instanz des TerminalLogger zurück
	 */
	public static getInstance(): TerminalLogger {
		if (!TerminalLogger.instance) {
			TerminalLogger.instance = new TerminalLogger();
		}
		return TerminalLogger.instance;
	}

	/**
	 * Loggt eine Nachricht vom Server zum Terminal
	 * Gibt eine Request-ID zurück, die für das Loggen der Antwort verwendet werden kann
	 */
	public async logToTerminal({
		terminalId,
		payload,
		paymentSessionId,
		mandantId,
	}: TerminalLogOptions): Promise<string> {
		try {
			const requestId = generateRequestId();

			// Speichere das Payload für spätere Verwendung
			requestMap.set(requestId, {
				terminalId,
				payload,
				timestamp: new Date().toISOString(),
				paymentSessionId,
				mandantId,
			});

			// Bereinige alte Einträge (älter als 5 Minuten)
			this.cleanupRequestMap();

			await axios.post("/api/terminal-message-log/log-to-terminal", {
				terminalId,
				payload,
				paymentSessionId,
				mandantId,
			});

			return requestId;
		} catch (error) {
			console.error(
				"Fehler beim Logging der Server-zu-Terminal-Nachricht:",
				error,
			);
			return "";
		}
	}

	/**
	 * Loggt eine Nachricht vom Terminal zum Server mit Bezug auf eine vorherige Anfrage
	 */
	public async logFromTerminal(
		{
			terminalId,
			payload,
			responsePayload,
			paymentSessionId,
			mandantId,
		}: TerminalLogOptions,
		requestId?: string,
	): Promise<void> {
		try {
			// Wenn eine Request-ID angegeben wurde, versuche die ursprüngliche Anfrage zu finden
			let originalRequest = null;
			if (requestId && requestMap.has(requestId)) {
				originalRequest = requestMap.get(requestId);
				requestMap.delete(requestId); // Entferne die Anfrage aus dem Map
			}

			// Wenn responsePayload angegeben wurde, verwende es direkt
			if (responsePayload) {
				await axios.post("/api/terminal-message-log/log-from-terminal", {
					terminalId,
					payload: {
						request: originalRequest?.payload || null,
						response: responsePayload,
					},
					paymentSessionId:
						paymentSessionId || originalRequest?.paymentSessionId,
					mandantId: mandantId || originalRequest?.mandantId,
				});
			} else {
				// Andernfalls verwende das normale Payload
				await axios.post("/api/terminal-message-log/log-from-terminal", {
					terminalId,
					payload,
					paymentSessionId:
						paymentSessionId || originalRequest?.paymentSessionId,
					mandantId: mandantId || originalRequest?.mandantId,
				});
			}
		} catch (error) {
			console.error(
				"Fehler beim Logging der Terminal-zu-Server-Nachricht:",
				error,
			);
		}
	}

	/**
	 * Loggt eine komplette Request-Response-Kommunikation in einem einzigen Eintrag
	 */
	public async logRequestResponse({
		terminalId,
		payload,
		responsePayload,
		paymentSessionId,
		mandantId,
	}: TerminalLogOptions): Promise<void> {
		try {
			await axios.post("/api/terminal-message-log/log-to-terminal", {
				terminalId,
				payload: {
					request: payload,
					response: responsePayload,
				},
				paymentSessionId,
				mandantId,
			});
		} catch (error) {
			console.error(
				"Fehler beim Logging der Request-Response-Kommunikation:",
				error,
			);
		}
	}

	/**
	 * Loggt einen Fehler in der Terminal-Kommunikation
	 */
	public async logError({
		terminalId,
		payload,
		responsePayload,
		paymentSessionId,
		mandantId,
	}: TerminalLogOptions): Promise<void> {
		try {
			await axios.post("/api/terminal-message-log/log-error", {
				terminalId,
				payload: responsePayload
					? {
							request: payload,
							response: responsePayload,
							error: true,
						}
					: payload,
				paymentSessionId,
				mandantId,
			});
		} catch (error) {
			console.error("Fehler beim Logging des Terminal-Fehlers:", error);
		}
	}

	/**
	 * Loggt eine Debug-Nachricht in der Terminal-Kommunikation
	 */
	public async logDebug({
		terminalId,
		payload,
		direction = "Unknown",
		paymentSessionId,
		mandantId,
	}: TerminalDebugOptions): Promise<void> {
		try {
			await axios.post("/api/terminal-message-log/log-debug", {
				terminalId,
				payload,
				direction,
				paymentSessionId,
				mandantId,
			});
		} catch (error) {
			console.error("Fehler beim Logging der Debug-Nachricht:", error);
		}
	}

	/**
	 * Bereinigt alte Einträge aus der Request-Map
	 * Entfernt Einträge, die älter als 5 Minuten sind
	 */
	private cleanupRequestMap(): void {
		const now = Date.now();
		const fiveMinutesAgo = now - 5 * 60 * 1000;

		requestMap.forEach((value, key) => {
			const timestamp = new Date(value.timestamp).getTime();
			if (timestamp < fiveMinutesAgo) {
				requestMap.delete(key);
			}
		});
	}
}

// Exportiere eine Zugriffsfunktion für einfacheren Zugriff auf die Singleton-Instanz
export const terminalLogger = TerminalLogger.getInstance();
