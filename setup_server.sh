#!/bin/bash

# ====================
# LOCALE EINSTELLUNGEN
# ====================

# Setze Locale-Einstellungen, um Fehler wie "manpath: can't set the locale" zu vermeiden
export LC_ALL=C.UTF-8
export LANG=C.UTF-8

# ====================
# KONFIGURATION

# Node.js, NVM und PM2 werden installiert
# ====================

DOMAIN1="api.terminal.eulektro.de"
PORT1=1337

DOMAIN2="terminal.eulektro.de"
PORT2=3000

EMAIL="<EMAIL>"  # Für Let's Encrypt Registrierung
ENABLE_HTTPS=true

# ====================
# INSTALLATION
# ====================

echo "👉 Installiere NVM (Node Version Manager)..."
curl https://raw.githubusercontent.com/nvm-sh/nvm/master/install.sh  | bash

# Lade NVM in die aktuelle Shell
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

echo "👉 Installiere Node.js 22..."
nvm install 22
nvm use 22
nvm alias default 22

echo "👉 Installiere PM2 global..."
npm install pm2 -g

echo "👉 Installiere NGINX..."
sudo apt update
sudo apt install -y nginx

# ====================
# NGINX KONFIGURATION
# ====================

echo "👉 Erstelle NGINX-Konfigurationen..."

sudo tee /etc/nginx/sites-available/$DOMAIN1 > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN1;

    location / {
        proxy_pass http://localhost:$PORT1;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

sudo tee /etc/nginx/sites-available/$DOMAIN2 > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN2;

    location / {
        proxy_pass http://localhost:$PORT2;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# ====================
# AKTIVIEREN UND TESTEN
# ====================

echo "👉 Aktiviere Sites..."
sudo ln -sf /etc/nginx/sites-available/$DOMAIN1 /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/$DOMAIN2 /etc/nginx/sites-enabled/

echo "👉 Teste NGINX-Konfiguration..."
sudo nginx -t
sudo systemctl reload nginx

# ====================
# HTTPS (Let's Encrypt)
# ====================

if [ "$ENABLE_HTTPS" = true ]; then
  echo "🔒 Installiere Certbot und richte HTTPS ein..."
  sudo apt install -y certbot python3-certbot-nginx
  sudo certbot --nginx \
    --non-interactive --agree-tos \
    --redirect \
    -m "$EMAIL" \
    -d "$DOMAIN1" -d "$DOMAIN2"
fi

echo "✅ Setup abgeschlossen. Deine Node.js-Anwendungen sind jetzt über HTTPS erreichbar."
