/**
 * OCPI Command Router
 *
 * Definiert die Routen für die OCPI 2.2.1 Command-Endpunkte:
 * - startSession
 * - stopSession
 * - unlockConnector
 */

import { factories } from '@strapi/strapi';

const routes = [
  // Standard-Routen
  {
    method: 'GET',
    path: '/ocpi-commands',
    handler: 'ocpi-command.find',
    config: {
      auth: false
    }
  },
  {
    method: 'GET',
    path: '/ocpi-commands/:id',
    handler: 'ocpi-command.findOne',
    config: {
      auth: false
    }
  },
  {
    method: 'POST',
    path: '/ocpi-commands',
    handler: 'ocpi-command.create',
    config: {
      auth: false
    }
  },
  {
    method: 'PUT',
    path: '/ocpi-commands/:id',
    handler: 'ocpi-command.update',
    config: {
      auth: false
    }
  },
  {
    method: 'DELETE',
    path: '/ocpi-commands/:id',
    handler: 'ocpi-command.delete',
    config: {
      auth: false
    }
  },
    // Benutzerdefinierte Routen
    {
      method: 'POST',
      path: '/ocpi/commands/STOP_SESSION',
      handler: 'ocpi-command.stopSession',
      config: {
        auth: false
      }
    },
    {
      method: 'POST',
      path: '/ocpi/commands/UNLOCK_CONNECTOR',
      handler: 'ocpi-command.unlockConnector',
      config: {
        auth: false
      }
    },
    // Versionsspezifische Routen
    {
      method: 'POST',
      path: '/ocpi/2.2.1/commands/STOP_SESSION',
      handler: 'ocpi-command.stopSession',
      config: {
        auth: false
      }
    },
    {
      method: 'POST',
      path: '/ocpi/2.2.1/commands/UNLOCK_CONNECTOR',
      handler: 'ocpi-command.unlockConnector',
      config: {
        auth: false
      }
    },
];

export default routes;
