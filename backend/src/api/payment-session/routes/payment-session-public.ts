/**
 * Öffentliche Routen für Payment-Sessions
 * Diese Routen erfordern keine Authentifizierung
 */

export default {
  routes: [
    {
      method: 'GET',
      path: '/public/payment-sessions/:sessionId',
      handler: 'payment-session-public.findPublic',
      config: {
        auth: false
      }
    },
    {
      method: 'POST',
      path: '/public/payment-sessions/:sessionId/email',
      handler: 'payment-session-public.saveEmail',
      config: {
        auth: false
      }
    },
    {
      method: 'GET',
      path: '/public/payment-sessions/:sessionId/invoice',
      handler: 'payment-session-public.checkInvoice',
      config: {
        auth: false
      }
    }
  ]
};
