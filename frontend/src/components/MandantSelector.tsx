"use client";

import React, { useState } from "react";
import { type Mandant, useMandant } from "./MandantContext";

export default function MandantSelector() {
	const { activeMandant, setActiveMandant, availableMandants, isLoading } =
		useMandant();
	const [isOpen, setIsOpen] = useState(false);

	// Wenn keine Daten geladen sind, zeige Ladezustand
	if (isLoading) {
		return (
			<div className="mt-2 flex w-full items-center justify-center px-3 py-2 text-gray-300 text-sm">
				<svg
					className="-ml-1 mr-2 h-5 w-5 animate-spin text-white"
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
				>
					<circle
						className="opacity-25"
						cx="12"
						cy="12"
						r="10"
						stroke="currentColor"
						strokeWidth="4"
					/>
					<path
						className="opacity-75"
						fill="currentColor"
						d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
					/>
				</svg>
				Lade Mandanten...
			</div>
		);
	}

	// Wenn keine Mandanten verfügbar sind
	if (!availableMandants || availableMandants.length === 0) {
		return (
			<div className="mt-2 w-full rounded-md bg-white/10 px-4 py-2 text-center text-gray-200 text-sm">
				Keine Mandanten verfügbar
			</div>
		);
	}

	// Wenn nur ein Mandant verfügbar ist, zeige nur Namen (nicht klickbar)
	if (availableMandants.length === 1) {
		return (
			<div className="mt-2 w-full rounded-md bg-white/10 px-4 py-2 font-medium text-sm text-white">
				<div className="flex items-center">
					<svg
						className="mr-2 h-4 w-4 text-cyan-300"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
						/>
					</svg>
					{activeMandant?.name}
				</div>
			</div>
		);
	}

	const handleMandantChange = (mandant: Mandant) => {
		setActiveMandant(mandant);
		setIsOpen(false);
	};

	return (
		<div className="relative mt-3 w-full">
			<button
				onClick={() => setIsOpen(!isOpen)}
				className="flex w-full items-center justify-between rounded-lg bg-cyan-600 px-4 py-2.5 text-sm text-white shadow-md transition-colors hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-white/50"
			>
				<div className="flex items-center">
					<svg
						className="mr-2 h-4 w-4 text-cyan-300"
						viewBox="0 0 24 24"
						fill="none"
						stroke="currentColor"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
						/>
					</svg>
					<span className="font-medium">{activeMandant?.name}</span>
				</div>
				<svg
					className={`h-4 w-4 transition-transform ${isOpen ? "rotate-180 transform" : ""}`}
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M19 9l-7 7-7-7"
					/>
				</svg>
			</button>

			{isOpen && (
				<div className="absolute z-10 mt-1 w-full overflow-hidden rounded-md bg-white shadow-lg">
					<div className="py-1">
						{availableMandants.map((mandant) => (
							<button
								key={mandant.id}
								onClick={() => handleMandantChange(mandant)}
								className={`flex w-full items-center px-4 py-2.5 text-left text-sm hover:bg-gray-100 ${
									activeMandant?.id === mandant.id
										? "bg-cyan-50 font-medium text-cyan-800"
										: "text-gray-700"
								}`}
							>
								{activeMandant?.id === mandant.id && (
									<svg
										className="mr-2 h-4 w-4 text-cyan-600"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M5 13l4 4L19 7"
										/>
									</svg>
								)}
								{activeMandant?.id !== mandant.id && (
									<span className="mr-2 w-6" />
								)}
								{mandant.name}
							</button>
						))}
					</div>
				</div>
			)}
		</div>
	);
}
