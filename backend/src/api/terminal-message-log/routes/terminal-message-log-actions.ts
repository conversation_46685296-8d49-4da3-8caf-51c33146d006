/**
 * terminal-message-log-actions router
 */

module.exports = {
  routes: [
    // Route für das Loggen von Server-zu-Terminal-Nachrichten
    {
      method: 'POST',
      path: '/terminal-message-log/log-to-terminal',
      handler: 'terminal-message-log-actions.logToTerminal',
      config: {
        auth: false, // Keine Authentifizierung erforderlich, da vom Frontend aufgerufen
      },
    },
    // Route für das Loggen von Terminal-zu-Server-Nachrichten
    {
      method: 'POST',
      path: '/terminal-message-log/log-from-terminal',
      handler: 'terminal-message-log-actions.logFromTerminal',
      config: {
        auth: false, // Keine Authentifizierung erforderlich, da vom Frontend aufgerufen
      },
    },
    // Route für das Loggen von Fehlern
    {
      method: 'POST',
      path: '/terminal-message-log/log-error',
      handler: 'terminal-message-log-actions.logError',
      config: {
        auth: false, // Keine Authentifizierung erford<PERSON>lich, da vom Frontend aufgerufen
      },
    },
    // Route für das Loggen von Debug-Nachrichten
    {
      method: 'POST',
      path: '/terminal-message-log/log-debug',
      handler: 'terminal-message-log-actions.logDebug',
      config: {
        auth: false, // Keine Authentifizierung erforderlich, da vom Frontend aufgerufen
      },
    },
    // Route zum Abrufen aller eindeutigen Terminal-IDs
    {
      method: 'GET',
      path: '/terminal-message-logs/terminal-ids',
      handler: 'terminal-message-log-actions.getTerminalIds',
      config: {
        auth: false, // Vereinfachter Zugriff für die Filterung
      },
    },
  ],
};
