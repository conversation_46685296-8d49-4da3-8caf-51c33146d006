/**
 * OCPI 2.2.1 Sessions Module Controller
 * Implementiert die OCPI 2.2.1 Sessions-Endpunkte für CPO
 */
import {document} from "postcss";
import { createSuccessResponse, createErrorResponse, OCPIStatusCode } from '../../ocpi-common/utils/response-utils';

// Helper-Funktionen für OCPI-Antworten wurden in ocpi-common/utils/response-utils.ts ausgelagert

// OCPI Session Controller
export default {

    /**
     * OCPI Session-Endpoint GET (CPO → eMSP)
     * Liefert alle aktiven Session-Objekte oder Details zu einer bestimmten Session
     */
    async getSessions(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            if (!validationResult.valid) {
                return ctx.send(createErrorResponse(OCPIStatusCode.CLIENT_ERROR, "Unauthorized"), { status: 401 });
            }

            // Log für den OCPI-Aufruf erstellen
            await strapi.service('api::ocpi-log.ocpi-logger').logRequest(ctx, 'GET', '/sessions');

            // Prüfen, ob eine spezifische Session angefragt wurde
            const sessionId = ctx.params.sessionId;

            let response;
            if (sessionId) {
                // Eine spezifische Session suchen
                const session = await strapi.entityService.findMany('api::ocpi-session.ocpi-session', {
                    filters: { sessionId },
                });

                if (!session || session.length === 0) {
                    return ctx.send(createErrorResponse(OCPIStatusCode.NOT_FOUND, "Session not found"), { status: 500 });
                }

                // Session in OCPI-Format konvertieren
                const ocpiSession = convertToOcpiSession(session[0]);
                response = createSuccessResponse(ocpiSession);
            } else {

                const sessions = await strapi.entityService.findMany('api::ocpi-session.ocpi-session', {
                    filters: { ocpiStatus: 'ACTIVE'},
                });

                // Sessions in OCPI-Format konvertieren
                const ocpiSessions = sessions.map(convertToOcpiSession);
                response = createSuccessResponse(ocpiSessions);
            }

            return ctx.send(response);
        } catch (error) {
            console.error('Error in getSessions:', error);
            return ctx.send(createErrorResponse(OCPIStatusCode.SERVER_ERROR, "Server error"), { status: 500 });
        }
    },

    /**
     * OCPI Session-Endpoint PUT (eMSP → CPO)
     * Aktualisiert eine bestehende Session oder erstellt eine neue
     */
    async updateSession(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            if (!validationResult.valid) {
                return ctx.send(createErrorResponse(OCPIStatusCode.CLIENT_ERROR, "Unauthorized"), { status: 401 });
            }

            const sessionId = ctx.params.sessionId;
            const sessionData = ctx.request.body;

            if (!sessionId || !sessionData) {
                return ctx.send(createErrorResponse(OCPIStatusCode.INVALID_PARAMETERS, "Missing required data"), { status: 400 });
            }

            // Suchen, ob die Session bereits existiert
            const existingSession = await strapi.documents('api::ocpi-session.ocpi-session').findFirst({
                filters: { sessionId },
            });

            let session;
            if (existingSession) {
                // Bestehende Session aktualisieren
                session = await strapi.documents('api::ocpi-session.ocpi-session').update({
                    documentId: existingSession.documentId,
                    data: {
                        ...convertFromOcpiSession(sessionData),
                    }
                });
            } else {

                const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
                    documentId: sessionData.authorization_reference,
                    populate: {
                        mandant: true,
                        terminal: {
                            populate: {
                                payter_connection: true,
                            }
                        }
                    }
                });

                // Neue Session erstellen
                session = await strapi.documents('api::ocpi-session.ocpi-session').create({
                    data: {
                        ...convertFromOcpiSession(sessionData),
                        payment_session: paymentSession.documentId,
                        sessionId: sessionId,
                        mandant: paymentSession.mandant.documentId,
                        publishedAt: new Date()
                    }
                });

                await strapi.service('api::terminal.push-terminal-screen').pushQrCode(paymentSession)

            }

            return ctx.send(createSuccessResponse());
        } catch (error) {
            console.error('Error in updateSession:', error);
            return ctx.send(createErrorResponse(OCPIStatusCode.SERVER_ERROR, "Server error"), { status: 500 });
        }
    },

    /**
     * OCPI Session-Endpoint PATCH (eMSP → CPO)
     * Aktualisiert Teile einer bestehenden Session
     */
    async patchSession(ctx) {
        try {
            // Token-Validator-Service referenzieren
            const tokenValidator = strapi.service('api::ocpi-common.token-validator');

            // Token validieren
            const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

            if (!validationResult.valid) {
                return ctx.send(createErrorResponse(2000, "Unauthorized"), { status: 401 });
            }

            const sessionId = ctx.params.sessionId;
            const sessionData = ctx.request.body;

            if (!sessionId || !sessionData) {
                return ctx.send(createErrorResponse(2001, "Missing required data"), { status: 400 });
            }

            // Session suchen
            const existingSession = await strapi.documents('api::ocpi-session.ocpi-session').findFirst({
                    filters: { sessionId: sessionId }
                }
            )


            if (!existingSession) {
                return ctx.send(createErrorResponse(2001, "Session not found"), { status: 404 });
            }

            // Konvertiere die eingehenden Daten in das Strapi-Format
            const convertedData = convertFromOcpiSession(sessionData);

            // Bereite die Update-Daten vor, indem wir nur die vorhandenen Felder aktualisieren
            const updateData = {};

            // Für jedes Feld in convertedData prüfen, ob es vorhanden ist
            Object.keys(convertedData).forEach(key => {
                if (convertedData[key] !== undefined) {
                    // Spezialbehandlung für chargingPeriods (Array-Felder)
                    if (key === 'chargingPeriods' && Array.isArray(convertedData[key])) {
                        // Bestehende chargingPeriods holen
                        const existingPeriods = existingSession.chargingPeriods as unknown as any || [];
                        // Neue Perioden hinzufügen, nicht überschreiben
                        updateData[key] = [...existingPeriods, ...convertedData[key]];
                    } else {
                        // Normale Felder einfach aktualisieren
                        updateData[key] = convertedData[key];
                    }
                }
            });

            // Session aktualisieren mit den vorbereiteten Daten
            const session = await strapi.documents('api::ocpi-session.ocpi-session').update({
                documentId: existingSession.documentId,
                data: updateData
            });

            return ctx.send(createSuccessResponse());
        } catch (error) {
            console.error('Error in patchSession:', error);
            return ctx.send(createErrorResponse(3000, "Server error"), { status: 500 });
        }
    }
};

/**
 * Konvertiert eine Strapi-Session in ein OCPI-Session-Objekt
 */
function convertToOcpiSession(session) {
    const result = {
        id: session.sessionId,
        country_code: session.countryCode,
        party_id: session.partyId,
        start_datetime: session.startTime,
        end_datetime: session.endTime || null,
        kwh: parseFloat(session.kwh) || 0,
        total_cost: {
            excl_vat: parseFloat(session.totalCost) || 0,
            currency: session.currency
        },
        auth_method: session.authMethod || null,
        authorization_reference: session.authorizationReference || null,
        currency: session.currency,
        ocpiStatus: session.status,
        last_updated: session.lastUpdated || session.updatedAt,
        location_id: session.locationId,
        evse_uid: session.evseUid,
        connector_id: session.connectorId,
        cdr_token: session.cdrToken || null,
        charging_periods: session.chargingPeriods || [],
    };
    return result;
}

/**
 * Konvertiert ein OCPI-Session-Objekt in ein Strapi-Session-Objekt
 */
function convertFromOcpiSession(ocpiSession) {
    return {
        startTime: ocpiSession.start_date_time || ocpiSession.start_datetime,
        endTime: ocpiSession.end_date_time || ocpiSession.end_datetime || null,
        kwh: ocpiSession.kwh || 0,
        totalCost: ocpiSession.total_cost?.excl_vat || ocpiSession.total_cost?.value || 0,
        currency: ocpiSession.currency || ocpiSession.total_cost?.currency || "EUR",
        ocpiStatus: ocpiSession.status,
        countryCode: ocpiSession.country_code,
        partyId: ocpiSession.party_id,
        locationId: ocpiSession.location_id,
        evseUid: ocpiSession.evse_uid,
        connectorId: ocpiSession.connector_id,
        authMethod: ocpiSession.auth_method,
        authorizationReference: ocpiSession.authorization_reference,
        cdrToken: ocpiSession.cdr_token,
        chargingPeriods: ocpiSession.charging_periods,
        lastUpdated: ocpiSession.last_updated
    };
}

