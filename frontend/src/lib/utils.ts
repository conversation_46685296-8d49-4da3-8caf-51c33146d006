import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility-Funktion zum Zusammenführen von Tailwind-CSS-Klassen
 * Kombiniert clsx und tailwind-merge für optimale Klassenverarbeitung
 */
export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

/**
 * Formatiert ein Datum im deutschen Format (TT.MM.YYYY)
 */
export function formatDate(dateString: string): string {
	const date = new Date(dateString);
	return date.toLocaleDateString("de-DE", {
		day: "2-digit",
		month: "2-digit",
		year: "numeric",
	});
}

/**
 * Formatiert ein Datum mit Uhrzeit im deutschen Format (TT.MM.YYYY, HH:MM)
 */
export function formatDateTime(dateString: string): string {
	const date = new Date(dateString);
	return date.toLocaleDateString("de-DE", {
		day: "2-digit",
		month: "2-digit",
		year: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	});
}

/**
 * Formatiert eine Zeitdauer in Millisekunden in ein lesbares Format (HH:MM:SS)
 */
export function formatDuration(milliseconds: number): string {
	const seconds = Math.floor(milliseconds / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);

	const remainingMinutes = minutes % 60;
	const remainingSeconds = seconds % 60;

	if (hours > 0) {
		return `${hours}:${remainingMinutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
	}
	return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}
