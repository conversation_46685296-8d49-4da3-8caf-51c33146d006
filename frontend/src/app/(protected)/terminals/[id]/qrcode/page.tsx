"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { InvoiceQRCode } from "~/components/invoice/InvoiceQRCode";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";
import { apiClient } from "~/services/api";

interface Terminal {
	id: string;
	attributes: {
		serialNumber: string;
		terminalName: string;
	};
}

interface PaymentSession {
	id: string;
	documentId: string;
	sessionId: string;
}

export default function TerminalQRCodePage() {
	const { id } = useParams();
	const [terminal, setTerminal] = useState<Terminal | null>(null);
	const [activeSession, setActiveSession] = useState<PaymentSession | null>(
		null,
	);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchTerminal = async () => {
			try {
				setLoading(true);
				const response = await apiClient.get(`/api/terminals/${id}`);
				setTerminal(response.data.data);

				// Aktive Session für das Terminal abrufen
				const sessionsResponse = await apiClient.get(
					`/api/payment-sessions?filters[terminal][id][$eq]=${id}&filters[state][$in][0]=started&filters[state][$in][1]=authorized&sort=createdAt:desc&pagination[limit]=1`,
				);

				if (
					sessionsResponse.data.data &&
					sessionsResponse.data.data.length > 0
				) {
					setActiveSession(sessionsResponse.data.data[0]);
				}
			} catch (err) {
				console.error("Error fetching terminal:", err);
				setError("Fehler beim Laden der Terminal-Daten.");
			} finally {
				setLoading(false);
			}
		};

		if (id) {
			fetchTerminal();
		}
	}, [id]);

	if (loading) {
		return (
			<div className="container py-6">
				<Skeleton className="mb-6 h-8 w-1/3" />
				<Skeleton className="mx-auto h-64 w-full max-w-sm" />
			</div>
		);
	}

	if (error || !terminal) {
		return (
			<div className="container py-6">
				<Card>
					<CardHeader>
						<CardTitle>Fehler</CardTitle>
					</CardHeader>
					<CardContent>
						<p>{error || "Terminal nicht gefunden"}</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container py-6">
			<h1 className="mb-6 font-bold text-2xl">
				QR-Code für Terminal: {terminal.attributes.terminalName}
			</h1>

			{activeSession ? (
				<div>
					<p className="mb-4 text-center">
						Aktive Sitzung gefunden. QR-Code für Rechnungsabruf:
					</p>
					<InvoiceQRCode sessionId={activeSession.documentId} />
				</div>
			) : (
				<Card>
					<CardHeader>
						<CardTitle>Keine aktive Sitzung</CardTitle>
					</CardHeader>
					<CardContent>
						<p>
							Für dieses Terminal ist derzeit keine aktive Ladesitzung
							vorhanden. Sobald ein Ladevorgang gestartet wird, wird hier ein
							QR-Code angezeigt.
						</p>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
