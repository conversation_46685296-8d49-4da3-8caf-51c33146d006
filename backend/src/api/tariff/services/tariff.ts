/**
 * tariff service
 */

import { factories } from '@strapi/strapi';

interface Price {
  sessionFee: number;
  pricePerKwh: number;
  perMinute: number;
  gracePeriod: number;
  maxBlockFee: number;
}

// Interface für einen OCPI Connector
interface OCPIConnector {
  id?: string;
  standard?: string;
  format?: string;
  powerType?: string;
  voltage?: number;
  amperage?: number;
  tariffId?: string;
  [key: string]: any; // Für andere mögliche Eigenschaften
}

export default factories.createCoreService('api::tariff.tariff', ({strapi}) => ({
  async getTariff(evseDocumentId: string, datetimeForPrice: Date): Promise<Price> {
    // Default price in case no tariff is found
    const defaultPrice: Price = {
      sessionFee: 0,
      pricePerKwh: 0,
      perMinute: 0,
      gracePeriod: 0,
      maxBlockFee: 0
    };

    try {
      // Get the EVSE by uid
      const evse = await strapi.documents('api::ocpi-evse.ocpi-evse').findOne({
        documentId: evseDocumentId,
        populate: {
          terminals: true,
          location: {
            populate: {
              mandant: true
            }
          }
        }
      });

      if (!evse) {
        return defaultPrice;
      }

      const currentDay = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][datetimeForPrice.getDay()];
      const currentHour = datetimeForPrice.getHours();

      // Determine charger type (AC/DC) from connectors
      let chargerType = 'AC';
      if (evse.connectors && Array.isArray(evse.connectors)) {
        for (const connector of evse.connectors) {
          // Typprüfung und Typumwandlung für den Connector
          const typedConnector = connector as unknown as OCPIConnector;

          if (typedConnector.powerType && typeof typedConnector.powerType === 'string' &&
              typedConnector.powerType.startsWith('DC')) {
            chargerType = 'DC';
            break;
          }
        }
      }

      // Priority 1: Check if tariff is linked directly to EVSE
      let tariff = await strapi.db.query('api::tariff.tariff').findOne({
        where: {
          ocpi_evses: { documentId: evse.documentId },
          chargerType,
          ValidFrom: { $lte: datetimeForPrice },
          ValidTo: { $gte: datetimeForPrice },
          publishedAt: { $notNull: true }
        },
        populate: {
          dailySchedules: {
            populate: {
              HourlyRate: true
            }
          },
          blockFeeSchedules: true
        }
      });

      // Priority 2: Check if tariff is linked to terminal
      if (!tariff && evse.terminals && evse.terminals.length > 0) {
        const terminal = evse.terminals[0];
        tariff = await strapi.db.query('api::tariff.tariff').findOne({
          where: {
            terminals: { documentId: terminal.documentId },
            chargerType,
            ValidFrom: { $lte: datetimeForPrice },
            ValidTo: { $gte: datetimeForPrice },
            publishedAt: { $notNull: true }
          },
          populate: {
            dailySchedules: {
              populate: {
                HourlyRate: true
              }
            },
            blockFeeSchedules: true
          }
        });
      }

      // Priority 3: Check if tariff is linked to location
      if (!tariff && evse.location) {
        tariff = await strapi.db.query('api::tariff.tariff').findOne({
          where: {
            ocpi_locations: { documentId: evse.location.documentId },
            chargerType,
            ValidFrom: { $lte: datetimeForPrice },
            ValidTo: { $gte: datetimeForPrice },
            publishedAt: { $notNull: true }
          },
          populate: {
            dailySchedules: {
              populate: {
                HourlyRate: true
              }
            },
            blockFeeSchedules: true
          }
        });
      }

      // Priority 4: Check if tariff is linked to mandant
      if (!tariff && evse.location?.mandant) {
        tariff = await strapi.db.query('api::tariff.tariff').findOne({
          where: {
            mandants: { documentId: evse.location.mandant.documentId },
            chargerType,
            ValidFrom: { $lte: datetimeForPrice },
            ValidTo: { $gte: datetimeForPrice },
            publishedAt: { $notNull: true }
          },
          populate: {
            dailySchedules: {
              populate: {
                HourlyRate: true
              }
            },
            blockFeeSchedules: true
          }
        });
      }

      // If no tariff found, return default price
      if (!tariff) {
        return defaultPrice;
      }

      // Find the daily schedule for the current day
      const dailySchedule = tariff.dailySchedules?.find(
        schedule => schedule.dayOfWeek === currentDay
      );

      // Find the hourly rate for the current hour
      const hourlyRate = dailySchedule?.HourlyRate?.find(
        rate => currentHour >= rate.hourFrom && (rate.hourTo === undefined || currentHour < rate.hourTo)
      );

      // Find the block fee schedule for the current day and hour
      const blockFeeSchedule = tariff.blockFeeSchedules?.find(
        schedule =>
          schedule.dayOfWeek === currentDay &&
          currentHour >= schedule.startHour &&
          currentHour <= schedule.endHour
      );

      // Construct and return the price object
      return {
        sessionFee: hourlyRate?.sessionFee || 0,
        pricePerKwh: hourlyRate?.pricePerKwh || 0,
        perMinute: blockFeeSchedule?.perMinute || 0,
        gracePeriod: blockFeeSchedule?.gracePeriod || 0,
        maxBlockFee: blockFeeSchedule?.maxFee || 0
      };
    } catch (error) {
      console.error('Error in getTariff service:', error);
      return defaultPrice;
    }
  }
}));
