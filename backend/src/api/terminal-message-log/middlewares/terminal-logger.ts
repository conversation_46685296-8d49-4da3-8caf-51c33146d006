// backend/src/api/terminal-message-log/middlewares/terminal-logger.ts
/**
 * Terminal Logger Middleware
 *
 * Diese Middleware fängt alle Anfragen an bestimmte Terminal-bezogene Endpunkte ab
 * und protokolliert sie in der terminal-message-log-Tabelle.
 *
 * Die Middleware kann in der middlewares.js konfiguriert werden, um bestimmte Routen zu überwachen.
 */

module.exports = (config, { strapi }) => {
  return async (ctx, next) => {
    // Prüfen, ob die Anfrage protokolliert werden soll
    const shouldLog = config.endpoints && Array.isArray(config.endpoints) &&
                     config.endpoints.some(endpoint => ctx.url.includes(endpoint));

    if (!shouldLog) {
      return next();
    }

    // Speichern der Anfragedaten vor der Verarbeitung
    const requestData = {
      method: ctx.method,
      url: ctx.url,
      params: ctx.params,
      query: ctx.query,
      body: ctx.request.body,
      headers: ctx.headers
    };

    // Extrahieren der Terminal-ID aus der Anfrage
    const terminalId = extractTerminalId(ctx);
    if (!terminalId) {
      return next();
    }

    // Bestimmen der Richtung basierend auf der URL
    const requestDirection = getRequestDirection(ctx);

    // Anfrage protokollieren
    try {
      const logger = strapi.service('api::terminal-message-log.terminal-message-log');
      // Mandant-ID ermitteln
      const mandantDocumentId = await getMandantDocumentId(ctx, terminalId, strapi);

      await logger.log({
        terminalId,
        direction: requestDirection,
        payload: requestData,
        messageType: 'info',
        mandantDocumentId: mandantDocumentId
      });
    } catch (logError) {
      console.error('Fehler beim Protokollieren der Terminal-Anfrage:', logError);
    }

    // Originale Antwort verarbeiten
    let responseData = null;
    let responseError = null;

    try {
      // Anfrage weiterleiten und Antwort abwarten
      await next();

      // Antwortdaten speichern
      responseData = {
        status: ctx.status,
        body: ctx.body,
        type: ctx.type
      };
    } catch (error) {
      // Fehler speichern
      responseError = {
        message: error.message,
        stack: error.stack,
        status: error.status || 500
      };
      throw error;
    } finally {
      // Antwort protokollieren (auch im Fehlerfall)
      if (terminalId) {
        try {
          const logger = strapi.service('api::terminal-message-log.terminal-message-log');
          // Umkehren der Richtung für die Antwort
          const responseDirection = reverseDirection(requestDirection);

          // Mandant-ID erneut ermitteln (hier als mandantId)
          const mandantId = await getMandantDocumentId(ctx, terminalId, strapi);

          await logger.log({
            terminalId,
            direction: responseDirection,
            payload: responseError || responseData,
            messageType: responseError ? 'error' : 'info',
            mandantId
          });
        } catch (logError) {
          console.error('Fehler beim Protokollieren der Terminal-Antwort:', logError);
        }
      }
    }
  };
};

/**
 * Extrahiert die Terminal-ID aus der Anfrage
 */
function extractTerminalId(ctx) {
  // Aus URL-Parameter
  if (ctx.params && ctx.params.id) {
    return ctx.params.id;
  }

  // Aus URL-Pfad extrahieren (z.B. /api/terminals/123456)
  const urlMatch = ctx.url.match(/\/terminals\/([^\/\?]+)/);
  if (urlMatch && urlMatch[1]) {
    return urlMatch[1];
  }

  // Aus Body extrahieren
  if (ctx.request.body) {
    const body = ctx.request.body;
    if (body.terminalId || body.serialNumber || body.terminal_id) {
      return body.terminalId || body.serialNumber || body.terminal_id;
    }
  }

  // Aus Query-Parameter
  if (ctx.query && ctx.query.terminalId) {
    return ctx.query.terminalId;
  }

  return null;
}

/**
 * Bestimmt die Richtung der Anfrage anhand der URL.
 * - /api/payter/callback => TerminalToServer
 * - /api/payter (ohne /callback) => WebToServer
 */
function getRequestDirection(ctx) {
  const url = ctx.url;

  switch (true) {
    case url.includes('/api/payter/callback') || url.includes('/api/payter/cardInfoCallback'):
      return 'TerminalToServer';

    case url.includes('/api/payter'):
      return 'WebToServer';

    default:
      return 'Unknown';
  }
}


/**
 * Kehrt die Richtung der Nachricht um:
 * - TerminalToServer => ServerToTerminal
 * - WebToServer => ServerToWeb
 */
function reverseDirection(direction) {
  if (direction === 'TerminalToServer') return 'ServerToTerminal';
  if (direction === 'WebToServer') return 'ServerToWeb';
  return 'Unknown';
}

/**
 * Ermittelt die Mandant-ID entweder aus dem Benutzerkontext oder über die Terminal-ID
 */
async function getMandantDocumentId(ctx, terminalId, strapi) {
  // 1. Versuch: Aus dem User-State
  if (ctx.state && ctx.state.user && ctx.state.user.mandant) {
    return ctx.state.user.mandant.documentId;
  }

  // 2. Versuch: Falls keine Mandant-ID im State, versuche sie über die Terminal-ID zu ermitteln
  if (terminalId) {
    try {
      // Terminal abfragen, um den zugehörigen Mandanten zu finden
      const terminalResult = await strapi.documents('api::terminal.terminal').findMany({
        filters: { serialNumber: terminalId },
        populate: { mandant: true }
      });

      // In Strapi 5 gibt findMany immer ein Array zurück, auch für single types
      if (terminalResult && terminalResult.length > 0 && terminalResult[0].mandant) {
        return terminalResult[0].mandant.documentId;
      }
    } catch (error) {
      console.error(`Fehler beim Ermitteln des Mandanten für Terminal ${terminalId}:`, error);
    }
  }

  return null;
}
