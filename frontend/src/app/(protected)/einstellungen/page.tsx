"use client";

import { useEffect, useState } from "react";
import { type WebhookResponse, payterWebhookApi } from "~/services/api";

interface GlobalConfig {
	id?: string;
	payterApiUrlProd: string;
	payterApiUrlTest: string;
	payterApiUrlDev: string;
	applicationName: string;
}

interface WebhookStatus {
	prod: {
		isLoading: boolean;
		message: string;
		isError: boolean;
	};
	test: {
		isLoading: boolean;
		message: string;
		isError: boolean;
	};
	dev: {
		isLoading: boolean;
		message: string;
		isError: boolean;
	};
}

export default function SettingsPage() {
	const [globalConfig, setGlobalConfig] = useState<GlobalConfig>({
		payterApiUrlProd: "",
		payterApiUrlTest: "",
		payterApiUrlDev: "",
		applicationName: "EulektroTerminalVerwaltung",
	});

	const [isLoading, setIsLoading] = useState(true);
	const [isSaving, setIsSaving] = useState(false);
	const [saveMessage, setSaveMessage] = useState("");

	// Status für die Webhook-Initialisierung
	const [webhookStatus, setWebhookStatus] = useState<WebhookStatus>({
		prod: { isLoading: false, message: "", isError: false },
		test: { isLoading: false, message: "", isError: false },
		dev: { isLoading: false, message: "", isError: false },
	});

	// Daten beim Seitenaufruf laden
	useEffect(() => {
		const fetchData = async () => {
			try {
				setIsLoading(true);
			} catch (error) {
				console.error("Fehler beim Laden der Konfiguration:", error);
			} finally {
				setIsLoading(false);
			}
		};

		fetchData();
	}, []);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setGlobalConfig((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setIsSaving(true);
		setSaveMessage("");

		try {
			setSaveMessage("Einstellungen wurden erfolgreich gespeichert.");
		} catch (error) {
			console.error("Fehler beim Speichern der Einstellungen:", error);
			setSaveMessage("Fehler beim Speichern der Einstellungen.");
		} finally {
			setIsSaving(false);

			// Nachricht nach 3 Sekunden ausblenden
			setTimeout(() => {
				setSaveMessage("");
			}, 3000);
		}
	};

	// Webhook für eine bestimmte Umgebung initialisieren
	const handleInitWebhook = async (environment: "prod" | "test" | "dev") => {
		// Status aktualisieren
		setWebhookStatus((prev) => ({
			...prev,
			[environment]: { isLoading: true, message: "", isError: false },
		}));

		try {
			// Webhook registrieren - das Backend kümmert sich um alle Details
			const response = await payterWebhookApi.registerWebhook(environment);

			// Erfolg oder Fehler anzeigen
			if (response.status_code === 1000) {
				setWebhookStatus((prev) => ({
					...prev,
					[environment]: {
						isLoading: false,
						message: "Webhook erfolgreich initialisiert.",
						isError: false,
					},
				}));
			} else {
				setWebhookStatus((prev) => ({
					...prev,
					[environment]: {
						isLoading: false,
						message: `Fehler: ${response.status_message}`,
						isError: true,
					},
				}));
			}
		} catch (error: any) {
			console.error(
				`Fehler bei der Webhook-Initialisierung für ${environment}:`,
				error,
			);
			setWebhookStatus((prev) => ({
				...prev,
				[environment]: {
					isLoading: false,
					message: `Fehler bei der Webhook-Initialisierung: ${error.message || "Unbekannter Fehler"}`,
					isError: true,
				},
			}));
		}

		// Nachricht nach 5 Sekunden ausblenden
		setTimeout(() => {
			setWebhookStatus((prev) => ({
				...prev,
				[environment]: {
					...prev[environment],
					message: "",
				},
			}));
		}, 5000);
	};

	return (
		<div>
			<div className="mb-6 rounded-lg bg-white p-6 shadow">
				<h1 className="mb-4 font-bold text-2xl text-gray-900">Einstellungen</h1>
				<p className="mb-4 text-gray-600">
					Hier können Sie globale Einstellungen für die Anwendung konfigurieren.
				</p>
			</div>

			{isLoading ? (
				<div className="flex justify-center rounded-lg bg-white p-6 shadow">
					<div className="text-gray-600">Lade Einstellungen...</div>
				</div>
			) : (
				<div className="rounded-lg bg-white p-6 shadow">
					<h2 className="mb-6 font-semibold text-xl">API-Einstellungen</h2>

					<form onSubmit={handleSubmit} className="space-y-6">
						<div className="space-y-4">
							<div>
								<label
									htmlFor="applicationName"
									className="block font-medium text-gray-700 text-sm"
								>
									Anwendungsname
								</label>
								<input
									type="text"
									name="applicationName"
									id="applicationName"
									value={globalConfig.applicationName}
									onChange={handleChange}
									className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
								/>
							</div>

							<div className="border-gray-200 border-t pt-4">
								<h3 className="mb-3 font-medium text-gray-900 text-lg">
									Payter API URLs
								</h3>

								<div className="mb-4">
									<label
										htmlFor="payterApiUrlProd"
										className="block font-medium text-gray-700 text-sm"
									>
										Produktions-URL
									</label>
									<input
										type="text"
										name="payterApiUrlProd"
										id="payterApiUrlProd"
										value={globalConfig.payterApiUrlProd}
										onChange={handleChange}
										placeholder="https://api.payter.eu/production"
										className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
									/>
									<p className="mt-1 text-gray-500 text-xs">
										API-URL für die Produktionsumgebung (Live-System)
									</p>
									<div className="mt-2">
										<button
											type="button"
											onClick={() => handleInitWebhook("prod")}
											disabled={webhookStatus.prod.isLoading}
											className="btn-primary"
										>
											{webhookStatus.prod.isLoading
												? "Wird initialisiert..."
												: "Webhook initialisieren"}
										</button>
										{webhookStatus.prod.message && (
											<p
												className={`mt-1 text-xs ${webhookStatus.prod.isError ? "text-red-500" : "text-green-500"}`}
											>
												{webhookStatus.prod.message}
											</p>
										)}
									</div>
								</div>

								<div className="mb-4">
									<label
										htmlFor="payterApiUrlTest"
										className="block font-medium text-gray-700 text-sm"
									>
										Test-URL
									</label>
									<input
										type="text"
										name="payterApiUrlTest"
										id="payterApiUrlTest"
										value={globalConfig.payterApiUrlTest}
										onChange={handleChange}
										placeholder="https://api.payter.eu/test"
										className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
									/>
									<p className="mt-1 text-gray-500 text-xs">
										API-URL für die Testumgebung
									</p>
									<div className="mt-2">
										<button
											type="button"
											onClick={() => handleInitWebhook("test")}
											disabled={webhookStatus.test.isLoading}
											className="btn-primary"
										>
											{webhookStatus.test.isLoading
												? "Wird initialisiert..."
												: "Webhook initialisieren"}
										</button>
										{webhookStatus.test.message && (
											<p
												className={`mt-1 text-xs ${webhookStatus.test.isError ? "text-red-500" : "text-green-500"}`}
											>
												{webhookStatus.test.message}
											</p>
										)}
									</div>
								</div>
							</div>
						</div>

						<div className="flex justify-end">
							<button type="submit" disabled={isSaving} className="btn-primary">
								{isSaving ? "Wird gespeichert..." : "Einstellungen speichern"}
							</button>
						</div>

						{saveMessage && (
							<div
								className={`mt-4 rounded p-3 ${saveMessage.includes("Fehler") ? "bg-red-50 text-red-700" : "bg-green-50 text-green-700"}`}
							>
								{saveMessage}
							</div>
						)}
					</form>
				</div>
			)}
		</div>
	);
}
