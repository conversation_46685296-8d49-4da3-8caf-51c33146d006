import axios from "axios";

/**
 * API-Client für öffentliche Endpunkte
 * Dieser Client erfordert keine Authentifizierung
 */
export const publicApiClient = axios.create({
	baseURL: process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337",
	headers: {
		"Content-Type": "application/json",
	},
});

/**
 * Service für öffentliche API-Endpunkte
 */
export const publicApiService = {
	/**
	 * Ruft öffentliche Informationen zu einer Payment-Session ab
	 * @param sessionId Die ID der Payment-Session
	 * @returns Die öffentlichen Informationen zur Payment-Session
	 */
	getPaymentSession: async (sessionId: string) => {
		try {
			const response = await publicApiClient.get(
				`/api/public/payment-sessions/${sessionId}`,
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching public payment session:", error);
			throw error;
		}
	},

	/**
	 * Ruft öffentliche Informationen zu einem Terminal für QR-Codes ab
	 * @param terminalId Die ID oder Seriennummer des Terminals
	 * @returns Die öffentlichen Informationen zum Terminal
	 */
	getTerminalQrData: async (terminalId: string) => {
		try {
			const response = await publicApiClient.get(
				`/api/terminal/qr/public/${terminalId}`,
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching public terminal QR data:", error);
			throw error;
		}
	},

	/**
	 * Speichert die E-Mail-Adresse für eine Payment-Session
	 * @param sessionId Die ID der Payment-Session
	 * @param email Die zu speichernde E-Mail-Adresse
	 * @returns Die aktualisierte Payment-Session
	 */
	savePaymentSessionEmail: async (sessionId: string, email: string) => {
		try {
			const response = await publicApiClient.post(
				`/api/public/payment-sessions/${sessionId}/email`,
				{
					email: email,
				},
			);
			return response.data;
		} catch (error) {
			console.error("Error saving payment session email:", error);
			throw error;
		}
	},

	/**
	 * Prüft, ob eine Rechnung für eine Payment-Session verfügbar ist
	 * @param sessionId Die ID der Payment-Session
	 * @returns Die Payment-Session mit Rechnungsinformationen
	 */
	checkInvoiceAvailability: async (sessionId: string) => {
		try {
			const response = await publicApiClient.get(
				`/api/public/payment-sessions/${sessionId}/invoice`,
			);
			return response.data;
		} catch (error) {
			console.error("Error checking invoice availability:", error);
			throw error;
		}
	},
};
