// src/api/ocpi-2.2.1-credentials/routes/credential.ts
'use strict';

export default {
    routes: [
        // Bestehende Routen
        {
            method: 'GET',
            path: '/ocpi/2.2.1/credentials',
            handler: 'credentials.getCredentials',
            config: {
                auth: false
            }
        },
        {
            method: 'POST',
            path: '/ocpi/2.2.1/credentials',
            handler: 'credentials.postCredentials',
            config: {
                auth: false
            }
        },
        // Neue Route für DELETE
        {
            method: 'DELETE',
            path: '/ocpi/2.2.1/credentials',
            handler: 'delete.deleteCredentials',
            config: {
                auth: false
            }
        }
    ],
};
