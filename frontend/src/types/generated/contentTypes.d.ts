import type { Schema, Struct } from "@strapi/strapi";

export interface AdminApiToken extends Struct.CollectionTypeSchema {
	collectionName: "strapi_api_tokens";
	info: {
		description: "";
		displayName: "Api Token";
		name: "Api Token";
		pluralName: "api-tokens";
		singularName: "api-token";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		accessKey: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		description: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}> &
			Schema.Attribute.DefaultTo<"">;
		expiresAt: Schema.Attribute.DateTime;
		lastUsedAt: Schema.Attribute.DateTime;
		lifespan: Schema.Attribute.BigInteger;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<"oneToMany", "admin::api-token"> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		permissions: Schema.Attribute.Relation<
			"oneToMany",
			"admin::api-token-permission"
		>;
		publishedAt: Schema.Attribute.DateTime;
		type: Schema.Attribute.Enumeration<["read-only", "full-access", "custom"]> &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"read-only">;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface AdminApiTokenPermission extends Struct.CollectionTypeSchema {
	collectionName: "strapi_api_token_permissions";
	info: {
		description: "";
		displayName: "API Token Permission";
		name: "API Token Permission";
		pluralName: "api-token-permissions";
		singularName: "api-token-permission";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		action: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"admin::api-token-permission"
		> &
			Schema.Attribute.Private;
		publishedAt: Schema.Attribute.DateTime;
		token: Schema.Attribute.Relation<"manyToOne", "admin::api-token">;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface AdminPermission extends Struct.CollectionTypeSchema {
	collectionName: "admin_permissions";
	info: {
		description: "";
		displayName: "Permission";
		name: "Permission";
		pluralName: "permissions";
		singularName: "permission";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		action: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		actionParameters: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
		conditions: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<[]>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<"oneToMany", "admin::permission"> &
			Schema.Attribute.Private;
		properties: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
		publishedAt: Schema.Attribute.DateTime;
		role: Schema.Attribute.Relation<"manyToOne", "admin::role">;
		subject: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface AdminRole extends Struct.CollectionTypeSchema {
	collectionName: "admin_roles";
	info: {
		description: "";
		displayName: "Role";
		name: "Role";
		pluralName: "roles";
		singularName: "role";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		code: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		description: Schema.Attribute.String;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<"oneToMany", "admin::role"> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		permissions: Schema.Attribute.Relation<"oneToMany", "admin::permission">;
		publishedAt: Schema.Attribute.DateTime;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		users: Schema.Attribute.Relation<"manyToMany", "admin::user">;
	};
}

export interface AdminTransferToken extends Struct.CollectionTypeSchema {
	collectionName: "strapi_transfer_tokens";
	info: {
		description: "";
		displayName: "Transfer Token";
		name: "Transfer Token";
		pluralName: "transfer-tokens";
		singularName: "transfer-token";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		accessKey: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		description: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}> &
			Schema.Attribute.DefaultTo<"">;
		expiresAt: Schema.Attribute.DateTime;
		lastUsedAt: Schema.Attribute.DateTime;
		lifespan: Schema.Attribute.BigInteger;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"admin::transfer-token"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		permissions: Schema.Attribute.Relation<
			"oneToMany",
			"admin::transfer-token-permission"
		>;
		publishedAt: Schema.Attribute.DateTime;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface AdminTransferTokenPermission
	extends Struct.CollectionTypeSchema {
	collectionName: "strapi_transfer_token_permissions";
	info: {
		description: "";
		displayName: "Transfer Token Permission";
		name: "Transfer Token Permission";
		pluralName: "transfer-token-permissions";
		singularName: "transfer-token-permission";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		action: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"admin::transfer-token-permission"
		> &
			Schema.Attribute.Private;
		publishedAt: Schema.Attribute.DateTime;
		token: Schema.Attribute.Relation<"manyToOne", "admin::transfer-token">;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface AdminUser extends Struct.CollectionTypeSchema {
	collectionName: "admin_users";
	info: {
		description: "";
		displayName: "User";
		name: "User";
		pluralName: "users";
		singularName: "user";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		blocked: Schema.Attribute.Boolean &
			Schema.Attribute.Private &
			Schema.Attribute.DefaultTo<false>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		email: Schema.Attribute.Email &
			Schema.Attribute.Required &
			Schema.Attribute.Private &
			Schema.Attribute.Unique &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 6;
			}>;
		firstname: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		isActive: Schema.Attribute.Boolean &
			Schema.Attribute.Private &
			Schema.Attribute.DefaultTo<false>;
		lastname: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<"oneToMany", "admin::user"> &
			Schema.Attribute.Private;
		password: Schema.Attribute.Password &
			Schema.Attribute.Private &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 6;
			}>;
		preferedLanguage: Schema.Attribute.String;
		publishedAt: Schema.Attribute.DateTime;
		registrationToken: Schema.Attribute.String & Schema.Attribute.Private;
		resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
		roles: Schema.Attribute.Relation<"manyToMany", "admin::role"> &
			Schema.Attribute.Private;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		username: Schema.Attribute.String;
	};
}

export interface ApiConnectorConnector extends Struct.CollectionTypeSchema {
	collectionName: "connectors";
	info: {
		displayName: "Connector";
		pluralName: "connectors";
		singularName: "connector";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		amperage: Schema.Attribute.Integer;
		connectorId: Schema.Attribute.String & Schema.Attribute.Required;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::connector.connector"
		> &
			Schema.Attribute.Private;
		power: Schema.Attribute.Decimal & Schema.Attribute.Required;
		publishedAt: Schema.Attribute.DateTime;
		status: Schema.Attribute.String;
		type: Schema.Attribute.Enumeration<["AC", "DC", "OTHER"]> &
			Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		voltage: Schema.Attribute.Integer;
	};
}

export interface ApiGlobalConfigGlobalConfig extends Struct.SingleTypeSchema {
	collectionName: "global_configs";
	info: {
		description: "Globale Konfigurationseinstellungen f\u00FCr die Anwendung";
		displayName: "GlobalConfig";
		pluralName: "global-configs";
		singularName: "global-config";
	};
	options: {
		draftAndPublish: true;
	};
	attributes: {
		ApplicationName: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"EulektroTerminalVerwaltung">;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::global-config.global-config"
		> &
			Schema.Attribute.Private;
		PayterApiUrlDev: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"https://api.payter.eu/dev">;
		PayterApiUrlProd: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"https://api.payter.eu/prod">;
		PayterApiUrlTest: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"https://api.payter.eu/test">;
		publishedAt: Schema.Attribute.DateTime;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiInvoiceTemplateInvoiceTemplate
	extends Struct.CollectionTypeSchema {
	collectionName: "invoice_templates";
	info: {
		description: "";
		displayName: "InvoiceTemplate";
		pluralName: "invoice-templates";
		singularName: "invoice-template";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		companyName: Schema.Attribute.String;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		from: Schema.Attribute.Date;
		invoice_prefix: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::invoice-template.invoice-template"
		> &
			Schema.Attribute.Private;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		publishedAt: Schema.Attribute.DateTime;
		to: Schema.Attribute.Date;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		vatId: Schema.Attribute.String;
	};
}

export interface ApiInvoiceInvoice extends Struct.CollectionTypeSchema {
	collectionName: "invoices";
	info: {
		displayName: "Invoice";
		pluralName: "invoices";
		singularName: "invoice";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		invoice_number: Schema.Attribute.String;
		invoiceId: Schema.Attribute.Integer;
		kindOfInvoice: Schema.Attribute.Enumeration<
			["INVOICE", "CREDIT", "STORNO", "CREDIT_STORNO"]
		> &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"INVOICE">;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::invoice.invoice"
		> &
			Schema.Attribute.Private;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		metadata: Schema.Attribute.JSON & Schema.Attribute.DefaultTo<{}>;
		ocpi_cdr: Schema.Attribute.Relation<"oneToOne", "api::ocpi-cdr.ocpi-cdr">;
		ocpi_session: Schema.Attribute.Relation<
			"oneToOne",
			"api::ocpi-session.ocpi-session"
		>;
		payment_session: Schema.Attribute.Relation<
			"oneToOne",
			"api::payment-session.payment-session"
		>;
		period_end_utc: Schema.Attribute.DateTime & Schema.Attribute.Required;
		period_start_utc: Schema.Attribute.DateTime & Schema.Attribute.Required;
		publishedAt: Schema.Attribute.DateTime;
		sum_gross: Schema.Attribute.Float & Schema.Attribute.Required;
		sum_net: Schema.Attribute.Float & Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		vat_amount: Schema.Attribute.Float & Schema.Attribute.Required;
		vat_percentage: Schema.Attribute.Float &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMax<
				{
					max: 100;
					min: 0;
				},
				number
			> &
			Schema.Attribute.DefaultTo<19>;
	};
}

export interface ApiMandantMandant extends Struct.CollectionTypeSchema {
	collectionName: "mandants";
	info: {
		description: "";
		displayName: "Mandant";
		pluralName: "mandants";
		singularName: "mandant";
	};
	options: {
		draftAndPublish: true;
	};
	attributes: {
		AcPrice: Schema.Attribute.Component<"price.price", false>;
		children: Schema.Attribute.Relation<"oneToMany", "api::mandant.mandant">;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		DcPrice: Schema.Attribute.Component<"price.price", false>;
		invoice_templates: Schema.Attribute.Relation<
			"oneToMany",
			"api::invoice-template.invoice-template"
		>;
		invoices: Schema.Attribute.Relation<"oneToMany", "api::invoice.invoice">;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::mandant.mandant"
		> &
			Schema.Attribute.Private;
		Name: Schema.Attribute.String;
		ocpi_cdrs: Schema.Attribute.Relation<"oneToMany", "api::ocpi-cdr.ocpi-cdr">;
		ocpi_commands: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-command.ocpi-command"
		>;
		ocpi_connections: Schema.Attribute.Relation<
			"manyToMany",
			"api::ocpi-connection.ocpi-connection"
		>;
		ocpi_locations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-location.ocpi-location"
		>;
		ocpi_logs: Schema.Attribute.Relation<"oneToMany", "api::ocpi-log.ocpi-log">;
		ocpi_sessions: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-session.ocpi-session"
		>;
		parent: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		payment_sessions: Schema.Attribute.Relation<
			"oneToMany",
			"api::payment-session.payment-session"
		>;
		payter_connection: Schema.Attribute.Relation<
			"manyToOne",
			"api::payter-connection.payter-connection"
		>;
		publishedAt: Schema.Attribute.DateTime;
		tariff: Schema.Attribute.Relation<"manyToOne", "api::tariff.tariff">;
		terminal_message_logs: Schema.Attribute.Relation<
			"oneToMany",
			"api::terminal-message-log.terminal-message-log"
		>;
		terminals: Schema.Attribute.Relation<"oneToMany", "api::terminal.terminal">;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		users: Schema.Attribute.Relation<
			"manyToMany",
			"plugin::users-permissions.user"
		>;
	};
}

export interface ApiOcpiCdrOcpiCdr extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_cdrs";
	info: {
		displayName: "OCPI CDR";
		pluralName: "ocpi-cdrs";
		singularName: "ocpi-cdr";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		authMethod: Schema.Attribute.String;
		authorizationReference: Schema.Attribute.String;
		cdrId: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		cdrLocation: Schema.Attribute.Component<"ocpi.cdr-location", false>;
		cdrToken: Schema.Attribute.Component<"ocpi.cdr-token", false>;
		chargingPeriods: Schema.Attribute.JSON;
		countryCode: Schema.Attribute.String;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		currency: Schema.Attribute.String & Schema.Attribute.Required;
		endDateTime: Schema.Attribute.DateTime;
		lastUpdated: Schema.Attribute.DateTime;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-cdr.ocpi-cdr"
		> &
			Schema.Attribute.Private;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		partyId: Schema.Attribute.String;
		payment_session: Schema.Attribute.Relation<
			"oneToOne",
			"api::payment-session.payment-session"
		>;
		publishedAt: Schema.Attribute.DateTime;
		rawData: Schema.Attribute.JSON;
		sessionId: Schema.Attribute.String;
		startDateTime: Schema.Attribute.DateTime;
		tariffs: Schema.Attribute.JSON;
		timestamp: Schema.Attribute.DateTime & Schema.Attribute.Required;
		totalCost: Schema.Attribute.Decimal & Schema.Attribute.Required;
		totalEnergy: Schema.Attribute.Decimal;
		totalEnergyCost: Schema.Attribute.Decimal;
		totalFixedCost: Schema.Attribute.Decimal;
		totalParkingCost: Schema.Attribute.Decimal;
		totalTime: Schema.Attribute.Decimal & Schema.Attribute.Required;
		totalTimeCost: Schema.Attribute.Decimal;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiOcpiCommandOcpiCommand extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_commands";
	info: {
		displayName: "OCPI Command";
		pluralName: "ocpi-commands";
		singularName: "ocpi-command";
	};
	options: {
		draftAndPublish: true;
	};
	attributes: {
		commandId: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-command.ocpi-command"
		> &
			Schema.Attribute.Private;
		payload: Schema.Attribute.JSON;
		publishedAt: Schema.Attribute.DateTime;
		status: Schema.Attribute.Enumeration<
			["pending", "sent", "acknowledged", "failed"]
		> &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"pending">;
		timestamp: Schema.Attribute.DateTime & Schema.Attribute.Required;
		type: Schema.Attribute.Enumeration<["start", "stop", "update"]> &
			Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiOcpiConnectionOcpiConnection
	extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_connections";
	info: {
		description: "Verwaltung von OCPI Verbindungen und deren Credentials";
		displayName: "OCPI Connection";
		pluralName: "ocpi-connections";
		singularName: "ocpi-connection";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		companyName: Schema.Attribute.String;
		connectionStatus: Schema.Attribute.Enumeration<
			["new", "pending", "registered", "active", "inactive", "disconnected"]
		> &
			Schema.Attribute.DefaultTo<"new">;
		connectionUrl: Schema.Attribute.String;
		countryCode: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 2;
			}> &
			Schema.Attribute.DefaultTo<"DE">;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		initialSecret: Schema.Attribute.String;
		lastConnection: Schema.Attribute.DateTime;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-connection.ocpi-connection"
		> &
			Schema.Attribute.Private;
		mandants: Schema.Attribute.Relation<"manyToMany", "api::mandant.mandant">;
		name: Schema.Attribute.String & Schema.Attribute.Required;
		ocpiVersion: Schema.Attribute.Enumeration<["v221"]> &
			Schema.Attribute.DefaultTo<"v221">;
		operatorId: Schema.Attribute.String;
		partyId: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 3;
			}>;
		publishedAt: Schema.Attribute.DateTime;
		receivingSecret: Schema.Attribute.String;
		remoteModules: Schema.Attribute.JSON;
		remoteParty: Schema.Attribute.JSON;
		role: Schema.Attribute.Enumeration<["sender", "receiver"]> &
			Schema.Attribute.Required;
		sendSecret: Schema.Attribute.String & Schema.Attribute.Unique;
		type: Schema.Attribute.Enumeration<["Prod", "Test", "Dev"]>;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiOcpiEvseOcpiEvse extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_evses";
	info: {
		description: "OCPI 2.2.1 EVSE object";
		displayName: "OCPI EVSE";
		pluralName: "ocpi-evses";
		singularName: "ocpi-evse";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		capabilities: Schema.Attribute.JSON;
		connectors: Schema.Attribute.JSON;
		coordinates: Schema.Attribute.Component<"ocpi.geo-location", false>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		directions: Schema.Attribute.Component<"ocpi.display-text", true>;
		evseId: Schema.Attribute.String;
		floorLevel: Schema.Attribute.String;
		images: Schema.Attribute.Component<"ocpi.image", true>;
		labelForTerminal: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 12;
			}>;
		lastUpdated: Schema.Attribute.DateTime & Schema.Attribute.Required;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-evse.ocpi-evse"
		> &
			Schema.Attribute.Private;
		location: Schema.Attribute.Relation<
			"manyToOne",
			"api::ocpi-location.ocpi-location"
		>;
		ocpiStatus: Schema.Attribute.Enumeration<
			[
				"AVAILABLE",
				"BLOCKED",
				"CHARGING",
				"INOPERATIVE",
				"OUTOFORDER",
				"PLANNED",
				"REMOVED",
				"RESERVED",
				"UNKNOWN",
			]
		> &
			Schema.Attribute.DefaultTo<"UNKNOWN">;
		parkingRestrictions: Schema.Attribute.JSON;
		payment_sessions: Schema.Attribute.Relation<
			"oneToMany",
			"api::payment-session.payment-session"
		>;
		physicalReference: Schema.Attribute.String;
		publishedAt: Schema.Attribute.DateTime;
		statusSchedule: Schema.Attribute.Component<"ocpi.status-schedule", true>;
		terminals: Schema.Attribute.Relation<
			"manyToMany",
			"api::terminal.terminal"
		>;
		uid: Schema.Attribute.String & Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiOcpiLocationOcpiLocation
	extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_locations";
	info: {
		description: "OCPI 2.2.1 Location-Objekt";
		displayName: "OCPI Location";
		pluralName: "ocpi-locations";
		singularName: "ocpi-location";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		address: Schema.Attribute.String & Schema.Attribute.Required;
		chargingWhenClosed: Schema.Attribute.Boolean;
		city: Schema.Attribute.String & Schema.Attribute.Required;
		coordinates: Schema.Attribute.Component<"ocpi.geo-location", false>;
		country: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 3;
			}>;
		countryCode: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 2;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		directions: Schema.Attribute.Component<"ocpi.display-text", true>;
		energyMix: Schema.Attribute.Component<"ocpi.energy-mix", false>;
		evses: Schema.Attribute.Relation<"oneToMany", "api::ocpi-evse.ocpi-evse">;
		facilities: Schema.Attribute.JSON;
		images: Schema.Attribute.Component<"ocpi.image", true>;
		lastUpdated: Schema.Attribute.DateTime & Schema.Attribute.Required;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-location.ocpi-location"
		> &
			Schema.Attribute.Private;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		name: Schema.Attribute.String;
		ocpiConnection: Schema.Attribute.Relation<
			"manyToOne",
			"api::ocpi-connection.ocpi-connection"
		>;
		ocpiId: Schema.Attribute.String & Schema.Attribute.Required;
		openingTimes: Schema.Attribute.Component<"ocpi.hours", false>;
		operator: Schema.Attribute.Component<"ocpi.business-details", false>;
		owner: Schema.Attribute.Component<"ocpi.business-details", false>;
		partyId: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 3;
			}>;
		postalCode: Schema.Attribute.String & Schema.Attribute.Required;
		publish: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
		publishAllowedTo: Schema.Attribute.JSON;
		publishedAt: Schema.Attribute.DateTime;
		relatedLocations: Schema.Attribute.Component<
			"ocpi.additional-geo-location",
			true
		>;
		suboperator: Schema.Attribute.Component<"ocpi.business-details", false>;
		terminals: Schema.Attribute.Relation<"oneToMany", "api::terminal.terminal">;
		timeZone: Schema.Attribute.String;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiOcpiLogOcpiLog extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_logs";
	info: {
		displayName: "OCPI Log";
		pluralName: "ocpi-logs";
		singularName: "ocpi-log";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		endpoint: Schema.Attribute.String;
		level: Schema.Attribute.Enumeration<["info", "warn", "error", "debug"]> &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"info">;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-log.ocpi-log"
		> &
			Schema.Attribute.Private;
		logId: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		message: Schema.Attribute.Text & Schema.Attribute.Required;
		publishedAt: Schema.Attribute.DateTime;
		request: Schema.Attribute.JSON;
		response: Schema.Attribute.JSON;
		timestamp: Schema.Attribute.DateTime & Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiOcpiSessionOcpiSession extends Struct.CollectionTypeSchema {
	collectionName: "ocpi_sessions";
	info: {
		displayName: "OCPI Session";
		pluralName: "ocpi-sessions";
		singularName: "ocpi-session";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		authMethod: Schema.Attribute.Enumeration<
			["AUTH_REQUEST", "COMMAND", "WHITELIST"]
		>;
		authorizationReference: Schema.Attribute.String;
		cdrToken: Schema.Attribute.JSON;
		chargingPeriods: Schema.Attribute.JSON;
		connectorId: Schema.Attribute.String;
		countryCode: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 2;
			}>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		currency: Schema.Attribute.String & Schema.Attribute.Required;
		endTime: Schema.Attribute.DateTime;
		kwh: Schema.Attribute.Decimal;
		evseUid: Schema.Attribute.String;
		lastUpdated: Schema.Attribute.DateTime;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-session.ocpi-session"
		> &
			Schema.Attribute.Private;
		locationId: Schema.Attribute.String;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		ocpiStatus: Schema.Attribute.Enumeration<
			["ACTIVE", "COMPLETED", "INVALID", "PENDING", "RESERVATION"]
		> &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"INVALID">;
		partyId: Schema.Attribute.String &
			Schema.Attribute.SetMinMaxLength<{
				maxLength: 3;
			}>;
		payment_session: Schema.Attribute.Relation<
			"oneToOne",
			"api::payment-session.payment-session"
		>;
		publishedAt: Schema.Attribute.DateTime;
		sessionId: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		startTime: Schema.Attribute.DateTime & Schema.Attribute.Required;
		totalCost: Schema.Attribute.Decimal;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiPaymentSessionPaymentSession
	extends Struct.CollectionTypeSchema {
	collectionName: "payment_sessions";
	info: {
		description: "";
		displayName: "PaymentSession";
		pluralName: "payment-sessions";
		singularName: "payment-session";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		authorizationCode: Schema.Attribute.String;
		authorizationHostReference: Schema.Attribute.String;
		authorizedAt: Schema.Attribute.DateTime;
		blockedAmount: Schema.Attribute.Integer;
		brand: Schema.Attribute.String;
		capturedAmount: Schema.Attribute.Integer;
		cardId: Schema.Attribute.String;
		closedAt: Schema.Attribute.DateTime;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		history: Schema.Attribute.JSON;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::payment-session.payment-session"
		> &
			Schema.Attribute.Private;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		maskedPan: Schema.Attribute.String;
		merchantReference: Schema.Attribute.String;
		ocpi_cdr: Schema.Attribute.Relation<"oneToOne", "api::ocpi-cdr.ocpi-cdr">;
		ocpi_evse: Schema.Attribute.Relation<
			"manyToOne",
			"api::ocpi-evse.ocpi-evse"
		>;
		ocpi_session: Schema.Attribute.Relation<
			"oneToOne",
			"api::ocpi-session.ocpi-session"
		>;
		ocppTransactionId: Schema.Attribute.String;
		paymentIntent: Schema.Attribute.String;
		publishedAt: Schema.Attribute.DateTime;
		showPriceDateTime: Schema.Attribute.DateTime;
		state: Schema.Attribute.Enumeration<
			["started", "authorized", "captured", "canceled"]
		>;
		terminal: Schema.Attribute.Relation<"manyToOne", "api::terminal.terminal">;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiPayterConnectionPayterConnection
	extends Struct.CollectionTypeSchema {
	collectionName: "payter_connections";
	info: {
		description: "";
		displayName: "PayterConnection";
		pluralName: "payter-connections";
		singularName: "payter-connection";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		ApiKey: Schema.Attribute.String;
		ApiUrl: Schema.Attribute.String;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::payter-connection.payter-connection"
		> &
			Schema.Attribute.Private;
		mandants: Schema.Attribute.Relation<"oneToMany", "api::mandant.mandant">;
		Name: Schema.Attribute.String;
		publishedAt: Schema.Attribute.DateTime;
		terminals: Schema.Attribute.Relation<"oneToMany", "api::terminal.terminal">;
		Type: Schema.Attribute.Enumeration<["Prod", "Test", "Dev"]>;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiTariffTariff extends Struct.CollectionTypeSchema {
	collectionName: "tariffs";
	info: {
		description: "";
		displayName: "Tariff";
		pluralName: "tariffs";
		singularName: "tariff";
	};
	options: {
		draftAndPublish: true;
	};
	attributes: {
		blockFeeSchedules: Schema.Attribute.Component<
			"tariff.block-fee-schedule",
			true
		>;
		chargerType: Schema.Attribute.Enumeration<["AC", "DC"]>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		dailySchedules: Schema.Attribute.Component<"tariff.daily-schedule", true>;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::tariff.tariff"
		> &
			Schema.Attribute.Private;
		mandants: Schema.Attribute.Relation<"oneToMany", "api::mandant.mandant">;
		Name: Schema.Attribute.String & Schema.Attribute.Required;
		ocpi_evses: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-evse.ocpi-evse"
		>;
		ocpi_locations: Schema.Attribute.Relation<
			"oneToMany",
			"api::ocpi-location.ocpi-location"
		>;
		publishedAt: Schema.Attribute.DateTime;
		terminals: Schema.Attribute.Relation<"oneToMany", "api::terminal.terminal">;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		ValidFrom: Schema.Attribute.DateTime & Schema.Attribute.Required;
		ValidTo: Schema.Attribute.DateTime & Schema.Attribute.Required;
	};
}

export interface ApiTerminalMessageLogTerminalMessageLog
	extends Struct.CollectionTypeSchema {
	collectionName: "terminal_message_logs";
	info: {
		description: "";
		displayName: "TerminalMessageLog";
		pluralName: "terminal-message-logs";
		singularName: "terminal-message-log";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		direction: Schema.Attribute.Enumeration<
			[
				"ServerToTerminal",
				"TerminalToServer",
				"WebToServer",
				"ServerToWeb",
				"Unknown",
			]
		>;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::terminal-message-log.terminal-message-log"
		> &
			Schema.Attribute.Private;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		messageType: Schema.Attribute.Enumeration<
			["info", "warning", "error", "debug"]
		>;
		payload: Schema.Attribute.JSON;
		paymentSessionId: Schema.Attribute.String;
		publishedAt: Schema.Attribute.DateTime;
		terminalId: Schema.Attribute.String;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface ApiTerminalTerminal extends Struct.CollectionTypeSchema {
	collectionName: "terminals";
	info: {
		description: "";
		displayName: "Terminal";
		pluralName: "terminals";
		singularName: "terminal";
	};
	options: {
		draftAndPublish: true;
	};
	attributes: {
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		evses: Schema.Attribute.Relation<"manyToMany", "api::ocpi-evse.ocpi-evse">;
		lastUpdate: Schema.Attribute.DateTime;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"api::terminal.terminal"
		> &
			Schema.Attribute.Private;
		location: Schema.Attribute.Relation<
			"manyToOne",
			"api::ocpi-location.ocpi-location"
		>;
		mandant: Schema.Attribute.Relation<"manyToOne", "api::mandant.mandant">;
		online: Schema.Attribute.Boolean;
		payment_sessions: Schema.Attribute.Relation<
			"oneToMany",
			"api::payment-session.payment-session"
		>;
		payter_connection: Schema.Attribute.Relation<
			"manyToOne",
			"api::payter-connection.payter-connection"
		>;
		publishedAt: Schema.Attribute.DateTime;
		serialNumber: Schema.Attribute.String & Schema.Attribute.Unique;
		state: Schema.Attribute.String;
		terminalName: Schema.Attribute.String;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginContentReleasesRelease
	extends Struct.CollectionTypeSchema {
	collectionName: "strapi_releases";
	info: {
		displayName: "Release";
		pluralName: "releases";
		singularName: "release";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		actions: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::content-releases.release-action"
		>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::content-releases.release"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String & Schema.Attribute.Required;
		publishedAt: Schema.Attribute.DateTime;
		releasedAt: Schema.Attribute.DateTime;
		scheduledAt: Schema.Attribute.DateTime;
		status: Schema.Attribute.Enumeration<
			["ready", "blocked", "failed", "done", "empty"]
		> &
			Schema.Attribute.Required;
		timezone: Schema.Attribute.String;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginContentReleasesReleaseAction
	extends Struct.CollectionTypeSchema {
	collectionName: "strapi_release_actions";
	info: {
		displayName: "Release Action";
		pluralName: "release-actions";
		singularName: "release-action";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		contentType: Schema.Attribute.String & Schema.Attribute.Required;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		entryDocumentId: Schema.Attribute.String;
		isEntryValid: Schema.Attribute.Boolean;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::content-releases.release-action"
		> &
			Schema.Attribute.Private;
		publishedAt: Schema.Attribute.DateTime;
		release: Schema.Attribute.Relation<
			"manyToOne",
			"plugin::content-releases.release"
		>;
		type: Schema.Attribute.Enumeration<["publish", "unpublish"]> &
			Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginI18NLocale extends Struct.CollectionTypeSchema {
	collectionName: "i18n_locale";
	info: {
		collectionName: "locales";
		description: "";
		displayName: "Locale";
		pluralName: "locales";
		singularName: "locale";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		code: Schema.Attribute.String & Schema.Attribute.Unique;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::i18n.locale"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.SetMinMax<
				{
					max: 50;
					min: 1;
				},
				number
			>;
		publishedAt: Schema.Attribute.DateTime;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginReviewWorkflowsWorkflow
	extends Struct.CollectionTypeSchema {
	collectionName: "strapi_workflows";
	info: {
		description: "";
		displayName: "Workflow";
		name: "Workflow";
		pluralName: "workflows";
		singularName: "workflow";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		contentTypes: Schema.Attribute.JSON &
			Schema.Attribute.Required &
			Schema.Attribute.DefaultTo<"[]">;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::review-workflows.workflow"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		publishedAt: Schema.Attribute.DateTime;
		stageRequiredToPublish: Schema.Attribute.Relation<
			"oneToOne",
			"plugin::review-workflows.workflow-stage"
		>;
		stages: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::review-workflows.workflow-stage"
		>;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginReviewWorkflowsWorkflowStage
	extends Struct.CollectionTypeSchema {
	collectionName: "strapi_workflows_stages";
	info: {
		description: "";
		displayName: "Stages";
		name: "Workflow Stage";
		pluralName: "workflow-stages";
		singularName: "workflow-stage";
	};
	options: {
		draftAndPublish: false;
		version: "1.1.0";
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		color: Schema.Attribute.String & Schema.Attribute.DefaultTo<"#4945FF">;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::review-workflows.workflow-stage"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String;
		permissions: Schema.Attribute.Relation<"manyToMany", "admin::permission">;
		publishedAt: Schema.Attribute.DateTime;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		workflow: Schema.Attribute.Relation<
			"manyToOne",
			"plugin::review-workflows.workflow"
		>;
	};
}

export interface PluginUploadFile extends Struct.CollectionTypeSchema {
	collectionName: "files";
	info: {
		description: "";
		displayName: "File";
		pluralName: "files";
		singularName: "file";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		alternativeText: Schema.Attribute.String;
		caption: Schema.Attribute.String;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		ext: Schema.Attribute.String;
		folder: Schema.Attribute.Relation<"manyToOne", "plugin::upload.folder"> &
			Schema.Attribute.Private;
		folderPath: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Private &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		formats: Schema.Attribute.JSON;
		hash: Schema.Attribute.String & Schema.Attribute.Required;
		height: Schema.Attribute.Integer;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::upload.file"
		> &
			Schema.Attribute.Private;
		mime: Schema.Attribute.String & Schema.Attribute.Required;
		name: Schema.Attribute.String & Schema.Attribute.Required;
		previewUrl: Schema.Attribute.String;
		provider: Schema.Attribute.String & Schema.Attribute.Required;
		provider_metadata: Schema.Attribute.JSON;
		publishedAt: Schema.Attribute.DateTime;
		related: Schema.Attribute.Relation<"morphToMany">;
		size: Schema.Attribute.Decimal & Schema.Attribute.Required;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		url: Schema.Attribute.String & Schema.Attribute.Required;
		width: Schema.Attribute.Integer;
	};
}

export interface PluginUploadFolder extends Struct.CollectionTypeSchema {
	collectionName: "upload_folders";
	info: {
		displayName: "Folder";
		pluralName: "folders";
		singularName: "folder";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		children: Schema.Attribute.Relation<"oneToMany", "plugin::upload.folder">;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		files: Schema.Attribute.Relation<"oneToMany", "plugin::upload.file">;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::upload.folder"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		parent: Schema.Attribute.Relation<"manyToOne", "plugin::upload.folder">;
		path: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 1;
			}>;
		pathId: Schema.Attribute.Integer &
			Schema.Attribute.Required &
			Schema.Attribute.Unique;
		publishedAt: Schema.Attribute.DateTime;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginUsersPermissionsPermission
	extends Struct.CollectionTypeSchema {
	collectionName: "up_permissions";
	info: {
		description: "";
		displayName: "Permission";
		name: "permission";
		pluralName: "permissions";
		singularName: "permission";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		action: Schema.Attribute.String & Schema.Attribute.Required;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::users-permissions.permission"
		> &
			Schema.Attribute.Private;
		publishedAt: Schema.Attribute.DateTime;
		role: Schema.Attribute.Relation<
			"manyToOne",
			"plugin::users-permissions.role"
		>;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
	};
}

export interface PluginUsersPermissionsRole
	extends Struct.CollectionTypeSchema {
	collectionName: "up_roles";
	info: {
		description: "";
		displayName: "Role";
		name: "role";
		pluralName: "roles";
		singularName: "role";
	};
	options: {
		draftAndPublish: false;
	};
	pluginOptions: {
		"content-manager": {
			visible: false;
		};
		"content-type-builder": {
			visible: false;
		};
	};
	attributes: {
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		description: Schema.Attribute.String;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::users-permissions.role"
		> &
			Schema.Attribute.Private;
		name: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 3;
			}>;
		permissions: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::users-permissions.permission"
		>;
		publishedAt: Schema.Attribute.DateTime;
		type: Schema.Attribute.String & Schema.Attribute.Unique;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		users: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::users-permissions.user"
		>;
	};
}

export interface PluginUsersPermissionsUser
	extends Struct.CollectionTypeSchema {
	collectionName: "up_users";
	info: {
		description: "";
		displayName: "User";
		name: "user";
		pluralName: "users";
		singularName: "user";
	};
	options: {
		draftAndPublish: false;
	};
	attributes: {
		blocked: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
		confirmationToken: Schema.Attribute.String & Schema.Attribute.Private;
		confirmed: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<false>;
		createdAt: Schema.Attribute.DateTime;
		createdBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		email: Schema.Attribute.Email &
			Schema.Attribute.Required &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 6;
			}>;
		locale: Schema.Attribute.String & Schema.Attribute.Private;
		localizations: Schema.Attribute.Relation<
			"oneToMany",
			"plugin::users-permissions.user"
		> &
			Schema.Attribute.Private;
		mandants: Schema.Attribute.Relation<"manyToMany", "api::mandant.mandant">;
		password: Schema.Attribute.Password &
			Schema.Attribute.Private &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 6;
			}>;
		provider: Schema.Attribute.String;
		publishedAt: Schema.Attribute.DateTime;
		resetPasswordToken: Schema.Attribute.String & Schema.Attribute.Private;
		role: Schema.Attribute.Relation<
			"manyToOne",
			"plugin::users-permissions.role"
		>;
		updatedAt: Schema.Attribute.DateTime;
		updatedBy: Schema.Attribute.Relation<"oneToOne", "admin::user"> &
			Schema.Attribute.Private;
		username: Schema.Attribute.String &
			Schema.Attribute.Required &
			Schema.Attribute.Unique &
			Schema.Attribute.SetMinMaxLength<{
				minLength: 3;
			}>;
	};
}

declare module "@strapi/strapi" {
	export namespace Public {
		export interface ContentTypeSchemas {
			"admin::api-token": AdminApiToken;
			"admin::api-token-permission": AdminApiTokenPermission;
			"admin::permission": AdminPermission;
			"admin::role": AdminRole;
			"admin::transfer-token": AdminTransferToken;
			"admin::transfer-token-permission": AdminTransferTokenPermission;
			"admin::user": AdminUser;
			"api::connector.connector": ApiConnectorConnector;
			"api::global-config.global-config": ApiGlobalConfigGlobalConfig;
			"api::invoice-template.invoice-template": ApiInvoiceTemplateInvoiceTemplate;
			"api::invoice.invoice": ApiInvoiceInvoice;
			"api::mandant.mandant": ApiMandantMandant;
			"api::ocpi-cdr.ocpi-cdr": ApiOcpiCdrOcpiCdr;
			"api::ocpi-command.ocpi-command": ApiOcpiCommandOcpiCommand;
			"api::ocpi-connection.ocpi-connection": ApiOcpiConnectionOcpiConnection;
			"api::ocpi-evse.ocpi-evse": ApiOcpiEvseOcpiEvse;
			"api::ocpi-location.ocpi-location": ApiOcpiLocationOcpiLocation;
			"api::ocpi-log.ocpi-log": ApiOcpiLogOcpiLog;
			"api::ocpi-session.ocpi-session": ApiOcpiSessionOcpiSession;
			"api::payment-session.payment-session": ApiPaymentSessionPaymentSession;
			"api::payter-connection.payter-connection": ApiPayterConnectionPayterConnection;
			"api::tariff.tariff": ApiTariffTariff;
			"api::terminal-message-log.terminal-message-log": ApiTerminalMessageLogTerminalMessageLog;
			"api::terminal.terminal": ApiTerminalTerminal;
			"plugin::content-releases.release": PluginContentReleasesRelease;
			"plugin::content-releases.release-action": PluginContentReleasesReleaseAction;
			"plugin::i18n.locale": PluginI18NLocale;
			"plugin::review-workflows.workflow": PluginReviewWorkflowsWorkflow;
			"plugin::review-workflows.workflow-stage": PluginReviewWorkflowsWorkflowStage;
			"plugin::upload.file": PluginUploadFile;
			"plugin::upload.folder": PluginUploadFolder;
			"plugin::users-permissions.permission": PluginUsersPermissionsPermission;
			"plugin::users-permissions.role": PluginUsersPermissionsRole;
			"plugin::users-permissions.user": PluginUsersPermissionsUser;
		}
	}
}
