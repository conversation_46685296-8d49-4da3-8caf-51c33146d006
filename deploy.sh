#!/bin/bash

# Log-Datei definieren
LOG_FILE="deploy.log"

# Funktion zum Loggen mit Zeitstempel
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "Deployment gestartet"

# NVM laden
log "Lade NVM..."
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

# Verwende eine bestimmte Node-Version
log "Wechsle zu Node 22..."
nvm install 22
nvm use 22 >> "$LOG_FILE" 2>&1 || { log "Fehler beim Wechseln zu Node 22"; exit 1; }

# Umgebungsvariablen kopieren
log "Kopiere Umgebungsvariablen..."
cp ../env/backend.env backend/.env
cp ../env/frontend.env frontend/.env

# Backend: Dependencies installieren, Admin bauen & neustarten
log "Starte Backend-Build..."
cd backend
log "Installiere Backend-Dependencies..."
npm ci >> "../$LOG_FILE" 2>&1 || { log "Fehler bei npm ci im Backend"; exit 1; }
log "Baue Backend..."
npm run build >> "../$LOG_FILE" 2>&1 || { log "Fehler beim Backend-Build"; exit 1; }

# Frontend: Dependencies installieren, bauen & neustarten
log "Starte Frontend-Build..."
cd ../frontend
log "Installiere Frontend-Dependencies..."
npm ci >> "../$LOG_FILE" 2>&1 || { log "Fehler bei npm ci im Frontend"; exit 1; }
log "Baue Frontend..."
npm run build >> "../$LOG_FILE" 2>&1 || { log "Fehler beim Frontend-Build"; exit 1; }

# Starte beide Anwendungen neu
log "Starte Anwendungen neu..."
cd ..
pm2 reload ecosystem.config.js --env production >> "$LOG_FILE" 2>&1 ||
  pm2 start ecosystem.config.js --env production >> "$LOG_FILE" 2>&1

log "Deployment abgeschlossen"
