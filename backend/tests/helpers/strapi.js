const strapiFactory = require("@strapi/strapi");
const fs = require("fs");
let instance;

async function setupStrapi() {
  if (!instance) {
    try {
      // Strapi-Instanz starten
      instance = await strapiFactory({ distDir: process.cwd() }).load();
      await instance.server.mount();
      console.log('Strapi instance started successfully');

      // Globale Strapi-Variable setzen
      global.strapi = instance;
    } catch (error) {
      console.error('Error starting Strapi:', error);
      throw error;
    }
  }
  return instance;
}

async function cleanupStrapi() {
  if (!instance) return;

  try {
    // Datenbank-Einstellungen abrufen
    const dbSettings = instance.config.get("database.connection");

    // Server schließen
    if (instance.server && instance.server.httpServer) {
      await instance.server.httpServer.close();
    }

    // Datenbankverbindung schließen
    if (instance.db && instance.db.connection) {
      await instance.db.connection.destroy();
    }

    // Testdatenbank löschen
    if (dbSettings && dbSettings.connection && dbSettings.connection.filename) {
      const tmpDbFile = dbSettings.connection.filename;
      if (fs.existsSync(tmpDbFile)) {
        fs.unlinkSync(tmpDbFile);
      }
    }

    // Instanz zurücksetzen
    instance = null;
    global.strapi = null;

    console.log('Strapi instance cleaned up successfully');
  } catch (error) {
    console.error('Error cleaning up Strapi:', error);
    throw error;
  }
}

module.exports = {
  setupStrapi,
  cleanupStrapi
};
