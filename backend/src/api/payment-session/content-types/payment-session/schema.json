{"kind": "collectionType", "collectionName": "payment_sessions", "info": {"singularName": "payment-session", "pluralName": "payment-sessions", "displayName": "PaymentSession", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"terminal": {"type": "relation", "relation": "manyToOne", "target": "api::terminal.terminal", "inversedBy": "payment_sessions"}, "paymentSessionState": {"type": "enumeration", "enum": ["started", "authorized", "captured", "canceled", "error"]}, "paymentIntent": {"type": "string"}, "history": {"type": "json", "default": "[]"}, "closedAt": {"type": "datetime"}, "ocppTransactionId": {"type": "string"}, "blockedAmount": {"type": "integer"}, "capturedAmount": {"type": "integer"}, "cardId": {"type": "string"}, "maskedPan": {"type": "string"}, "brand": {"type": "string"}, "merchantReference": {"type": "string"}, "authorizationCode": {"type": "string"}, "authorizationHostReference": {"type": "string"}, "authorizedAt": {"type": "datetime"}, "mandant": {"type": "relation", "relation": "manyToOne", "target": "api::mandant.mandant", "inversedBy": "payment_sessions"}, "ocpi_evse": {"type": "relation", "relation": "manyToOne", "target": "api::ocpi-evse.ocpi-evse", "inversedBy": "payment_sessions"}, "ocpi_cdr": {"type": "relation", "relation": "oneToOne", "target": "api::ocpi-cdr.ocpi-cdr", "inversedBy": "payment_session"}, "ocpi_session": {"type": "relation", "relation": "oneToOne", "target": "api::ocpi-session.ocpi-session", "inversedBy": "payment_session"}, "showPriceDateTime": {"type": "datetime"}, "invoice": {"type": "relation", "relation": "oneToOne", "target": "api::invoice.invoice", "inversedBy": "payment_session"}, "costumerEmailForInvoice": {"type": "email"}, "costumerEmailSendetAt": {"type": "datetime"}}}