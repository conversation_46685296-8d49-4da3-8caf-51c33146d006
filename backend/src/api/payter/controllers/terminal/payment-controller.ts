// backend/src/api/payter/controllers/terminal/payment-controller.ts
import { AxiosInstance } from 'axios';

/**
 * Payment Controller für Payter Terminals
 * Verwaltet die Bezahlvorgänge auf den Terminals
 */

/**
 * Startet den Kartenlesevorgang auf dem Terminal
 *
 * @param apiClient - Der API-Client
 * @param callbackUrl - Die Callback-URL
 * @param authorizedAmount - Der autorisierte Betrag in Cent
 * @returns Die Antwort der API
 */
export async function startCardReading(
    ctx,
  apiClient: AxiosInstance,
  callbackUrl: string,
  authorizedAmount: number
) {
  // Parameter für die URL erstellen
  const qr = strapi.config.get('server.frontendURL') + `/${ctx.state.terminal.mandant.name}/terminal/${ctx.state.terminal.documentId}`;

  const params = new URLSearchParams({
    authorizedAmount: authorizedAmount.toString(),
    callbackUrl: callbackUrl,
    uiMessage: `Es werden ${(authorizedAmount / 100).toFixed(2).replace('.', ',')} € reserviert`,
    customIdleScreen: 'false',
    language: ctx.state.terminal.currentLanguage,
    qr: qr,
    uiMessageTitle: 'Bitte Karte vorhalten'
  });

  const url = `/terminals/${ctx.state.terminal.serialNumber}/start?${params.toString()}`;
  console.log(`Starting card reading for terminal ${ctx.state.terminal.serialNumber} with URL: ${url}`);

  return await apiClient.post(url, {});
}

/**
 * Ruft die Karteninformationen vom Terminal ab
 *
 * @param apiClient - Der API-Client
 * @param terminalId - Die Terminal-ID
 * @returns Die Karteninformationen
 */
export async function getCardInfo(apiClient: AxiosInstance, terminalId: string) {
  const url = `/terminals/${terminalId}/card`;
  console.log(`Getting card info for terminal ${terminalId} with URL: ${url}`);

  try {
    // Sende die Anfrage an die Payter API
    const response = await apiClient.get(url);
    return response.data;
  } catch (error) {
    console.error(`Error getting card info for terminal ${terminalId}:`, error);
    throw error;
  }
}

/**
 * Autorisiert eine Zahlung auf dem Terminal
 *
 * @param apiClient - Der API-Client
 * @param terminalId - Die Terminal-ID
 * @param callbackUrl - Die Callback-URL
 * @returns Die Antwort der API
 */
export async function authorizePayment(apiClient: AxiosInstance, terminalId: string, callbackUrl: string) {
  // Berechne das Datum 3 Tage in der Zukunft
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + 10);

  // Formatiere das Datum als YYYY-MM-DD HH:MM:SS
  const formattedDate = futureDate.toISOString().slice(0, 10) + ' ' +
                       futureDate.toTimeString().slice(0, 8);

  const publicURL = strapi.config.get('server.publicURL');
  const authorizedCallbackUrl = `${publicURL}/api/payter/authorizedCallback`;


  // Parameter für die Autorisierung
  const params = new URLSearchParams({
    uiMessage: 'Vielen Dank für Ihre Zahlung, der Ladevorgang wird gestartet...',
    uiMessageTimeout: '15',
    receiptLine: 'Eulektro Ladevorgang',
    callbackUrl: authorizedCallbackUrl,
    merchantReference: `eulektro_${Date.now()}`,
    sessionActionDate: formattedDate,
    sessionAction: 'CANCEL'
  });

  const url = `/terminals/${terminalId}/authorize?${params.toString()}`;
  console.log(`Authorizing payment for terminal ${terminalId} with URL: ${url}`);

  try {
    // Sende die Anfrage an die Payter API mit leerem Objekt statt leerem String
    const response = await apiClient.post(url, {});
    return response.data;
  } catch (error) {
    console.error(`Error authorizing payment for terminal ${terminalId}:`, error);
    throw error;
  }
}

/**
 * Verarbeitet den POST Callback von Payter
 */
export function getCallback(ctx) {
  console.log("Received GET callback from Payter:", ctx.request.body);

  return ctx.send({
    status_code: 1000,
    status_message: "OK - Callback received"
  });
}
