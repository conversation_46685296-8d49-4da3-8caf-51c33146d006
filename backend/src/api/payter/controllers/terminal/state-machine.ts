// backend/src/api/payter/controllers/terminal/state-machine.ts
import screens from "../../config/payter-screens.json";
import { AxiosInstance } from 'axios';

import {uiCallback} from './ui-controller';
import {PayterErrorResponse, ResponseData} from "../../types/types";
import {authorizedCallback} from "./authorizedCallback";
import {displayScreen} from "./uiActions";


/**
 * Verarbeitet Status-Callbacks vom Payter-Terminal (z.B. Zustandsänderungen).
 */
export async function stateCallback(ctx) {
  console.log("Received state callback from Payter:", ctx.request.body);

  // Extrahiere relevante Daten aus dem Callback
  const callbackData = ctx.request.body || {};
  const terminalId = callbackData.serialNumber || callbackData.session?.serialNumber;
  const state = callbackData.state || callbackData.session?.state;

  console.log('Extracted state callback data:', { terminalId, state });

  if (!terminalId) {
    return ctx.send({
      status: 400,
      message: "Bad Request: Missing 'terminalId' in callback data."
    });
  }

  ctx.state.terminal = await strapi.documents('api::terminal.terminal').findFirst({
    filters: {
      serialNumber: terminalId,
    },
    populate: ['mandant']
  });


  try {
    return await processStateCallback(ctx, terminalId, state);
  } catch (error) {
    console.error(`Error processing state callback for terminal ${terminalId}:`, error);
    ctx.status = 500;

    // Erstelle die Fehlerantwort
    const errorResponse: PayterErrorResponse = {
      status: 500,
      message: `Error processing state callback for terminal ${terminalId}`,
      error: error.message || 'Unknown error',
      timestamp: new Date().toISOString()
    };

    return ctx.send(errorResponse);
  }
}

/**
 * Verarbeitet Callbacks vom Payter-Terminal (Legacy-Methode für Abwärtskompatibilität).
 */
export async function postCallback(ctx) {
  console.log("Received POST callback from Payter (legacy):", ctx.request.body);

  // Entscheide, ob es sich um einen UI- oder Status-Callback handelt
  const callbackData = ctx.request.body || {};

  if (callbackData.state) {
    // Wenn ein 'state'-Feld vorhanden ist, handelt es sich um einen Status-Callback
    return await stateCallback(ctx);
  } else if (callbackData.screenId || callbackData.response) {
    // Wenn ein 'screenId'- oder 'response'-Feld vorhanden ist, handelt es sich um einen UI-Callback
    return await uiCallback(ctx);
  } else {
    // Wenn keines der Felder vorhanden ist, können wir nicht entscheiden
    console.warn("Cannot determine callback type, using UI callback as default");
    return await uiCallback(ctx);
  }
}





/**
 * Implementiert die State Machine für Status-Callbacks (Zustandsänderungen)
 */
async function processStateCallback(ctx, terminalId: string, state: string) {
  // State Machine implementieren
  let nextScreen: string | null = null;
  let sessionId: string = ctx.request.body?.sessionId || `session_${Date.now()}`;

  // Stelle sicher, dass sessionId immer ein String ist
  if (typeof sessionId !== 'string') {
    sessionId = `session_${Date.now()}`;
  }
  const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

  console.log(`Current state: ${state}`);

  // State Machine für Zustandsänderungen
  switch (state) {
    case 'CARD':
      // Card = Karte wurde gelesen und kann im nächsten Schritt autorisiert werden
      console.log('Card read callback received:', ctx.request.body);

      const noTransitionResponse: ResponseData = {
        status_code: 1000,
        status_message: `OK - State callback processed for state ${state}, no state transition`,
        session_id: sessionId,
        next_screen: ""
      };

     return ctx.send(noTransitionResponse);

    case 'IDLE':
      // Terminal ist im IDLE-Zustand
      console.log('Terminal is in IDLE state');
      // Hier könnte man z.B. den Idle-Screen anzeigen
      nextScreen = 'screen-init';
      break;

    case 'READING':
      // Terminal ist im READING-Zustand
      console.log('Terminal is in READING state');
      // ToDo Push reading Info screen
      // Hier könnte man z.B. einen Hinweis anzeigen, dass das Terminal auf eine Karte wartet
      break;

    case 'STOPPING':
      // Terminal ist im STOPPING-Zustand
      console.log('Terminal is in STOPPING state');
      // Hier könnte man z.B. einen Hinweis anzeigen, dass der Vorgang abgebrochen wird
      break;

    case 'AUTHORIZING':
      // Terminal ist im AUTHORIZING-Zustand
      console.log('Terminal is in AUTHORIZING state');
      // Hier könnte man z.B. einen Hinweis anzeigen, dass die Zahlung autorisiert wird
      break;

    case 'AUTHORIZED':
      // Terminal ist im AUTHORIZED-Zustand
      console.log('Terminal is in AUTHORIZED state');
      // Rufe die authorizeCallback-Funktion auf, um eine PaymentSession zu erstellen
      return await authorizedCallback(ctx, terminalId, state);

    case 'ERROR':
      // Terminal ist im ERROR-Zustand
      console.log('Terminal is in ERROR state');
      // Hier könnte man z.B. einen Fehlerbildschirm anzeigen
      nextScreen = 'error';
      break;

    default:
      console.log(`Unknown terminal state: ${state}`);
  }

  // Nächsten Screen anzeigen, falls vorhanden
  if (nextScreen) {
    let response;

    // Je nach nächstem Screen die entsprechende API aufrufen
    switch (nextScreen) {
      case 'charging-started':
        // Hier den Screen mit der Session-ID anzeigen

        break;
      case 'screen-init':
        response = await displayScreen(ctx, apiClient, callback.ui, 'screen-init');
        break;
      case 'error':
        response = await displayScreen(ctx, apiClient, callback.ui, 'screen-error');
        break;
    }

    // Bereite die Antwort vor
    const nextScreenStr: string = nextScreen || '';

    const responseData: ResponseData = {
      status_code: 1000,
      status_message: `OK - Transitioned to ${nextScreenStr} state`,
      session_id: sessionId,
      next_screen: nextScreenStr
    };

    // Füge die Payment-Session-ID und Karteninformationen hinzu, falls vorhanden
    if (ctx.state.paymentSessionId) {
      responseData.payment_session_id = ctx.state.paymentSessionId;
    }

    if (ctx.state.cardInfo) {
      // Füge nur die wichtigsten Karteninformationen hinzu, um die Antwort nicht zu groß zu machen
      responseData.card_info = {
        maskedPan: ctx.state.cardInfo.maskedPan,
        cardId: ctx.state.cardInfo.cardId,
        brand: ctx.state.cardInfo.sessions?.[0]?.brand
      };
    }

    return ctx.send(responseData);
  }

  // Wenn kein nächster Screen definiert ist, einfach OK zurückgeben
  const noTransitionResponse: ResponseData = {
    status_code: 1000,
    status_message: `OK - State callback processed for state ${state}, no state transition`,
    session_id: sessionId,
    next_screen: ""
  };

  return ctx.send(noTransitionResponse);
}
