{"name": "backend", "version": "1.0.0", "private": true, "description": "Eulektro Terminal Verwaltung Backend", "scripts": {"build": "strapi build", "deploy": "strapi deploy", "develop": "node --inspect ./node_modules/@strapi/strapi/bin/strapi.js develop", "start": "strapi start", "strapi": "strapi", "types": "strapi ts:generate-types", "test": "jest --forceExit --detect<PERSON><PERSON>Handles"}, "dependencies": {"@strapi/admin": "^5.13.0", "@strapi/design-system": "^2.0.0-rc.23", "@strapi/icons": "^2.0.0-rc.23", "@strapi/plugin-cloud": "5.13.0", "@strapi/plugin-users-permissions": "5.13.0", "@strapi/strapi": "5.13.0", "better-sqlite3": "11.10.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.0", "styled-components": "^6.1.18", "zod": "^v4.0.0-beta", "pdfkit": "^0.17.1", "mysql2": "^3.14.1"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/supertest": "^6.0.3", "express": "^5.1.0", "jest": "^29.7.0", "msw": "^2.8.2", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}