{"kind": "collectionType", "collectionName": "ocpi_commands", "info": {"singularName": "ocpi-command", "pluralName": "ocpi-commands", "displayName": "OCPI Command"}, "options": {"draftAndPublish": true}, "attributes": {"commandId": {"type": "string", "unique": true, "required": true}, "type": {"type": "enumeration", "enum": ["start", "stop", "update"], "required": true}, "status": {"type": "enumeration", "enum": ["pending", "sent", "acknowledged", "failed"], "default": "pending", "required": true}, "payload": {"type": "json"}, "timestamp": {"type": "datetime", "required": true}}}