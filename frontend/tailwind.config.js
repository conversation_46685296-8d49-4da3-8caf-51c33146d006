/** @type {import('tailwindcss').Config} */
export default {
	content: ["./src/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			colors: {
				primary: "oklch(0.546 0.245 262.881)",
				"primary-light": "rgb(43, 135, 154)",
				"primary-dark": "rgb(23, 85, 104)",
				secondary: "rgba(236, 236, 231, 0.5)",
				"secondary-solid": "rgb(236, 236, 231)",
			},
			backgroundImage: {
				"gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
				"gradient-primary":
					"linear-gradient(135deg, rgb(33, 105, 124) 0%, rgb(43, 135, 154) 50%, rgb(23, 85, 104) 100%)",
			},
		},
	},
	plugins: [],
};
