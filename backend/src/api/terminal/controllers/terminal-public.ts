'use strict';

/**
 * <PERSON>ffentlicher Controller für Terminal-Daten
 * Bietet Endpunkte für den Zugriff auf Terminal-Daten ohne Authentifizierung
 */

module.exports = {
  /**
   * Gibt öffentliche Informationen zu einem Terminal zurück
   * @param {Object} ctx - Der Kontext des Requests
   * @returns {Object} Die öffentlichen Informationen zum Terminal
   */
  async getPublicQrData(ctx) {
    try {
      const { terminalId } = ctx.params;

      // Suche nach dem Terminal anhand der terminalId oder serialNumber
      const terminal = await strapi.documents('api::terminal.terminal').findOne({
        documentId: terminalId,
        populate: {
          // Nur die benötigten Felder für den Mandanten
          mandant: {
            fields: ['name'],
            populate: {
             logo: {
               fields: ['url', 'width', 'height']
             },
            }
          },
          // // Nur die benötigten Felder für die EVSEs
          evses: {
            fields: ['uid', 'evseId', 'ocpiStatus', 'labelForTerminal'],
            populate: {
              coordinates: true,
            },
          },
          // Nur die benötigten Felder für die Location
          location: {
            fields: ['name', 'address', 'city', 'postalCode', 'country'],
            populate: {
              coordinates: true,
            }
          }
        }
      });

      if (!terminal) {
        return ctx.notFound('Terminal nicht gefunden');
      }

      // Für jede EVSE die letzte aktive Payment-Session finden
      const evsePaymentSessions = {};

      // Für jede EVSE im Terminal
      if (terminal.evses && terminal.evses.length > 0) {
        for (const evse of terminal.evses) {
          // Finde die letzte aktive Payment-Session für diese EVSE
          const payment_sessions = await strapi.documents('api::payment-session.payment-session').findMany({
            filters: {
              ocpi_evse: {
                documentId: evse.documentId
              },
              paymentSessionState: {
                $in: ['authorized', 'started']
              },
              closedAt: null // Nur Sessions ohne closedAt
            },
            sort: { createdAt: 'desc' }, // Sortiere nach createdAt absteigend (neueste zuerst)
            limit: 1, // Nur die neueste Session
            populate: {
              ocpi_session: {
                fields: ['startTime', 'endTime', 'kwh', 'authorizationReference', 'currency', 'totalCost', 'ocpiStatus']
              }
            }
          });
          // Wenn eine Session gefunden wurde, speichere sie für diese EVSE
          if (payment_sessions && payment_sessions.length > 0) {
            evsePaymentSessions[evse.documentId] = payment_sessions[0];
          }
        }
      }

      // Füge die Payment-Sessions zu den EVSEs hinzu
      const result = { ...terminal };
      if (result.evses) {
        result.evses = result.evses.map(evse => {
          const session = evsePaymentSessions[evse.documentId];
          return {
            ...evse,
            currentPaymentSession: session ? {
              documentId: session.documentId,
              state: session.paymentSessionState,
              authorizedAt: session.authorizedAt || session.createdAt,
              blockedAmount: session.blockedAmount || 0,
              ocpi_session: session.ocpi_session
            } : null
          };
        });
      }

      return result;
    } catch (error) {
      strapi.log.error('Fehler beim Abrufen der öffentlichen Terminal-Daten:', error);
      return ctx.badRequest('Fehler beim Abrufen der Daten');
    }
  }
};
