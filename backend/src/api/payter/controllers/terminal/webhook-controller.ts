// backend/src/api/payter/controllers/terminal/webhook-controller.ts

/**
 * Webhook Controller für Payter Terminals
 * Verwaltet die Webhooks für Terminal-Status-Updates.
 */

/**
 * Registriert einen globalen Webhook bei Payter für Terminal-Status-Updates.
 * Diese Methode wird vom Frontend aufgerufen, um die globalen Webhooks zu registrieren.
 */
export async function putWebhook(ctx) {
  // Umgebung aus dem Request-Body oder Standard (test)
  const environment = ctx.request.body?.environment || 'test';

  // Status-Liste, die überwacht werden soll - immer die gleichen Status
  const states = ["IDLE", "READING", "CARD", "STOPPING", "AUTHORIZING"];

  try {

   const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClientByConnectionType(environment);

    if (!apiClient || !callback.state) {
      ctx.status = 500;
      return ctx.send({
        status_code: 500,
        status_message: "Failed to create API client."
      });
    }

    // Bereite die Anfrage vor
    const requestBody = {
      callbackUrl: callback.state,
      states: states
    };

    // Sende die Anfrage an die Payter API
    const response = await apiClient.put('/webhook', requestBody);

    console.log(`Payter API Webhook Registration Response (${environment}) - Status:`, response.status);
    return ctx.send({
      status_code: 1000,
      status_message: `OK - Payter Webhook Registration Successful for ${environment} environment`,
      payter_response_status: response.status,
      payter_response_data: response.data,
      webhook_url: callback.state
    });
  } catch (error) {
    console.error(`Error registering webhook with Payter API (${environment}):`, error);
    if (error.response) {
      // Spezifische Fehlerbehandlung basierend auf der Payter-API-Antwort
      const status = error.response.status;
      const errorData = error.response.data;

      ctx.status = status;
      return ctx.send({
        status_code: status,
        status_message: errorData.message || `Error registering webhook with Payter for ${environment} environment`,
        error: errorData.error || 'Unknown error',
        timestamp: errorData.timestamp || new Date().toISOString(),
        error_details: errorData
      });
    } else if (error.request) {
      ctx.status = 504;
      return ctx.send({
        status_code: 504,
        status_message: `No response received from Payter API when registering webhook for ${environment} environment`
      });
    } else {
      ctx.status = 500;
      return ctx.send({
        status_code: 500,
        status_message: `Internal error setting up Payter webhook registration request for ${environment} environment`,
        error_details: error.message
      });
    }
  }
}

/**
 * Empfängt Callback-Daten von der Payter API und verarbeitet den Transaktionsstatus.
 */
export function postPayter(ctx) {
  console.log("Received callback from Payter:", ctx.request.body);
  // Hier kannst du den Callback verarbeiten, z. B. die PaymentSession updaten,
  // den Transaktionsstatus speichern oder weitere Logik ausführen.
  return ctx.send({
    status_code: 1000,
    status_message: "OK - Callback received"
  });
}
