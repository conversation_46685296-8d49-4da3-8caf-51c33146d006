module.exports = {
    apps: [
        {
            name: 'etv-backend',
            cwd: './backend',
            script: "npm",
            args: 'start',
            node_args: '--no-warnings',
            env: {
                NODE_ENV: 'development',
                PORT: '1337',
                STRAPI_DISABLE_SOURCE_MAPS: 'true',
            },
            env_production: {
                NODE_ENV: 'production',
                PORT: '1337',
                STRAPI_DISABLE_SOURCE_MAPS: 'true',
            },
            instances: 1,
            exec_mode: 'fork',
            watch: false,
            autorestart: true,
            max_memory_restart: '250M',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            combine_logs: true
        },
        {
            name: 'etv-frontend',
            script: "npm",
            args: "run start",
            cwd: './frontend',
            env: {
                NODE_ENV: 'development',
                PORT: 3000
            },
            env_production: {
                NODE_ENV: 'production',
                PORT: 3000
            },
            instances: 1,
            exec_mode: 'fork',
            watch: false,
            max_memory_restart: '250M',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            combine_logs: true
        }
    ],

    deploy: {
        production: {
            user: 'eulektro',
            host: 'terminal.eulektro.de',
            ref: 'origin/master',
            repo: 'ssh://************************:30001/eulektro/etv.git',
            path: '/home/<USER>/terminal.eulektro.de',
            "pre-setup": `echo "Setting up deployment directories"`,
            "post-setup": `echo "Setup complete"`,
            'pre-deploy-local': '',
            'post-deploy': "./deploy.sh"
        }
    }
};
