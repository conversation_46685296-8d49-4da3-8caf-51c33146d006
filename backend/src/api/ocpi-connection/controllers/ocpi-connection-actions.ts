// src/api/ocpi-connection/controllers/ocpi-connection-actions.ts
'use strict';

/**
 * OCPI Connection Actions Controller
 * Enthält Endpunkte für benutzerdefinierte Aktionen im Strapi-Admin
 */
module.exports = {
    /**
     * POST /ocpi-connections/:id/connect
     * Startet einen OCPI-Handshake für die angegebene Verbindung
     */
    async connect(ctx) {
        try {
            const { id } = ctx.params;

            if (!id) {
                return ctx.badRequest('ID ist erforderlich');
            }

            // Mandant aus dem Kontext holen, falls vorhanden
            const mandantId = ctx.state?.user?.mandant?.id || null;

            // Handshake über den Service ausführen
            const handshakeService = strapi.service('api::ocpi-handshake.ocpi-handshake');
            const result = await handshakeService.performHandshake(id, true, mandantId); // true = ID verwenden

            if (result.success) {
                return ctx.send({
                    data: result,
                    message: 'OCPI-Verbindung hergestellt'
                });
            } else {
                return ctx.badRequest(result.status_message, { details: result });
            }
        } catch (error) {
            return ctx.badRequest(`Fehler beim Verbinden: ${error.message}`);
        }
    },

    /**
     * POST /ocpi-connections/:id/disconnect
     * Trennt eine OCPI-Verbindung
     */
    async disconnect(ctx) {
        try {
            const { id } = ctx.params;

            if (!id) {
                return ctx.badRequest('ID ist erforderlich');
            }

            // Mandant aus dem Kontext holen, falls vorhanden
            const mandantId = ctx.state?.user?.mandant?.id || null;

            // Disconnect über den Service ausführen
            const handshakeService = strapi.service('api::ocpi-handshake.ocpi-handshake');
            const result = await handshakeService.disconnectConnection(id, true, mandantId); // true = ID verwenden

            if (result.success) {
                return ctx.send({
                    data: result,
                    message: 'OCPI-Verbindung getrennt'
                });
            } else {
                return ctx.badRequest(result.status_message, { details: result });
            }
        } catch (error) {
            return ctx.badRequest(`Fehler beim Trennen: ${error.message}`);
        }
    }
};
